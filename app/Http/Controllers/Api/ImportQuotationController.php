<?php

namespace App\Http\Controllers\Api;

use App\Services\ImportQuotationService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;

class ImportQuotationController extends Controller
{

  /**
   * Store a newly created resource in storage.
   *
   * @param Request $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function store(Request $request)
  {
    try {
      dispatch(new \App\Jobs\ImportQuotations($request->all()));

      return response()
        ->json(
          [
            'message' => 'Import started successfully',
            'status' => true
          ],
          Response::HTTP_CREATED
        );
    } catch (\Exception $e) {

      return response()
        ->json(
          ['error' => $e->getMessage()],
          Response::HTTP_INTERNAL_SERVER_ERROR
        );
    }
  }

  public function view($num)
  {
    return view('source.quotations'. $num);
  }
}
