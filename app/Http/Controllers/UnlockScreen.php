<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;

class UnlockScreen extends Controller
{
  /**
   * Handle the incoming request.
   *
   * @param \Illuminate\Http\Request $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function __invoke(Request $request)
  {
    if (!Hash::check($request->password, $request->user()->password)) {
      if (!$request->user()->screen_locked) {
        $request->user()->update([
          'screen_locked' => true
        ]);
      }

      return response()->json([
        'message' => 'User is not authorized'
      ], Response::HTTP_UNAUTHORIZED);
    }

    $request->user()->update([
      'screen_locked' => false
    ]);

    return response()->json([], Response::HTTP_OK);
  }
}
