<?php

namespace App\Http\Livewire\PurchaseOrders;

use App\Services\ListService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Livewire\Component;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Entities\PurchaseOrderItem;
use Modules\General\Entities\PurchaseOrderRequest;
use Modules\Inventory\Entities\Item;

class ShowPurchaseOrderItemComponent extends Component
{
  public $poItemId;
  public $poItemLineNumber;
  public $poItemQuantity;
  public $poItemQuantityReceived;
  public $poItemQuantityBackOrdered;
  public $poItemPartNumber = '';
  public $poItemManufacturerPartNumber;
  public $poItemOrderedFor;
  public $poItemUnit;
  public $poItemDescription;
  public $poItemPrice;
  public $poItemAmount;
  public $lineNumber;
  public $isReceived = false;
  /**
   * @var bool|mixed
   */
  public $poItemDeleted = false;

  public function mount(array $purchaseOrderItem, $isReceived = false)
  {
    $this->fill([
      'poItemId' => $purchaseOrderItem['id'],
      'poItemQuantity' => $purchaseOrderItem['quantity'],
      'poItemQuantityReceived' => $purchaseOrderItem['quantity_received'],
      'poItemQuantityBackOrdered' => $purchaseOrderItem['quantity'] - $purchaseOrderItem['quantity_received'],
      'poItemPartNumber' => $purchaseOrderItem['part_number'],
      'poItemManufacturerPartNumber' => $purchaseOrderItem['manufacturer_part_number'],
      'poItemOrderedFor' => $purchaseOrderItem['ordered_for'],
      'poItemPrice' => $purchaseOrderItem['price'],
      'poItemDescription' => $purchaseOrderItem['description'],
      'poItemUnit' => $purchaseOrderItem['unit'],
      'poItemLineNumber' => $purchaseOrderItem['line_number'],
      'isReceived' => $isReceived,
      'poItemAmount' => $purchaseOrderItem['actual_amount'],
    ]);
  }

  public function render()
  {
    return view('livewire.purchase-orders.partials.show-item');
  }

  public function updatingPoItemQuantity($value)
  {
    $this->poItemQuantity = empty($value) ? 0 : $value;
  }

  public function updatedPoItemQuantityReceived($value)
  {
    $this->poItemQuantityBackOrdered = $this->poItemQuantity - $value;
    $this->poItemAmount = (empty($value) ? 0 : $value) * $this->poItemPrice;
  }

  public function updatedPoItemPrice($value)
  {
    $this->poItemAmount = (empty($value) ? 0 : $value) * $this->poItemQuantity;
  }

  public function updated()
  {
    $this->saveItem();
  }

  protected function saveItem()
  {
    $this->validate([
      'poItemQuantity' => 'required|numeric',
      'poItemPrice' => 'required|numeric'
    ]);

    return PurchaseOrderItem::whereId($this->poItemId)
      ->update($this->getPurchaseOrderItemDetails());
  }

  public function deletePurchaseOrderItem(): bool
  {
    try {
      $this->emitUp('poItemDeleted', $this->poItemId);
      PurchaseOrderItem::find($this->poItemId)->delete();
      $this->poItemDeleted = true;
      return true;
    } catch (\Exception $e) {
      $this->poItemDeleted = false;
      return false;
    }
  }

  protected function getItemAmount(array $purchaseOrderItem)
  {
    if ($this->isReceived) {
      return $purchaseOrderItem['quantity_received'] * $purchaseOrderItem['price'];
    }

    return $purchaseOrderItem['quantity'] * $purchaseOrderItem['price'];
  }

  /**
   * @return array
   */
  protected function getPurchaseOrderItemDetails(): array
  {
    return [
      'line_number' => $this->poItemLineNumber,
      'quantity' => $this->poItemQuantity,
      'quantity_received' => $this->poItemQuantityReceived,
      'part_number' => $this->poItemPartNumber,
      'manufacturer_part_number' => $this->poItemManufacturerPartNumber,
      'unit' => $this->poItemUnit,
      'ordered_for' => $this->poItemOrderedFor,
      'description' => $this->poItemDescription,
      'price' => $this->poItemPrice,
    ];
  }
}
