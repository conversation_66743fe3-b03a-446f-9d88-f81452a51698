<?php

namespace App\Http\Livewire\PurchaseOrders;

use App\Services\ListService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Livewire\Component;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Entities\PurchaseOrderItem;
use Modules\General\Entities\PurchaseOrderRequest;
use Modules\Inventory\Entities\Item;

class ShowPurchaseOrderComponent extends Component
{
  public $purchaseOrder;
  public $nextPurchaseOrderNumber;
  public array $purchaseOrderItems = [];
  public array $shipViaOptions = [];
  public $itemQuantity;
  public string $itemPartNumber = '';
  public $shippingDate = '';
  public $shippingDateNotes = '';
  public $shippingVia = '';
  public $shippingAddress = "Pacific Water Inc.\n200 W. Haven Ave.\nSalt Lake City, UT 84115";
  public $vendorAddress;
  public $requiredBy = 'A.S.A.P'; // TODO: Change to required_at to mean date or point in time.
  public bool $purchaseOrderUpdated = false;
  public $customItemQuantity;
  public $customItemPartNumber = '';
  public $customItemManufacturerPartNumber = '';
  public $customItemUnit = '';
  public $customItemOrderedFor = '';
  public $customItemDescription = '';
  public $customItemPrice = 0;
  public $itemLineNumber;

  protected array $propertiesToReset = [
    'itemLineNumber',
    'itemPartNumber',
    'itemQuantity',
    'customItemQuantity',
    'customItemPartNumber',
    'customItemManufacturerPartNumber',
    'customItemUnit',
    'customItemOrderedFor',
    'customItemDescription',
    'customItemPrice'
  ];

  protected function getListeners()
  {
    return [
      'poItemDeleted' => 'removePurchaseOrderItem'
    ];
  }

  public function mount(PurchaseOrder $purchaseOrder)
  {
    $this->purchaseOrder = $purchaseOrder;
    $this->requiredBy = $purchaseOrder->required_by;
    $this->shipViaOptions = settings('global.vendor_shipping_modes.value', []);
    $this->shippingVia = $purchaseOrder->ship_via;
    $this->shippingDate = $purchaseOrder->shipping_date;
    $this->shippingDateNotes = $purchaseOrder->shipping_notes;
    $this->shippingAddress = Str::startsWith($purchaseOrder->shipping_address, '200 W. Haven Ave') ? $this->shippingAddress : $purchaseOrder->shipping_address;
    $this->vendorAddress = $this->getVendorAddress($purchaseOrder);
    $this->purchaseOrderItems = $this->formatPurchaseOrderItems($purchaseOrder->purchaseOrderItems);
  }

  public function render()
  {
    return view('livewire.purchase-orders.show');
  }

  /**
   * Format purchase order items for presentation to the UI
   *
   * @param Collection $items
   * @return array
   */
  public function formatPurchaseOrderItems(Collection $items): array
  {
    return $items->map(function($purchaseItem) {
      return $purchaseItem->toArray();
    })->all();
  }

  /**
   * @return false
   */
  public function addItem(): bool
  {
    $this->validate([
      'itemPartNumber' => 'required|string|max:50',
      'itemQuantity' => 'required|numeric|min:0',
    ]);

    $item = $this->findItem();

    if (empty($item)) {
      // TODO: Popup some message on the UI showing that item was not found.
      return false;
    }

    $itemAsArray = $item->toArray();
    $itemAsArray['part_number'] = $item->code;
    $itemAsArray['quantity'] = $this->itemQuantity;
    $itemAsArray['ordered_for'] = '';
    $itemAsArray['line_number'] = count($this->purchaseOrderItems) + 1;
    $itemAsArray['manufacturer_part_number'] = $item->manufacturer_part_number;
    $this->purchaseOrderItems[$item->code] = $this->addToPurchaseOrder($itemAsArray);

    $this->resetForm();

    return true;
  }

  public function addCustomItem()
  {
    $this->validate([
      'customItemPartNumber' => 'string|max:50',
      'customItemQuantity' => 'numeric|min:0',
    ]);

    $customItem = [
      'quantity' => $this->customItemQuantity,
      'part_number' => $this->customItemPartNumber,
      'manufacturer_part_number' => $this->customItemManufacturerPartNumber,
      'ordered_for' => $this->customItemOrderedFor,
      'unit' => $this->customItemUnit,
      'description' => $this->customItemDescription,
      'price' => $this->customItemPrice,
      'amount' => round($this->customItemPrice * $this->customItemQuantity, 2),
      'line_number' => count($this->purchaseOrderItems) + 1,
    ];

    $this->purchaseOrderItems[$this->customItemPartNumber] = $this->addToPurchaseOrder($customItem);

    $this->resetForm();
  }

  protected function findItem()
  {
    if (empty($this->itemPartNumber)) {
      return null;
    }

    return Item::whereCode($this->itemPartNumber)->first();
  }

  protected function resetForm()
  {
    $this->reset($this->propertiesToReset);
  }

  public function removePurchaseOrderItem($itemId)
  {
    $this->purchaseOrderItems = Arr::where($this->purchaseOrderItems, function ($item) use ($itemId) {
      return $item['id'] !== $itemId;
    });
  }

  public function updatePurchaseOrder()
  {
    $this->validate([
      'requiredBy' => 'required|string|max:50',
      'shippingVia' => 'required|string|max:50',
      'shippingDate' => 'nullable|date',
      'shippingDateNotes' => 'nullable|string|max:255'
    ]);

    $this->purchaseOrderUpdated = $this->purchaseOrder->update([
      'required_by' => $this->requiredBy,
      'ship_via' => $this->shippingVia,
      'shipping_date' => $this->shippingDate,
      'shipping_notes' => $this->shippingDateNotes,
      'shipping_address' => $this->shippingAddress,
      'vendor_address' => $this->vendorAddress
    ]);

    session()->flash('success', 'Purchase Order successfully updated.');
    Cache::forget('items_already_ordered'); // TODO: Use the update event for this code
  }

  protected function getVendorAddress(PurchaseOrder $purchaseOrder)
  {

    if (! empty($purchaseOrder->vendor_address)) {
      return $purchaseOrder->vendor_address;
    } elseif (! empty($purchaseOrder->vendor)) {
      return $purchaseOrder->vendor->name . "\n" . optional($purchaseOrder->vendor->accountAddress)->full_address;
    }

    return null;
  }

  protected function addToPurchaseOrder(array $item)
  {
    $poItem = PurchaseOrderItem::create([
      'purchase_order_id' => $this->purchaseOrder->id,
      'part_number' => $item['part_number'],
      'manufacturer_part_number' => Arr::get($item, 'manufacturer_part_number'),
      'quantity' => $item['quantity'],
      'unit' => $item['unit'],
      'description' => $item['description'],
      'price' => $item['price'],
      'ordered_for' => $item['ordered_for'],
      'line_number' => $item['line_number']
    ]);

    Cache::forget('items_already_ordered');

    return $poItem->fresh()->toArray();
  }
}
