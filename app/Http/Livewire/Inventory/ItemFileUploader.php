<?php

namespace App\Http\Livewire\Inventory;

use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithFileUploads;
use Modules\Inventory\Entities\Item;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\Support\RemoteFile;

class ItemFileUploader extends Component
{
  use WithFileUploads;

  public $itemFile;

  public $item;

  public $attachments = [];

  public bool $uploading = false;

  public array $uploadedFiles = [];

  public string|int $itemId = '';

  public function mount(string|int $itemId)
  {
    $this->item = Item::find($this->itemId);
    $this->attachments = $this->getFileAttachments();
  }

  public function render()
  {
    return view('livewire.inventory.item-file-uploader');
  }

  public function uploadFiles()
  {
    $this->validate([
      'itemFile' => ['required', 'file', 'max:512000'],
      'itemId' => ['required', 'exists:items,id'],
    ]);
    $this->uploading = true;
    $disk = $this->getDiskName();

    $this->item->addMedia($this->itemFile->getRealPath())
      ->setFile(new RemoteFile($this->itemFile->getRealPath(), $disk))
      ->usingFileName($this->itemFile->getClientOriginalName())
      ->toMediaCollection('attachments', $disk);

//    $this->attachments = $this->getFileAttachments();
//    $this->reset('files');


    session()->flash('success', 'Files uploaded successfully.');

    //return true;
    return redirect()->route('inventory.items.show', $this->item);
  }

  public function deleteAttachment($attachmentId): bool
  {
    $media = Media::query()->find($attachmentId);

    if (! $media) {
      return false;
    }

    $media->delete();

    $this->item = $this->item->refresh();
    $this->attachments = $this->getFileAttachments();

    return true;
  }

  protected function getFileAttachments()
  {
    if (empty($this->item)) {
      return [];
    }

    return $this->item->attachments->map(function (Media $attachment) {
      return [
        'id' => $attachment->id,
        'url' => $attachment->getFullUrl(),
        'file_name' => $attachment->file_name,
      ];
    });
  }

  protected function getDiskName()
  {
    return config('media-library.disk_name');
  }
}
