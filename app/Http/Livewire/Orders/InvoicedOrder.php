<?php

namespace App\Http\Livewire\Orders;

use Livewire\Component;

class InvoicedOrder extends Component
{
  public bool $isHidden = false;
  public bool $forEmail = false;
  public bool $forPrint = false;

  public $invoice;

  public function mount($invoice)
  {
    $this->invoice = $invoice;
    $this->forPrint = empty($invoice['email_invoices']);
    $this->forEmail = ! $this->forPrint;
  }

  public function render()
  {
    return view('livewire.orders.invoiced-order');
  }
}
