<?php

namespace App\Http\Livewire\Reports;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Component;
use Modules\Orders\Entities\Payment;

class AnnualMonthlySales extends Component
{
  public string $startDate;
  public string $endDate;
  /**
   * @var mixed|string
   */
  public $formattedStartDate;
  /**
   * @var mixed|string
   */
  public $formattedEndDate;

  public string $selectedYear = '';
  /**
   * @var mixed
   */
  public $minYear;
  public $maxYear;

  public function mount()
  {
    $this->minYear = $this->getMinYear();
    $this->maxYear = now()->year;
    $this->selectedYear = $this->maxYear;
  }

  public function render()
  {
    return view('livewire.reports.annual-monthly-sales', [
      'monthlySales' => $this->getMonthlySales(),
    ]);
  }

  protected function getMonthlySales(): \Illuminate\Support\Collection
  {
    return DB::table('orders')
      ->selectRaw('
        date_format(invoiced_at, "%b %y") as month,
        date_format(invoiced_at, "%m") as month_digit,
        sum(total_amount) as month_total
      ')
      ->where('invoiced', true)
      ->whereYear('invoiced_at', $this->selectedYear)
      ->groupBy(['month_digit', 'month'])
      ->orderBy('month_digit')
      ->get();
  }

  protected function getMinYear(): int
  {
    $firstInvoiceDate = DB::table('orders')
      ->select('invoiced_at')
      ->min('invoiced_at');

    return Carbon::createFromDate($firstInvoiceDate)->year;
  }
}
