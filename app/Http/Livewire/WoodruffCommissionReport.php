<?php

namespace App\Http\Livewire;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Modules\Orders\Entities\Commission;
use Modules\Orders\Entities\Order;

class WoodruffCommissionReport extends Component
{
  use HasCommissions;

  public $startDate;
  public $endDate;
  public $printLink;
  public $printButtonDisabled = false;
  public $commissionDiscount = 0;
  public $isStandardReport = true;
  public $showCustomEntryForm = false;
  public $customOrderFullNumber;
  public $customQuantity = 1;
  public $customDescription;
  public $customPartNumber;
  public $customPrice = 0;
  public $customAmount = 0;
  public $customCommission;
  public $customInvoiceNumber;
  public $customFinalDate;
  public $customPurchaseOrderNumber;
  public $customOrderDate;
  public $routeSuffix = 'woodruff.index';
  public $commissionType;

  public function mount()
  {
    $this->startDate = $this->getStartDate();
    $this->endDate = $this->getEndDate();
    $this->commissionDiscount = settings('global.commissions.types.ws.value', 0);
    $this->isStandardReport = (bool) request()->standard;
    $this->commissionType = $this->isStandardReport ? Commission::TYPE_WS : Commission::TYPE_WS_CS;
    $this->printLink = route('orderModule.reports.commissions.woodruff.print', [
      'start_date' => $this->startDate,
      'end_date' => $this->endDate,
      'standard' => $this->isStandardReport
    ]);
  }

  public function render()
  {
    return view('livewire.reports.commissions-report-woodruff', [
      'commissionGroups' => $this->getCommissions(),
      'customCommissions' => $this->getCustomCommissions()
    ]);
  }

  public function updatedCustomQuantity($value)
  {
    $this->customQuantity = floatval($value);
    $this->customAmount = round(floatval($value) * floatval($this->customPrice), 2);
  }

  public function updatedCustomPrice($value)
  {
    $this->customPrice = floatval($value);
    $this->customAmount = round(floatval($this->customQuantity) * floatval($value), 2);
  }

  public function updatedCustomCommission($value)
  {
    $this->customCommission = floatval($value);
  }

  public function updatedStartDate()
  {
    $this->printLink = route('orderModule.reports.commissions.woodruff.print', [
      'start_date' => $this->startDate,
      'end_date' => $this->endDate,
      'standard' => $this->isStandardReport
    ]);
  }

  public function updatedEndDate()
  {
    $this->printLink = route('orderModule.reports.commissions.woodruff.print', [
      'start_date' => $this->startDate,
      'end_date' => $this->endDate,
      'standard' => $this->isStandardReport
    ]);
  }

  protected function generatePrintLink()
  {
    $this->printLInk = route('orderModule.reports.commissions.woodruff.print', [
      'start_date' => $this->startDate,
      'end_date' => $this->endDate,
      'standard' => $this->isStandardReport
    ]);
  }
}
