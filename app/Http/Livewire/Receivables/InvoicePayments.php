<?php

namespace App\Http\Livewire\Receivables;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Livewire\Component;
use Livewire\WithPagination;
use Modules\Orders\Entities\Receivable;

class InvoicePayments extends Component
{
  use WithPagination;

  const OPERATOR_BETWEEN = 'BETWEEN';
  const OPERATOR_NOT_BETWEEN = 'NOT BETWEEN';
  const OPERATOR_IS_NULL = 'IS NULL';
  const OPERATOR_IS_NOT_NULL = 'IS NOT NULL';

  protected string $paginationTheme = 'bootstrap';
  public bool $readyToLoad = false;
  public int|string $minAge = 0;
  public int $skip = 0;
  public int $limit = 50;
  public array $searchParams = [];
  public string $searchTerm = '';
  protected array $filters = [];
  public string $sortBy = 'record_timestamp';
  public string $sortOrder = 'asc';


  protected $listeners = [
    'searching' => 'updateSearchParams',
    'searchFilterRemoved' => 'removeSearchParams',
    'searchFiltersCleared' => 'resetSearchParams'
  ];
  /**
   * @var \Illuminate\Support\Collection|mixed
   */
  public $summary;

  public function render()
  {
    return view('livewire.receivables.index', [
      'receivables' => $this->getReceivables(),
      'searchConditionOptions' => $this->conditions()
    ]);
  }

  public function updatingMinAge(): void
  {
    $this->resetPage();
  }

  public function loadReceivables(): void
  {
    $this->readyToLoad = true;
  }

  protected function getReceivables()
  {
    if (! $this->readyToLoad) {
      $receivables = collect([]);
      $paginatedRecords = $receivables->forPage($this->page, $this->limit);

      return new LengthAwarePaginator($paginatedRecords, $receivables->count(), $this->limit, $this->page);
    }

    $query = Receivable::query();

    if ($this->minAge == 0 && empty($this->searchParams) && empty($this->searchTerm)) {
      $this->summary = $this->getReceivableSummary($query);

      return $query->orderBy($this->sortBy, $this->sortOrder)->paginate($this->limit);
    }

    if ($this->minAge > 0) {
      $query = $query->where('age', '>', $this->minAge);
    }

    if (! empty($this->searchTerm)) {
      $this->summary = $this->getReceivableSummary($query);
      $this->resetPage();
      return $this->searchReceivables($query);
    }

    if (empty($this->searchParams)) {
      $this->summary = $this->getReceivableSummary($query);

      return $query
        ->skip($this->skip)
        ->limit($this->limit)
        ->orderBy($this->sortBy, $this->sortOrder)
        ->paginate($this->limit);
    }

    // User is performing an advanced search.
    // Clear the simple search and use the advanced search only
    $this->searchTerm = '';

    $this->getFilters()
      ->each(function ($condition, $field) use (&$query) {
        $query = $this->buildQuery($query, $condition, $field);
      });

    $this->summary = $this->getReceivableSummary($query);

    return $query
      ->orderBy($this->sortBy, $this->sortOrder)
      ->paginate(50);
  }

  protected function getReceivableSummary(Builder $query): array
  {
    return [
      'total_balance' => number_format($query->sum('balance'), 2),
      'total_debit' => number_format($query->sum('debit_amount'), 2),
      'total_credit' => number_format($query->sum('credit_amount'), 2)
    ];
  }

  public function nextPage(): void
  {
    $this->skip += $this->limit;
  }

  public function previousPage(): void
  {
    if ($this->skip - $this->limit >= 0) {
      $this->skip -= $this->limit;
    } else {
      $this->skip = 0;
    }
  }

  public function updateSearchParams($params): void
  {
    $this->searchParams = $params;
  }

  public function removeSearchParams($index): bool
  {
    if (empty($this->searchParams)) {
      return false;
    }

    unset($this->searchParams['terms'][$index]);
    unset($this->searchParams['fields'][$index]);
    unset($this->searchParams['conditions'][$index]);

    return true;
  }

  public function resetSearchParams(): void
  {
    $this->searchParams = [];
  }

  protected function getFilters(): \Illuminate\Support\Collection
  {
    return collect($this->searchParams['fields'])
      ->mapWithKeys(function ($field, $key) {
        return [
          $field => Arr::get(
            $this->conditions($this->searchParams['terms'][$key]),
            $this->searchParams['conditions'][$key],
            []
          )
        ];
      });
  }

  protected function buildQuery(Builder $query, $condition, $field = null): Builder
  {
    $value = $condition['value'];
    $operator = $condition['operator'];

    if ($operator === self::OPERATOR_BETWEEN) {
      $pieces = explode(' ', $value);
      return $query->whereBetween($field, [$pieces[0], $pieces[2]]);
    }

    if ($operator === self::OPERATOR_NOT_BETWEEN) {
      $pieces = explode(' ', $value);
      return $query->whereNotBetween($field, [$pieces[0], $pieces[2]]);
    }

    if ($operator === self::OPERATOR_IS_NULL) {
      return $query->whereNull($field);
    }

    if ($operator === self::OPERATOR_IS_NOT_NULL) {
      return $query->whereNotNull($field);
    }

    return $query->where($field, $operator, $value);
  }

  protected function conditions($term = ''): array
  {
    return [
      'like' => [
        'label' => 'Contains',
        'operator' => 'LIKE',
        'value' => "%{$term}%"
      ],
      'not_like' => [
        'label' => 'Does Not Contain',
        'operator' => 'NOT LIKE',
        'value' => "%{$term}%"
      ],
      'starts_with' => [
        'label' => 'Begins With',
        'operator' => 'LIKE',
        'value' => "{$term}%"
      ],
      'ends_with' => [
        'label' => 'Ends With',
        'operator' => 'LIKE',
        'value' => "%{$term}"
      ],
      'equal' => [
        'label' => 'Is Equal To',
        'operator' => '=',
        'value' => $term
      ],
      'not_equal' => [
        'label' => 'Is Not Equal To',
        'operator' => '<>',
        'value' => $term
      ],
      'less_than' => [
        'label' => 'Is Less Than',
        'operator' => '<',
        'value' => $term
      ],
      'greater_than' => [
        'label' => 'Is Greater Than',
        'operator' => '>',
        'value' => $term
      ],
      'less_than_equal' => [
        'label' => 'Is Less Than or Equal To',
        'operator' => '<=',
        'value' => $term
      ],
      'greater_than_equal' => [
        'label' => 'Is Greater Than or Equal To',
        'operator' => '>=',
        'value' => $term
      ],
      'between' => [
        'label' => 'Is Between',
        'operator' => 'BETWEEN',
        'value' => $term
      ],
      'not_between' => [
        'label' => 'Is Not Between',
        'operator' => 'NOT BETWEEN',
        'value' => $term
      ],
      'empty' => [
        'label' => 'Is Empty',
        'operator' => 'IS NULL',
        'value' => $term
      ],
      'not_empty' => [
        'label' => 'Is Not Empty',
        'operator' => 'IS NOT NULL',
        'value' => $term
      ]
    ];
  }

  protected function searchReceivables(Builder $query)
  {
    $value = "%{$this->searchTerm}%";

    $query = $query
      ->where('account_number', 'LIKE', $value)
      ->orWhere('customer_name', 'LIKE', $value)
      ->orWhere('purchase_order_number', 'LIKE', $value)
      ->orWhere('reference_number', 'LIKE', $value)
      ->orWhere('balance', 'LIKE', $value)
      ->orWhere('record_date', 'LIKE', $value)
      ->orWhere('debit_amount', 'LIKE', $value)
      ->orWhere('credit_amount', 'LIKE', $value)
      ->orWhere('payment_method', 'LIKE', $value)
      ->orWhere('payment_reference', 'LIKE', $value)
      ->orWhere('payment_log', 'LIKE', $value)
      ->orWhere('payment_terms', 'LIKE', $value)
      ->orWhere('notes', 'LIKE', $value);

    $this->summary = $this->getReceivableSummary($query);

    return $query->orderBy($this->sortBy, $this->sortOrder)->paginate(50);
  }

  public function sortBy(string $column)
  {
    $this->sortOrder = $this->sortOrder === 'desc' ? 'asc' : 'desc';
    $this->sortBy = $column;
  }
}
