<?php

namespace App\Http\Livewire\Users\Customers;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Component;
use Livewire\WithPagination;
use Modules\Users\Entities\Customer;

class CustomerReceivables extends Component
{
  use WithPagination;
  protected string $paginationTheme = 'bootstrap';
  public Customer $customer;
  public bool $readyToLoad = false;
  public int $perPage = 10;
  public $balance = 0;
  public $creditAmount = 0;
  public $debitAmount = 0;
  public $amountPaid = 0;

  public string $search = '';

  public function render(): \Illuminate\View\View
  {
    return view('livewire.users.customers.customer-receivables', [
      'receivables' => $this->readyToLoad ? $this->getRecords() : collect([])
    ]);
  }

  public function updatingSearch(): void
  {
    $this->resetPage();
  }

  public function loadReceivables(): void
  {
    $this->readyToLoad = true;
  }

  public function getRecords(): \Illuminate\Contracts\Pagination\LengthAwarePaginator
  {
    $query = $this->customer->pendingReceivables()
      ->select('record_timestamp', 'record_id', 'reference_number', 'balance', 'record_date', 'debit_amount', 'credit_amount', 'payment_method', 'payment_log', 'age', 'notes', 'type', 'paid');

    if (! empty($this->search)) {
      $query->where(function (Builder $query) {
        $query->where('reference_number', 'like', "%$this->search%")
          ->orWhere('record_date', 'like', "%$this->search%")
          ->orWhere('debit_amount', 'like', "%$this->search%")
          ->orWhere('credit_amount', 'like', "%$this->search%")
          ->orWhere('amount_paid', 'like', "%$this->search%");
      });
    }

    $receivables = $query->get();

    $this->balance = number_format($receivables->sum('balance'), 2);
    $this->creditAmount = number_format($receivables->sum('credit_amount'), 2);
    $this->debitAmount = number_format($receivables->sum('debit_amount'), 2);
    $this->amountPaid = number_format($receivables->sum('amount_paid'), 2);

    $paginatedRecords = $receivables->forPage($this->page, $this->perPage);

    return new LengthAwarePaginator($paginatedRecords, $receivables->count(), $this->perPage, $this->page);

  }
}
