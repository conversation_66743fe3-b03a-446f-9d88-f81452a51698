<?php

namespace App\Http\Livewire;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class EmailLog extends Component
{
  use WithPagination;

  protected string $paginationTheme = 'bootstrap';

  public string $startDate;
  public string $endDate;
  public string $searchTerm = '';

  public function mount()
  {
    $currentDate = $this->getCurrentDateString();
    $this->startDate = $currentDate;
    $this->endDate = $currentDate;
  }

  public function render()
  {
    return view('livewire.email-log', [
      'emailLogs' => $this->getEmailLogs()
    ]);
  }

  protected function getCurrentDateString(): string
  {
    return now()->toDateString();
  }

  /**
   * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
   */
  protected function getEmailLogs()
  {
    $query = DB::table('email_logs')
      ->select('type', 'email', 'recipient', 'subject')
      ->addSelect(DB::raw('DATE_FORMAT(created_at, "%m/%d/%y") as formatted_created_at'))
      ->whereBetween('created_at', [
        $this->startDate,
        \Carbon\Carbon::parse($this->endDate)->endOfDay()->toDateTimeString()
      ]);

    if (! empty($this->searchTerm)) {
      $query = $query->where(function (Builder $query) {
        $query->where('email', 'LIKE', "%{$this->searchTerm}%")
          ->orWhere('recipient', 'LIKE', "%{$this->searchTerm}%")
          ->orWhere('subject', 'LIKE', "%{$this->searchTerm}%");
      });
    }

    return $query->latest()->paginate(25);
  }
}
