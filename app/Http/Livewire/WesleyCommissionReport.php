<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Modules\Orders\Entities\Commission;
use Modules\Orders\Entities\Order;

class WesleyCommissionReport extends Component
{
  use HasCommissions;

  public $startDate;
  public $endDate;
  public $printLink;
  public $printButtonDisabled = false;
  public $showCustomEntryForm = false;
  public $customQuantity = 1;
  public $customDescription;
  public $customPartNumber;
  public $customPrice = 0;
  public $customAmount = 0;
  public $customCommission;
  public $customInvoiceNumber;
  public $customFinalDate;
  public $customPurchaseOrderNumber;
  public $customOrderDate;
  public $routeSuffix = 'wes.index';
  public $commissionType = Commission::TYPE_WES;

  public function mount()
  {
    $this->startDate = $this->getStartDate();
    $this->endDate = $this->getEndDate();
    $this->printLink = route('orderModule.reports.commissions.wes.print', [
      'start_date' => $this->startDate,
      'end_date' => $this->endDate
    ]);
  }

  public function render()
  {
    return view('livewire.reports.commissions-report-wes', [
      'commissionGroups' => $this->getCommissions(),
      'customCommissions' => $this->getCustomCommissions(),
    ]);
  }

  public function updatedCustomQuantity($value)
  {
    $this->customQuantity = floatval($value);
    $this->customAmount = round(floatval($value) * floatval($this->customPrice), 2);
  }

  public function updatedCustomPrice($value)
  {
    $this->customPrice = floatval($value);
    $this->customAmount = round(floatval($this->customQuantity) * floatval($value), 2);
  }

  public function updatedCustomCommission($value)
  {
    $this->customCommission = floatval($value);
  }

  public function updatedStartDate()
  {
    $this->printLink = route('orderModule.reports.commissions.wes.print', [
      'start_date' => $this->startDate,
      'end_date' => $this->endDate
    ]);
  }

  public function updatedEndDate()
  {
    $this->printLink = route('orderModule.reports.commissions.wes.print', [
      'start_date' => $this->startDate,
      'end_date' => $this->endDate
    ]);
  }

  protected function generatePrintLink()
  {
    $this->printLInk = route('orderModule.reports.commissions.wes.print', [
      'start_date' => $this->startDate,
      'end_date' => $this->endDate
    ]);
  }
}
