<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class ActivityLog extends Model
{
  protected $casts = [
    'data' => 'array'
  ];

  protected $guarded = [];

  public function loggable()
  {
    return $this->morphTo('loggable');
  }

  public function getFormattedDateAttribute()
  {
    return $this->created_at->format('m/d/y h:i');
  }

  public function getOrderIdAttribute()
  {
    return Arr::get($this->data, 'order_id');
  }
}
