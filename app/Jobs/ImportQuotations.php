<?php

namespace App\Jobs;

use App\Services\ImportInvoiceService;
use App\Services\ImportQuotationService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ImportQuotations implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
  protected $importData;
  /**
   * Create a new job instance.
   *
   * @param array $importData
   */
  public function __construct(array $importData)
  {
    $this->importData = $importData;
  }

  /**
   * Execute the job.
   *
   * @param ImportQuotationService $importService
   * @return void
   */
  public function handle(ImportQuotationService $importService)
  {
    $importService->import($this->importData);
  }
}
