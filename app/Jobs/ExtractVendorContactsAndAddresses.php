<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\General\Entities\Provider;

class ExtractVendorContactsAndAddresses implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

  /**
   * Create a new job instance.
   *
   * @return void
   */
  public function __construct()
  {
    //
  }

  /**
   * Execute the job.
   *
   * @return void
   */
  public function handle()
  {
    // FYI: This is already included in the main import for vendors.
    $vendors = Provider::all();

    $vendors->each(function (Provider $vendor) {
      $addresses = $vendor->getOption('addresses', []);
      $vendor->addresses()->createMany($addresses);
      $contacts = $vendor->getOption('contacts', []);
      $vendor->contacts()->createMany($contacts);
    });
  }
}
