<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Str;
use Modules\Inventory\Entities\Bom;
use Modules\Inventory\Entities\Item;

class ImportItemDetails implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

  /**
   * Create a new job instance.
   *
   * @return void
   */
  public function __construct()
  {
    //
  }

  /**
   * Execute the job.
   *
   * @return void
   */
  public function handle()
  {
    // FYI: This has been included in the main import for inventory.

    $items = Item::all();
    $items->each(function (Item $item) {
      // Get BOM details

      $this->importBom($item);
      $this->importBuildInstructions($item);
    });
  }

  protected function importBom(Item $item)
  {
    $bom = $item->getOption('bom');

    if (empty($bom)) {
      return true;
    }

    if (Str::contains($bom, "\r\n")) {
      $bomAsArray = explode("\r\n", $bom);
    } else {
      $bomAsArray = explode("\n", $bom);
    }

    collect($bomAsArray)
      ->each(function ($bomItem) use ($item) {
        $partNumber = Str::after($bomItem, '/');
        $partQuantity = Str::before($bomItem, '/');
        $inventoryItem = Item::where('code', $partNumber)->first();

        if (empty($inventoryItem)) {
          return true;
        }

        $currentDateTime = now();
        Bom::updateOrInsert(
          [
            'item_id' => $inventoryItem->id,
            'part_id' => $item->id,
          ],
          [
            'quantity' => round($partQuantity, 2),
            'created_at' => $currentDateTime,
            'updated_at' => $currentDateTime
          ]
        );
      });
  }

  protected function importBuildInstructions(Item $item)
  {
    if (empty($item->build_notes)) {
      return true;
    }

    return $item->update(['build_description' => $item->build_notes->body]);
  }
}
