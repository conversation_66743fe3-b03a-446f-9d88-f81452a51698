<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Modules\General\Entities\Category;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Policies\CategoryPolicy;
use Modules\General\Policies\PurchaseOrderPolicy;
use Modules\General\Policies\ServiceProviderPolicy;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Policies\ItemPolicy;
use Modules\Orders\Entities\Order;
use Modules\Orders\Policies\OrderPolicy;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\Preference;
use Modules\Users\Entities\Role;
use Modules\Users\Entities\Staff;
use Modules\Users\Policies\CustomerPolicy;
use Modules\Users\Policies\PreferencePolicy;
use Modules\Users\Policies\RolePolicy;
use Modules\Users\Policies\StaffPolicy;

class AuthServiceProvider extends ServiceProvider
{
  /**
   * The policy mappings for the application.
   *
   * @var array
   */
  protected $policies = [
    // General
    Category::class => CategoryPolicy::class,
    Provider::class => ServiceProviderPolicy::class,
    PurchaseOrder::class => PurchaseOrderPolicy::class,
    // Inventory
    Item::class => ItemPolicy::class,

    // Users
    Customer::class => CustomerPolicy::class,
    Role::class => RolePolicy::class,
    Preference::class => PreferencePolicy::class,
    Staff::class => StaffPolicy::class,

    // Order Manager
    Order::class => OrderPolicy::class,
  ];

  /**
   * Register any authentication / authorization services.
   *
   * @return void
   */
  public function boot()
  {
    $this->registerPolicies();

    // Implicitly grant "Super Admin" role all permissions
    Gate::before(function ($user, $ability) {
      return $user->isAdmin() ? true : null;
    });
  }
}
