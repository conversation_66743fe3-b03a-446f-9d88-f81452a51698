<?php

namespace App\Providers;

use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\Notification;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Modules\Orders\Entities\Order;

class ViewServiceProvider extends ServiceProvider
{
  /**
   * Register services.
   *
   * @return void
   */
  public function register()
  {
    //
  }

  /**
   * Bootstrap services.
   *
   * @return void
   */
  public function boot()
  {
    View::composer('layouts.partials.sidebar', function (\Illuminate\View\View $view) {
      $notificationCounts = DatabaseNotification::all()->groupBy(function ($notification) {
        return $notification->data['type'];
      })->map(function ($notificationType) {
        return count($notificationType);
      });

      $view->with([
        'notificationCounts' => $notificationCounts
      ]);
    });

    View::composer([
      'orders::orders.show',
      'orders::quotations.show',
      'users::customers.orders.create',
      'users::customers.quotations.create',
      'orders::quotations.duplicates.create',
      'orders::orders.duplicates.create'
    ], function (\Illuminate\View\View $view) {

      if (in_array($view->name(), [
        'users::customers.orders.create',
        'users::customers.quotations.create',
        'orders::quotations.duplicates.create'])) {
        $inputDisabled = '';
      } else {
        $inputDisabled = !request()->user()->can('update order') ? 'disabled' : '';
      }

      $view->with([
        'inputDisabled' => $inputDisabled
      ]);
    });

    View::composer(['users::customers.edit', 'users::customers.create'], function (\Illuminate\View\View $view) {
      if (in_array($view->name(), ['users::customers.create'])) {
        $inputDisabled = '';
      } else {
        $inputDisabled = !request()->user()->can('update customer') ? 'disabled' : '';
      }

      $view->with([
        'inputDisabled' => $inputDisabled
      ]);
    });
  }
}
