<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Modules\General\Entities\Category;
use Modules\Inventory\Entities\Item;
use Modules\Orders\Actions\Orders\GetCategorySalesAction;
use Modules\Orders\Actions\Orders\UpdateCategorySalesAction;
use Modules\Orders\Entities\CategorySale;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;

class UpdateCategorySales extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:update-category-sales';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update category sales table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(UpdateCategorySalesAction $action)
    {
      $this->info('---------Start on Orders---------');
      $this->info('');

      $bar = $this->output->createProgressBar(Order::count());

      $bar->start();

      $orders = Order::cursor();

      foreach($orders as $order) {
        $action->execute($order);

        $bar->advance();
      }

      $bar->finish();

      $this->info('');
      $this->info('--------Done--------');

      return 1;
    }
}
