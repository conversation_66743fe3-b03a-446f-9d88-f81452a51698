<?php

namespace App\Console\Commands;

use App\Services\ImportInvoiceService;
use App\Services\ImportPurchaseOrderService;
use Illuminate\Console\Command;

class UpdatePurchaseOrdersReceiverDetails extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'update:purchase-order-receiver';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Update purchase order receiver details';

  protected ImportPurchaseOrderService $importService;
  /**
   * Create a new command instance.
   *
   * @return void
   */
  public function __construct(ImportPurchaseOrderService $service)
  {
    parent::__construct();
    $this->importService = $service;
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    $this->importService->dataType = 'old_purchase_orders';
    $allOrderData = $this->importService->getAllData();

    $this->importData($allOrderData);
  }

  protected function importData($dataToImport)
  {
    $importBar = $this->output->createProgressBar(count($dataToImport));

    $importBar->start();

    try {
      foreach ($dataToImport as $importRow) {

        $this->importService->updateReceiverDetails($importRow->data);

        $importBar->advance();
      }

      $importBar->finish();

      $this->info(' ');
      $this->info('Done!');
    } catch (\Exception $e) {
      $this->error($e->getMessage());
    }
  }
}
