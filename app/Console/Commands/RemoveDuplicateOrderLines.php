<?php

namespace App\Console\Commands;

use App\Traits\UpdatesImportedOrderAmounts;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;

class RemoveDuplicateOrderLines extends Command
{
    use UpdatesImportedOrderAmounts;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:remove-duplicate-order-lines';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove duplicate order lines';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Order::query()
          ->with('items')
          ->where('id', '<=', 11594)
          ->where('id', '>=', 11550)
          ->get()
          ->each(function (Order $order) {
            $itemsToKeep = $order->items->unique('item_id')->modelKeys();

            $order->items()->whereNotIn('id', $itemsToKeep)->delete();

            $this->updateOrderAmounts($order->fresh(), $order->taxed, $order->tax_amount);
          });

        return 1;
    }
}
