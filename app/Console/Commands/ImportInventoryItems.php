<?php

namespace App\Console\Commands;

use App\Services\ImportInventoryFromDatabase;
use Illuminate\Console\Command;
use Modules\Inventory\Entities\InventoryImport;
use Modules\Inventory\Entities\Item;

class ImportInventoryItems extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'import:inventory';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Import items into the inventory_imports table';

  /**
   * Execute the console command.
   *
   * @return int
   */
  public function handle(): int
  {
    return $this->importData();
  }

  protected function importData(): int
  {

    $importBar = $this->output->createProgressBar(Item::query()->count());

    $importBar->start();

    try {
      foreach (Item::all() as $item) {

        InventoryImport::query()->updateOrCreate(
          [
            'item_id' => $item->id
          ],
          [
            'item_id' => $item->id,
            'category_id' => $item->category_id
          ]
        );

        $importBar->advance();
      }

      $importBar->finish();

      $this->info(' ');
      $this->info('Done!');

      return 1;
    } catch (\Exception $e) {
      $this->error($e->getMessage());

      return 0;
    }
  }
}
