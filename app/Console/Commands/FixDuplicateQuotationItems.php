<?php

namespace App\Console\Commands;

use App\Services\FixDuplicatedQuoteService;
use App\Services\ImportInvoiceService;
use App\Services\ImportQuoteService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixDuplicateQuotationItems extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'import:fix-duplicate-quotation-items';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Fix duplicate quotation items.';

  protected FixDuplicatedQuoteService $importService;
  /**
   * Create a new command instance.
   *
   * @return void
   */
  public function __construct(FixDuplicatedQuoteService $service)
  {
    parent::__construct();
    $this->importService = $service;
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    $this->importData($this->importService->getAllData());
  }

  protected function importData($dataToImport)
  {
    $this->info('Fixing duplicated quotation items data');
    $importCount = count($dataToImport);
    $importBar = $this->output->createProgressBar($importCount);
    $importBar->start();

    try {
      foreach ($dataToImport as $importKey => $import) {
        $rawQuotations = collect($import->data)
          ->reject(function ($row) {
            return empty($row['DATE']);
          })
          ->groupBy('ID');
        $groupBar = $this->output->createProgressBar($rawQuotations->count());
        $groupBar->start();

        foreach($rawQuotations as $rawQuotation) {
          try {
            $this->importService->createRecord($rawQuotation);
          } catch (\Exception $e) {
            $this->importService->badData[] = [
              'exception' => $e->getMessage(),
              'records' => $rawQuotation->all()
            ];
          }

          $groupBar->advance();
        }

        $groupBar->finish();
        $this->info('');
        $this->info('Imported ' . ($importKey + 1) . ' of ' . $importCount);

        $importBar->advance();
      }

      $importBar->finish();

      $this->info(' ');
      $this->info('Done!');

      $this->info('------------------------------');
      $this->info('Bad Data not Imported: (' . count($this->importService->badData) . '). See errors below:');

      foreach($this->importService->badData as $badData) {
        $this->alert($badData['exception']);
      }

    } catch (\Exception $e) {
      $this->error($e->getMessage());
    }
  }
}
