<?php

namespace App\Console\Commands;

use App\Services\ImportMissingInvoiceService;
use Illuminate\Console\Command;

class ImportMissingOrders extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'import:missing-orders';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Import missing orders into the database';

  protected ImportMissingInvoiceService $importOrdersService;
  /**
   * Create a new command instance.
   *
   * @return void
   */
  public function __construct(ImportMissingInvoiceService $invoiceService)
  {
    parent::__construct();
    $this->importOrdersService = $invoiceService;
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    $this->importData($this->importOrdersService->getAllData());
  }

  protected function importData($import)
  {
    $this->info('Importing missing invoice data');

    $orderGroups = collect($import->data)
      ->reject(function ($row) {
        return empty($row['DATE']);
      })
      ->groupBy('ORDER#')
      ->all();

    $importBar = $this->output->createProgressBar(count($orderGroups));

    $importBar->start();

    try {

      foreach ($orderGroups as $invoiceGroup) {

        $this->importOrdersService->createRecord($invoiceGroup);

        $importBar->advance();
      }

      $importBar->finish();

      $this->info(' ');
      $this->info('Done!');
    } catch (\Exception $e) {
      $this->error($e->getMessage());
    }
  }
}
