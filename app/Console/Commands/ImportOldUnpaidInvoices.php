<?php

namespace App\Console\Commands;

use App\Models\Import;
use App\Services\ImportInvoiceService;
use Illuminate\Console\Command;

class ImportOldUnpaidInvoices extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'import:unpaid-invoices';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Import unpaid invoices into the database';

  protected ImportInvoiceService $importOrdersService;
  /**
   * Create a new command instance.
   *
   * @return void
   */
  public function __construct(ImportInvoiceService $invoiceService)
  {
    parent::__construct();
    $this->importOrdersService = $invoiceService;
  }

  /**
   * Execute the console command.
   *
   * @return int
   */
  public function handle(): int
  {
    $this->importData(Import::where('data_type', ['unpaid_invoices'])->get());

    return 1;
  }

  protected function importData($dataToImport)
  {
    $this->info('Importing old unpaid invoice data');

    $importBar = $this->output->createProgressBar(count($dataToImport));

    $importBar->start();

    try {
      foreach ($dataToImport as $importRow) {

        $this->importOrdersService->createItems($importRow->data);

        $importBar->advance();
      }

      $importBar->finish();

      $this->info(' ');
      $this->info('Done!');
    } catch (\Exception $e) {
      $this->error($e->getMessage());
    }
  }
}
