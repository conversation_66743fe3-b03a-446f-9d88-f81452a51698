<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class BatchCustomerStatementsGenerated implements ShouldBroadcast
{

    public $broadcastQueue = 'broadcasts';
    public $filename;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(string $filename)
    {
        $this->filename = $filename;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new Channel('staff.notifications');
    }

    public function broadcastWith()
    {
        return [
            'message' => 'Batch print for customer statements have been completed',
            'filename' => $this->filename,
        ];
    }
}
