<?php


namespace App\Services;

use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Inventory\Entities\Item;
use Modules\Orders\Entities\Order;
use Modules\Users\Entities\Customer;
use Money\Money;

class ImportQuotationService
{
  use WithFaker;

  /**
   * Import customers and vendors
   *
   * @param array $sourceData
   */
  public function import(array $sourceData)
  {
    // We don't need audit records here.
    Order::disableAuditing();

    $this->createItems($sourceData);

    Order::enableAuditing();
  }

  /**
   * @param array $rawData
   */
  protected function createItems(array $rawData)
  {
    collect($rawData)->groupBy('QUOTE#')
      ->each(function ($orderRecords) {
        $this->createRecord($orderRecords);
      });
  }

  /**
   * @param Collection $rawOrderDetails
   * @return bool
   */
  protected function createRecord(Collection $rawOrderDetails): bool
  {
    $orderData = $rawOrderDetails->first();
    $customer = Customer::select('user_id')->where('account_number', $orderData['CUSTCODE'])->first();

    if (empty($customer)) {
      \Log::debug($rawOrderDetails);
      return false;
    }

    $orderDetails = [
      'type' => 'quotation',
      'created_at' => Carbon::parse($orderData['DATE'])->toDateTimeString(),
      'created_by' => 3, // Pacific Test Account
      'number' => (new Order)->nextOrderNumber(),
      'full_number' => $orderData['QUOTE#'],
      'customer_id' => $customer->user_id,
      'purchase_order_number' => $orderData['P.O.#'],
      'shop_notes' => $orderData['SHOP NOTES'],
      'private_notes' => $orderData['PRIVATENOTES'],
      'shipping_name' => $orderData['SHIP COMPANY'],
      'shipping_address' => $orderData['SHIP ADDRESS'],
      'shipping_phone' => $orderData['SHIP PHONE'],
      'salesperson' => $orderData['SALESPERSON'],
      'quotation_expires_at' => Carbon::parse($orderData['EXPIRATION'])->toDateString(),
      'imported' => true,
      'payment_terms' => $orderData['TERMS'],
      'quotation_lead_time' => $orderData['LEAD TIME'],
      'options' => [
        'taxable' => empty($orderData['TAXABLE']),
        'quoted' => $orderData['QUOTED'],
        'statuses' => [
          'created' => [
            'name' => 'created',
            'created_at' => now()->format('m-d-y H:i:s'),
            'created_by' => auth()->id(),
            'previous' => null,
            'note' => ''
          ]
        ]
      ]
    ];

    $taxLineItem = null;
    $shipViaItem = null;
    $itemAmount = Money::USD(0);

    $orderLineItems = $rawOrderDetails->sortBy('LINE')
      ->reject(function ($orderLine) use (&$shipViaItem, &$taxLineItem) {
        if (Str::contains($orderLine['PRODUCT'], 'Sales Tax')) {
          $taxLineItem = $orderLine;
          return true;
        } else if (Str::contains($orderLine['PRODUCT'], 'Ship Via')) {
          $shipViaItem = $orderLine;
          return true;
        }

        return empty($orderLine['CUSTCODE']) && (int) $orderLine['QTY'] === 0;
      })
      ->map(function ($orderLine, $lineKey) use (&$itemAmount) {
        $inventoryItem = Item::where('code', $orderLine['PART#'])->first();

        $itemAmount = $itemAmount->add(Money::USD(str_money($orderLine['TOTAL'])));

        $record = [
          'quantity' => $orderLine['QTY'],
          'code' => $orderLine['PART#'],
          'description' => $orderLine['PRODUCT'],
          'price' => $orderLine['PRICE'],
          'unit' => $orderLine['UNIT'],
          'cost' => $orderLine['COST'],
          'build_instructions' => $orderLine['NOTES'],
          'location' => $orderLine['LOCATION'],
          'weight' => $orderLine['WEIGHT']
        ];

        if (! empty($inventoryItem)) {
          $record['item_id'] = $inventoryItem->id;
        } else {
          $record['item_id'] = 'custom_item_' . $lineKey;
          $record['type'] = 'custom';
        }

        return $record;
      })->all();

    if (! empty($shipViaItem)) {
      $orderDetails['shipping_via'] = Str::after($shipViaItem['PRODUCT'], 'Ship Via ');
      $orderDetails['shipping_cost'] = $shipViaItem['TOTAL'];
    }

    if (! is_null($taxLineItem)) {
      $orderDetails['tax_amount'] = $taxLineItem['TOTAL'];
      $orderDetails['taxed'] = true;
      $orderDetails['quotation_add_tax'] = true;
    }

    $orderDetails['amount'] = $itemAmount->getAmount() / 100;
    $orderDetails['total_amount'] = $itemAmount->add(Money::USD(str_money($orderDetails['tax_amount'] ?? 0)))
        ->add(Money::USD(str_money($orderDetails['shipping_cost'] ?? 0)))
        ->getAmount() / 100;

    $order = $customer->orders()->create($orderDetails);
    $order->items()->createMany($orderLineItems);

    return true;
  }

  protected function addOutOfStateDetails(array $record, $orderData): array
  {
    $shippingAddress = $orderData['SHIP ADDRESS'];
    $billingAddress = $orderData['BILLING ADDRESS'];

    if (! empty($shippingAddress)) {
      return $record;
    }

    $shippingOutOfState = ! Str::contains($shippingAddress, [' Utah ', ' UT ', ' utah ', ' UTAH ']);
    $billingOutOfState = ! Str::contains($billingAddress, [' Utah ', ' UT ', ' utah ', ' UTAH ']);

    if ($shippingOutOfState && $billingOutOfState) {
      $record['out_of_state'] = true;

      return $record;
    }

    return $record;
  }
}
