<?php


namespace App\Services;

use App\Models\Import;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\General\Entities\Category;
use Modules\General\Entities\Provider;
use Modules\Inventory\Actions\UpdateItemPricingAction;
use Modules\Inventory\Actions\UpdateItemProductPricingAction;
use Modules\Inventory\Entities\Bom;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Entities\Level;
use Modules\Inventory\Entities\Note;
use Modules\Inventory\Services\PriceCalculator;
use Modules\Users\Entities\Address;
use Modules\Users\Entities\Contact;
use Modules\Users\Entities\Customer;

class ImportInventoryFromDatabase
{
  use WithFaker;

  /**
   * @return mixed
   */
  public function getAllData()
  {
    return Import::where('data_type', 'inventory')->get();
  }

  /**
   * @param array $record
   * @return mixed
   */
  public function createItem(array $record)
  {
    $item = Item::updateOrCreate(
      [
        'code' => $record['code']
      ],
      Arr::except($record, ['notes', 'vendor'])
    );

    $item->notes()->delete();
    $item->notes()->createMany($record['notes']);

    if (!empty($record['vendor'])) {
      $item->vendors()->sync($record['vendor']);
    }

    $this->importBom($item);
    $this->importBuildInstructions($item);

    app(UpdateItemPricingAction::class)->execute($item);
    app(UpdateItemProductPricingAction::class)->execute($item);

    return $item;
  }

  public function importBom(Item $item): bool
  {
    $bom = $item->getOption('bom');

    if (empty($bom)) {
      return true;
    }

    if (Str::contains($bom, "\r\n")) {
      $bomAsArray = explode("\r\n", $bom);
    } else {
      $bomAsArray = explode("\n", $bom);
    }

    collect($bomAsArray)
      ->each(function ($bomItem) use ($item) {
        $partNumber = Str::after($bomItem, '/');
        $partQuantity = Str::before($bomItem, '/');
        $inventoryItem = Item::where('code', $partNumber)->first();

        if (empty($inventoryItem)) {
          return true;
        }

        $currentDateTime = now();
        Bom::updateOrInsert(
          [
            'item_id' => $inventoryItem->id,
            'part_id' => $item->id,
          ],
          [
            'quantity' => round($partQuantity, 2),
            'created_at' => $currentDateTime,
            'updated_at' => $currentDateTime
          ]
        );

        return true;
      });

    return true;
  }

  public function importBuildInstructions(Item $item): bool
  {
    if (empty($item->build_notes)) {
      return true;
    }

    return $item->update(['build_description' => $item->build_notes->body]);
  }

  public function syncItemCategories($source): Collection
  {
    return collect($source)
      ->unique('Category')
      ->reject(function ($row) {
        /**
         * Unique method above will leave at least one blank category field
         * We also don't want to bother with a category without a part number
         * Lastly, we are rejecting categories name containing any alpha-numerals i.e only punctuation characters..
         */
        return empty($row['Category']) || empty($row['OurPart#']) || ctype_punct($row['Category']);
      })
      ->mapWithKeys(function ($row, $key) {
        return [
          $key => [
            'Category' => $row['Category']
          ]
        ];
      })
      ->each(function ($row) {
        Category::updateOrCreate(
          [
            'name' => $row['Category'],
          ],
          [
            'type' => 'item'
          ]
        );
      });
  }

  public function dumpLevelCategories()
  {
    DB::table('category_level')->truncate();
    DB::unprepared("
      INSERT INTO `category_level` (`level_id`, `category_id`, `discount`, `pricing_method`)
      VALUES
        (2300, 1, NULL, 'list_price'),
        (2300, 2, NULL, 'list_price'),
        (2300, 3, 0.52, 'list_price'),
        (2300, 4, 0.52, 'list_price'),
        (2300, 5, 0.58, 'list_price'),
        (2300, 6, 0.54, 'list_price'),
        (2300, 7, 0.54, 'list_price'),
        (2300, 8, 0.54, 'list_price'),
        (2300, 9, 0.55, 'list_price'),
        (2300, 10, 0.55, 'list_price'),
        (2300, 11, 0.56, 'list_price'),
        (2300, 12, 0.54, 'list_price'),
        (2300, 13, 0.54, 'list_price'),
        (2300, 14, 0.54, 'list_price'),
        (2300, 15, 0.56, 'list_price'),
        (2300, 16, 0.54, 'list_price'),
        (2300, 17, 0.54, 'list_price'),
        (2300, 18, NULL, 'list_price'),
        (2300, 19, 0.54, 'list_price'),
        (2300, 20, 0.52, 'list_price'),
        (2300, 21, 0.54, 'list_price'),
        (2300, 22, 0.54, 'list_price'),
        (2300, 23, NULL, 'list_price'),
        (2300, 24, 0.54, 'list_price'),
        (2300, 25, 0.52, 'list_price'),
        (2300, 26, 0.54, 'list_price'),
        (2300, 27, 0.54, 'list_price'),
        (2300, 28, 0.54, 'list_price'),
        (2300, 29, 0.54, 'list_price'),
        (2300, 30, 0.54, 'list_price'),
        (2300, 31, 0.54, 'list_price'),
        (2300, 32, 0.54, 'list_price'),
        (2300, 33, 0.54, 'list_price'),
        (2300, 34, 0.54, 'list_price'),
        (2300, 35, 0.54, 'list_price'),
        (2300, 36, 0.54, 'list_price'),
        (2300, 37, 0.54, 'list_price'),
        (2300, 38, 0.54, 'list_price'),
        (2300, 39, 0.54, 'list_price'),
        (2300, 40, 0.54, 'list_price'),
        (2300, 41, 0.54, 'list_price'),
        (2300, 42, 0.54, 'list_price'),
        (2300, 43, NULL, 'list_price'),
        (2300, 44, 0.50, 'list_price'),
        (2300, 45, 0.52, 'list_price'),
        (2300, 46, 0.56, 'list_price'),
        (2300, 47, 0.54, 'list_price'),
        (2300, 48, 0.54, 'list_price'),
        (2300, 49, 0.52, 'list_price'),
        (2300, 50, 0.54, 'list_price'),
        (2300, 51, 0.54, 'list_price'),
        (2300, 52, 0.54, 'list_price'),
        (2300, 53, 0.52, 'list_price'),
        (2300, 54, 0.54, 'list_price'),
        (2300, 55, 0.52, 'list_price'),
        (2300, 56, 0.54, 'list_price'),
        (2302, 1, 0.56, 'list_price'),
        (2302, 2, NULL, 'list_price'),
        (2302, 3, NULL, 'list_price'),
        (2302, 4, 0.60, 'list_price'),
        (2302, 5, 0.64, 'list_price'),
        (2302, 6, 0.60, 'list_price'),
        (2302, 7, 0.60, 'list_price'),
        (2302, 8, 0.60, 'list_price'),
        (2302, 9, 0.60, 'list_price'),
        (2302, 10, 0.60, 'list_price'),
        (2302, 11, 0.60, 'list_price'),
        (2302, 12, 0.60, 'list_price'),
        (2302, 13, 0.56, 'list_price'),
        (2302, 14, 0.64, 'list_price'),
        (2302, 15, 0.60, 'list_price'),
        (2302, 16, 0.60, 'list_price'),
        (2302, 17, 0.60, 'list_price'),
        (2302, 18, NULL, 'list_price'),
        (2302, 19, 0.60, 'list_price'),
        (2302, 20, 0.56, 'list_price'),
        (2302, 21, 0.60, 'list_price'),
        (2302, 22, 0.60, 'list_price'),
        (2302, 23, NULL, 'list_price'),
        (2302, 24, 0.60, 'list_price'),
        (2302, 25, 0.52, 'list_price'),
        (2302, 26, 0.60, 'list_price'),
        (2302, 27, 0.60, 'list_price'),
        (2302, 28, 0.60, 'list_price'),
        (2302, 29, 0.60, 'list_price'),
        (2302, 30, 0.60, 'list_price'),
        (2302, 31, 0.60, 'list_price'),
        (2302, 32, 0.60, 'list_price'),
        (2302, 33, 0.60, 'list_price'),
        (2302, 34, 0.60, 'list_price'),
        (2302, 35, 0.60, 'list_price'),
        (2302, 36, 0.60, 'list_price'),
        (2302, 37, 0.60, 'list_price'),
        (2302, 38, 0.60, 'list_price'),
        (2302, 39, 0.60, 'list_price'),
        (2302, 40, 0.56, 'list_price'),
        (2302, 41, 0.68, 'list_price'),
        (2302, 42, 0.56, 'list_price'),
        (2302, 43, NULL, 'list_price'),
        (2302, 44, 0.60, 'list_price'),
        (2302, 45, 0.60, 'list_price'),
        (2302, 46, 0.60, 'list_price'),
        (2302, 47, 0.60, 'list_price'),
        (2302, 48, 0.60, 'list_price'),
        (2302, 49, 0.60, 'list_price'),
        (2302, 50, 0.60, 'list_price'),
        (2302, 51, 0.60, 'list_price'),
        (2302, 52, 0.60, 'list_price'),
        (2302, 53, 0.52, 'list_price'),
        (2302, 54, 0.60, 'list_price'),
        (2302, 55, 0.52, 'list_price'),
        (2302, 56, 0.60, 'list_price'),
        (2304, 1, NULL, 'list_price'),
        (2304, 2, NULL, 'list_price'),
        (2304, 3, 0.56, 'list_price'),
        (2304, 4, 0.60, 'list_price'),
        (2304, 5, 0.64, 'list_price'),
        (2304, 6, 0.60, 'list_price'),
        (2304, 7, 0.60, 'list_price'),
        (2304, 8, 0.60, 'list_price'),
        (2304, 9, 0.60, 'list_price'),
        (2304, 10, 0.60, 'list_price'),
        (2304, 11, 0.60, 'list_price'),
        (2304, 12, 0.60, 'list_price'),
        (2304, 13, 0.62, 'list_price'),
        (2304, 14, 0.64, 'list_price'),
        (2304, 15, 0.60, 'list_price'),
        (2304, 16, 0.60, 'list_price'),
        (2304, 17, 0.60, 'list_price'),
        (2304, 18, NULL, 'list_price'),
        (2304, 19, 0.60, 'list_price'),
        (2304, 20, 0.58, 'list_price'),
        (2304, 21, 0.60, 'list_price'),
        (2304, 22, 0.60, 'list_price'),
        (2304, 23, NULL, 'list_price'),
        (2304, 24, 0.60, 'list_price'),
        (2304, 25, 0.54, 'list_price'),
        (2304, 26, 0.60, 'list_price'),
        (2304, 27, 0.60, 'list_price'),
        (2304, 28, 0.60, 'list_price'),
        (2304, 29, 0.60, 'list_price'),
        (2304, 30, 0.60, 'list_price'),
        (2304, 31, 0.60, 'list_price'),
        (2304, 32, 0.60, 'list_price'),
        (2304, 33, 0.60, 'list_price'),
        (2304, 34, 0.60, 'list_price'),
        (2304, 35, 0.60, 'list_price'),
        (2304, 36, 0.60, 'list_price'),
        (2304, 37, 0.60, 'list_price'),
        (2304, 38, 0.60, 'list_price'),
        (2304, 39, 0.60, 'list_price'),
        (2304, 40, 0.62, 'list_price'),
        (2304, 41, 0.68, 'list_price'),
        (2304, 42, 0.62, 'list_price'),
        (2304, 43, NULL, 'list_price'),
        (2304, 44, 0.60, 'list_price'),
        (2304, 45, 0.60, 'list_price'),
        (2304, 46, 0.60, 'list_price'),
        (2304, 47, 0.60, 'list_price'),
        (2304, 48, 0.60, 'list_price'),
        (2304, 49, 0.60, 'list_price'),
        (2304, 50, 0.60, 'list_price'),
        (2304, 51, 0.60, 'list_price'),
        (2304, 52, 0.60, 'list_price'),
        (2304, 53, 0.54, 'list_price'),
        (2304, 54, 0.60, 'list_price'),
        (2304, 55, 0.54, 'list_price'),
        (2304, 56, 0.60, 'list_price'),
        (2306, 1, 0.54, 'list_price'),
        (2306, 2, NULL, 'list_price'),
        (2306, 3, NULL, 'list_price'),
        (2306, 4, 0.50, 'list_price'),
        (2306, 5, 0.51, 'list_price'),
        (2306, 6, 0.52, 'list_price'),
        (2306, 7, 0.52, 'list_price'),
        (2306, 8, 0.52, 'list_price'),
        (2306, 9, 0.50, 'list_price'),
        (2306, 10, 0.50, 'list_price'),
        (2306, 11, 0.52, 'list_price'),
        (2306, 12, 0.52, 'list_price'),
        (2306, 13, 0.52, 'list_price'),
        (2306, 14, 0.52, 'list_price'),
        (2306, 15, 0.50, 'list_price'),
        (2306, 16, 0.52, 'list_price'),
        (2306, 17, 0.52, 'list_price'),
        (2306, 18, NULL, 'list_price'),
        (2306, 19, 0.52, 'list_price'),
        (2306, 20, 0.50, 'list_price'),
        (2306, 21, 0.52, 'list_price'),
        (2306, 22, 0.52, 'list_price'),
        (2306, 23, NULL, 'list_price'),
        (2306, 24, 0.52, 'list_price'),
        (2306, 25, 0.52, 'list_price'),
        (2306, 26, 0.52, 'list_price'),
        (2306, 27, 0.52, 'list_price'),
        (2306, 28, 0.52, 'list_price'),
        (2306, 29, 0.52, 'list_price'),
        (2306, 30, 0.52, 'list_price'),
        (2306, 31, 0.52, 'list_price'),
        (2306, 32, 0.52, 'list_price'),
        (2306, 33, 0.52, 'list_price'),
        (2306, 34, 0.52, 'list_price'),
        (2306, 35, 0.52, 'list_price'),
        (2306, 36, 0.52, 'list_price'),
        (2306, 37, 0.52, 'list_price'),
        (2306, 38, 0.52, 'list_price'),
        (2306, 39, 0.52, 'list_price'),
        (2306, 40, 0.56, 'list_price'),
        (2306, 41, 0.52, 'list_price'),
        (2306, 42, 0.52, 'list_price'),
        (2306, 43, NULL, 'list_price'),
        (2306, 44, 0.52, 'list_price'),
        (2306, 45, 0.50, 'list_price'),
        (2306, 46, 0.50, 'list_price'),
        (2306, 47, 0.52, 'list_price'),
        (2306, 48, 0.52, 'list_price'),
        (2306, 49, 0.52, 'list_price'),
        (2306, 50, 0.52, 'list_price'),
        (2306, 51, 0.52, 'list_price'),
        (2306, 52, 0.52, 'list_price'),
        (2306, 53, 0.52, 'list_price'),
        (2306, 54, 0.52, 'list_price'),
        (2306, 55, 0.52, 'list_price'),
        (2306, 56, 0.52, 'list_price'),
        (2308, 1, NULL, 'list_price'),
        (2308, 2, NULL, 'list_price'),
        (2308, 3, NULL, 'list_price'),
        (2308, 4, 0.75, 'list_price'),
        (2308, 5, 0.80, 'list_price'),
        (2308, 6, 0.75, 'list_price'),
        (2308, 7, 0.75, 'list_price'),
        (2308, 8, 0.80, 'list_price'),
        (2308, 9, 0.75, 'list_price'),
        (2308, 10, 0.75, 'list_price'),
        (2308, 11, 0.80, 'list_price'),
        (2308, 12, 0.80, 'list_price'),
        (2308, 13, 0.70, 'list_price'),
        (2308, 14, 0.85, 'list_price'),
        (2308, 15, 0.80, 'list_price'),
        (2308, 16, 0.75, 'list_price'),
        (2308, 17, 0.80, 'list_price'),
        (2308, 18, 0.80, 'list_price'),
        (2308, 19, 0.75, 'list_price'),
        (2308, 20, 0.65, 'list_price'),
        (2308, 21, 0.75, 'list_price'),
        (2308, 22, 0.75, 'list_price'),
        (2308, 23, NULL, 'list_price'),
        (2308, 24, 0.75, 'list_price'),
        (2308, 25, 0.70, 'list_price'),
        (2308, 26, 0.75, 'list_price'),
        (2308, 27, 0.75, 'list_price'),
        (2308, 28, 0.75, 'list_price'),
        (2308, 29, 0.75, 'list_price'),
        (2308, 30, 0.75, 'list_price'),
        (2308, 31, 0.75, 'list_price'),
        (2308, 32, 0.75, 'list_price'),
        (2308, 33, 0.75, 'list_price'),
        (2308, 34, 0.75, 'list_price'),
        (2308, 35, 0.75, 'list_price'),
        (2308, 36, 0.75, 'list_price'),
        (2308, 37, 0.75, 'list_price'),
        (2308, 38, 0.75, 'list_price'),
        (2308, 39, 0.75, 'list_price'),
        (2308, 40, 0.70, 'list_price'),
        (2308, 41, 0.80, 'list_price'),
        (2308, 42, 0.70, 'list_price'),
        (2308, 43, NULL, 'list_price'),
        (2308, 44, 0.75, 'list_price'),
        (2308, 45, 0.75, 'list_price'),
        (2308, 46, 0.80, 'list_price'),
        (2308, 47, 0.80, 'list_price'),
        (2308, 48, 0.75, 'list_price'),
        (2308, 49, 0.75, 'list_price'),
        (2308, 50, 0.75, 'list_price'),
        (2308, 51, 0.75, 'list_price'),
        (2308, 52, 0.75, 'list_price'),
        (2308, 53, 0.60, 'list_price'),
        (2308, 54, 0.80, 'list_price'),
        (2308, 55, 0.60, 'list_price'),
        (2308, 56, 0.75, 'list_price'),
        (2310, 1, 0.43, 'list_price'),
        (2310, 2, NULL, 'list_price'),
        (2310, 3, 0.52, 'list_price'),
        (2310, 4, 0.52, 'list_price'),
        (2310, 5, 0.54, 'list_price'),
        (2310, 6, 0.52, 'list_price'),
        (2310, 7, 0.53, 'list_price'),
        (2310, 8, 0.54, 'list_price'),
        (2310, 9, 0.53, 'list_price'),
        (2310, 10, 0.53, 'list_price'),
        (2310, 11, 0.52, 'list_price'),
        (2310, 12, 0.54, 'list_price'),
        (2310, 13, 0.52, 'list_price'),
        (2310, 14, 0.54, 'list_price'),
        (2310, 15, 0.52, 'list_price'),
        (2310, 16, 0.54, 'list_price'),
        (2310, 17, 0.54, 'list_price'),
        (2310, 18, NULL, 'list_price'),
        (2310, 19, 0.52, 'list_price'),
        (2310, 20, 0.52, 'list_price'),
        (2310, 21, 0.53, 'list_price'),
        (2310, 22, 0.53, 'list_price'),
        (2310, 23, 0.54, 'list_price'),
        (2310, 24, 0.53, 'list_price'),
        (2310, 25, 0.52, 'list_price'),
        (2310, 26, 0.53, 'list_price'),
        (2310, 27, 0.53, 'list_price'),
        (2310, 28, 0.53, 'list_price'),
        (2310, 29, 0.53, 'list_price'),
        (2310, 30, 0.53, 'list_price'),
        (2310, 31, 0.53, 'list_price'),
        (2310, 32, 0.53, 'list_price'),
        (2310, 33, 0.53, 'list_price'),
        (2310, 34, 0.52, 'list_price'),
        (2310, 35, 0.53, 'list_price'),
        (2310, 36, 0.56, 'list_price'),
        (2310, 37, 0.53, 'list_price'),
        (2310, 38, 0.53, 'list_price'),
        (2310, 39, 0.53, 'list_price'),
        (2310, 40, 0.52, 'list_price'),
        (2310, 41, 0.52, 'list_price'),
        (2310, 42, 0.54, 'list_price'),
        (2310, 43, 0.54, 'list_price'),
        (2310, 44, 0.52, 'list_price'),
        (2310, 45, 0.52, 'list_price'),
        (2310, 46, 0.52, 'list_price'),
        (2310, 47, 0.54, 'list_price'),
        (2310, 48, 0.53, 'list_price'),
        (2310, 49, 0.52, 'list_price'),
        (2310, 50, 0.53, 'list_price'),
        (2310, 51, 0.52, 'list_price'),
        (2310, 52, 0.52, 'list_price'),
        (2310, 53, 0.52, 'list_price'),
        (2310, 54, 0.54, 'list_price'),
        (2310, 55, 0.52, 'list_price'),
        (2310, 56, 0.53, 'list_price'),
        (2312, 1, NULL, 'list_price'),
        (2312, 2, NULL, 'list_price'),
        (2312, 3, NULL, 'list_price'),
        (2312, 4, 0.54, 'list_price'),
        (2312, 5, 0.55, 'list_price'),
        (2312, 6, 0.54, 'list_price'),
        (2312, 7, 0.54, 'list_price'),
        (2312, 8, 0.56, 'list_price'),
        (2312, 9, 0.54, 'list_price'),
        (2312, 10, 0.54, 'list_price'),
        (2312, 11, 0.54, 'list_price'),
        (2312, 12, 0.56, 'list_price'),
        (2312, 13, 0.55, 'list_price'),
        (2312, 14, 0.56, 'list_price'),
        (2312, 15, 0.54, 'list_price'),
        (2312, 16, 0.54, 'list_price'),
        (2312, 17, 0.56, 'list_price'),
        (2312, 18, NULL, 'list_price'),
        (2312, 19, 0.54, 'list_price'),
        (2312, 20, 0.54, 'list_price'),
        (2312, 21, 0.54, 'list_price'),
        (2312, 22, 0.54, 'list_price'),
        (2312, 23, NULL, 'list_price'),
        (2312, 24, 0.54, 'list_price'),
        (2312, 25, 0.52, 'list_price'),
        (2312, 26, 0.54, 'list_price'),
        (2312, 27, 0.54, 'list_price'),
        (2312, 28, 0.54, 'list_price'),
        (2312, 29, 0.54, 'list_price'),
        (2312, 30, 0.54, 'list_price'),
        (2312, 31, 0.54, 'list_price'),
        (2312, 32, 0.54, 'list_price'),
        (2312, 33, 0.54, 'list_price'),
        (2312, 34, 0.54, 'list_price'),
        (2312, 35, 0.54, 'list_price'),
        (2312, 36, 0.54, 'list_price'),
        (2312, 37, 0.54, 'list_price'),
        (2312, 38, 0.54, 'list_price'),
        (2312, 39, 0.54, 'list_price'),
        (2312, 40, 0.55, 'list_price'),
        (2312, 41, 0.56, 'list_price'),
        (2312, 42, 0.55, 'list_price'),
        (2312, 43, NULL, 'list_price'),
        (2312, 44, 0.52, 'list_price'),
        (2312, 45, 0.54, 'list_price'),
        (2312, 46, 0.54, 'list_price'),
        (2312, 47, 0.56, 'list_price'),
        (2312, 48, 0.54, 'list_price'),
        (2312, 49, 0.54, 'list_price'),
        (2312, 50, 0.54, 'list_price'),
        (2312, 51, 0.54, 'list_price'),
        (2312, 52, 0.54, 'list_price'),
        (2312, 53, 0.52, 'list_price'),
        (2312, 54, 0.56, 'list_price'),
        (2312, 55, 0.52, 'list_price'),
        (2312, 56, 0.54, 'list_price'),
        (2314, 1, NULL, 'list_price'),
        (2314, 2, NULL, 'list_price'),
        (2314, 3, NULL, 'list_price'),
        (2314, 4, 0.60, 'list_price'),
        (2314, 5, 0.64, 'list_price'),
        (2314, 6, 0.60, 'list_price'),
        (2314, 7, 0.62, 'list_price'),
        (2314, 8, 0.60, 'list_price'),
        (2314, 9, 0.62, 'list_price'),
        (2314, 10, 0.62, 'list_price'),
        (2314, 11, 0.62, 'list_price'),
        (2314, 12, 0.60, 'list_price'),
        (2314, 13, 0.64, 'list_price'),
        (2314, 14, 0.70, 'list_price'),
        (2314, 15, 0.60, 'list_price'),
        (2314, 16, 0.62, 'list_price'),
        (2314, 17, 0.62, 'list_price'),
        (2314, 18, NULL, 'list_price'),
        (2314, 19, 0.62, 'list_price'),
        (2314, 20, 0.58, 'list_price'),
        (2314, 21, 0.62, 'list_price'),
        (2314, 22, 0.62, 'list_price'),
        (2314, 23, NULL, 'list_price'),
        (2314, 24, 0.62, 'list_price'),
        (2314, 25, 0.56, 'list_price'),
        (2314, 26, 0.62, 'list_price'),
        (2314, 27, 0.62, 'list_price'),
        (2314, 28, 0.62, 'list_price'),
        (2314, 29, 0.62, 'list_price'),
        (2314, 30, 0.62, 'list_price'),
        (2314, 31, 0.62, 'list_price'),
        (2314, 32, 0.62, 'list_price'),
        (2314, 33, 0.62, 'list_price'),
        (2314, 34, 0.62, 'list_price'),
        (2314, 35, 0.62, 'list_price'),
        (2314, 36, 0.62, 'list_price'),
        (2314, 37, 0.62, 'list_price'),
        (2314, 38, 0.62, 'list_price'),
        (2314, 39, 0.62, 'list_price'),
        (2314, 40, 0.64, 'list_price'),
        (2314, 41, 0.60, 'list_price'),
        (2314, 42, 0.64, 'list_price'),
        (2314, 43, NULL, 'list_price'),
        (2314, 44, 0.60, 'list_price'),
        (2314, 45, 0.60, 'list_price'),
        (2314, 46, 0.62, 'list_price'),
        (2314, 47, 0.62, 'list_price'),
        (2314, 48, 0.62, 'list_price'),
        (2314, 49, 0.62, 'list_price'),
        (2314, 50, 0.62, 'list_price'),
        (2314, 51, 0.60, 'list_price'),
        (2314, 52, 0.60, 'list_price'),
        (2314, 53, 0.56, 'list_price'),
        (2314, 54, 0.60, 'list_price'),
        (2314, 55, 0.56, 'list_price'),
        (2314, 56, 0.62, 'list_price'),
        (2316, 1, 0.56, 'list_price'),
        (2316, 2, NULL, 'list_price'),
        (2316, 3, NULL, 'list_price'),
        (2316, 4, 0.56, 'list_price'),
        (2316, 5, 0.54, 'list_price'),
        (2316, 6, 0.56, 'list_price'),
        (2316, 7, 0.56, 'list_price'),
        (2316, 8, 0.56, 'list_price'),
        (2316, 9, 0.56, 'list_price'),
        (2316, 10, 0.56, 'list_price'),
        (2316, 11, 0.58, 'list_price'),
        (2316, 12, 0.56, 'list_price'),
        (2316, 13, 0.56, 'list_price'),
        (2316, 14, 0.58, 'list_price'),
        (2316, 15, 0.58, 'list_price'),
        (2316, 16, 0.56, 'list_price'),
        (2316, 17, 0.58, 'list_price'),
        (2316, 18, NULL, 'list_price'),
        (2316, 19, 0.56, 'list_price'),
        (2316, 20, 0.52, 'list_price'),
        (2316, 21, 0.56, 'list_price'),
        (2316, 22, 0.56, 'list_price'),
        (2316, 23, 0.56, 'list_price'),
        (2316, 24, 0.56, 'list_price'),
        (2316, 25, 0.54, 'list_price'),
        (2316, 26, 0.56, 'list_price'),
        (2316, 27, 0.56, 'list_price'),
        (2316, 28, 0.56, 'list_price'),
        (2316, 29, 0.56, 'list_price'),
        (2316, 30, 0.56, 'list_price'),
        (2316, 31, 0.56, 'list_price'),
        (2316, 32, 0.56, 'list_price'),
        (2316, 33, 0.56, 'list_price'),
        (2316, 34, 0.56, 'list_price'),
        (2316, 35, 0.56, 'list_price'),
        (2316, 36, 0.56, 'list_price'),
        (2316, 37, 0.56, 'list_price'),
        (2316, 38, 0.56, 'list_price'),
        (2316, 39, 0.56, 'list_price'),
        (2316, 40, 0.56, 'list_price'),
        (2316, 41, 0.70, 'list_price'),
        (2316, 42, 0.56, 'list_price'),
        (2316, 43, NULL, 'list_price'),
        (2316, 44, 0.54, 'list_price'),
        (2316, 45, 0.56, 'list_price'),
        (2316, 46, 0.54, 'list_price'),
        (2316, 47, 0.58, 'list_price'),
        (2316, 48, 0.56, 'list_price'),
        (2316, 49, 0.56, 'list_price'),
        (2316, 50, 0.56, 'list_price'),
        (2316, 51, 0.56, 'list_price'),
        (2316, 52, 0.56, 'list_price'),
        (2316, 53, 0.52, 'list_price'),
        (2316, 54, 0.56, 'list_price'),
        (2316, 55, 0.52, 'list_price'),
        (2316, 56, 0.56, 'list_price'),
        (2318, 1, NULL, 'list_price'),
        (2318, 2, NULL, 'list_price'),
        (2318, 3, 0.64, 'list_price'),
        (2318, 4, 0.80, 'list_price'),
        (2318, 5, 0.90, 'list_price'),
        (2318, 6, 0.80, 'list_price'),
        (2318, 7, 0.80, 'list_price'),
        (2318, 8, 0.80, 'list_price'),
        (2318, 9, 0.80, 'list_price'),
        (2318, 10, 0.80, 'list_price'),
        (2318, 11, 0.80, 'list_price'),
        (2318, 12, 0.80, 'list_price'),
        (2318, 13, 0.70, 'list_price'),
        (2318, 14, 0.95, 'list_price'),
        (2318, 15, 0.80, 'list_price'),
        (2318, 16, 0.80, 'list_price'),
        (2318, 17, 0.95, 'list_price'),
        (2318, 18, 0.80, 'list_price'),
        (2318, 19, 0.80, 'list_price'),
        (2318, 20, 0.58, 'list_price'),
        (2318, 21, 0.80, 'list_price'),
        (2318, 22, 0.80, 'list_price'),
        (2318, 23, NULL, 'list_price'),
        (2318, 24, 0.80, 'list_price'),
        (2318, 25, 0.70, 'list_price'),
        (2318, 26, 0.80, 'list_price'),
        (2318, 27, 0.80, 'list_price'),
        (2318, 28, 0.80, 'list_price'),
        (2318, 29, 0.80, 'list_price'),
        (2318, 30, 0.80, 'list_price'),
        (2318, 31, 0.80, 'list_price'),
        (2318, 32, 0.80, 'list_price'),
        (2318, 33, 0.80, 'list_price'),
        (2318, 34, 0.80, 'list_price'),
        (2318, 35, 0.80, 'list_price'),
        (2318, 36, 0.80, 'list_price'),
        (2318, 37, 0.80, 'list_price'),
        (2318, 38, 0.80, 'list_price'),
        (2318, 39, 0.80, 'list_price'),
        (2318, 40, 0.70, 'list_price'),
        (2318, 41, 0.80, 'list_price'),
        (2318, 42, 0.70, 'list_price'),
        (2318, 43, NULL, 'list_price'),
        (2318, 44, 0.80, 'list_price'),
        (2318, 45, 0.80, 'list_price'),
        (2318, 46, 0.99, 'list_price'),
        (2318, 47, 0.95, 'list_price'),
        (2318, 48, 0.80, 'list_price'),
        (2318, 49, 0.80, 'list_price'),
        (2318, 50, 0.80, 'list_price'),
        (2318, 51, 0.80, 'list_price'),
        (2318, 52, 0.80, 'list_price'),
        (2318, 53, 0.60, 'list_price'),
        (2318, 54, 0.80, 'list_price'),
        (2318, 55, 0.80, 'list_price'),
        (2318, 56, 0.80, 'list_price'),
        (2350, 1, 2.00, 'list_price'),
        (2350, 2, NULL, 'list_price'),
        (2350, 3, NULL, 'list_price'),
        (2350, 4, 0.99, 'list_price'),
        (2350, 5, 1.00, 'list_price'),
        (2350, 6, 0.99, 'list_price'),
        (2350, 7, 0.99, 'list_price'),
        (2350, 8, 0.99, 'list_price'),
        (2350, 9, 1.04, 'list_price'),
        (2350, 10, 1.04, 'list_price'),
        (2350, 11, 0.99, 'list_price'),
        (2350, 12, 0.99, 'list_price'),
        (2350, 13, 1.00, 'list_price'),
        (2350, 14, 1.20, 'list_price'),
        (2350, 15, 1.20, 'list_price'),
        (2350, 16, 0.99, 'list_price'),
        (2350, 17, 1.20, 'list_price'),
        (2350, 18, NULL, 'list_price'),
        (2350, 19, 0.90, 'list_price'),
        (2350, 20, NULL, 'list_price'),
        (2350, 21, 0.99, 'list_price'),
        (2350, 22, 0.99, 'list_price'),
        (2350, 23, NULL, 'list_price'),
        (2350, 24, 0.99, 'list_price'),
        (2350, 25, 0.99, 'list_price'),
        (2350, 26, 0.99, 'list_price'),
        (2350, 27, 0.99, 'list_price'),
        (2350, 28, 0.99, 'list_price'),
        (2350, 29, 0.99, 'list_price'),
        (2350, 30, 0.99, 'list_price'),
        (2350, 31, 0.99, 'list_price'),
        (2350, 32, 0.99, 'list_price'),
        (2350, 33, 0.99, 'list_price'),
        (2350, 34, 0.99, 'list_price'),
        (2350, 35, 0.99, 'list_price'),
        (2350, 36, 0.99, 'list_price'),
        (2350, 37, 0.99, 'list_price'),
        (2350, 38, 0.99, 'list_price'),
        (2350, 39, 0.99, 'list_price'),
        (2350, 40, 1.00, 'list_price'),
        (2350, 41, 0.99, 'list_price'),
        (2350, 42, NULL, 'list_price'),
        (2350, 43, NULL, 'list_price'),
        (2350, 44, 1.20, 'list_price'),
        (2350, 45, 0.99, 'list_price'),
        (2350, 46, 1.20, 'list_price'),
        (2350, 47, 1.20, 'list_price'),
        (2350, 48, 0.99, 'list_price'),
        (2350, 49, 0.96, 'list_price'),
        (2350, 50, 0.99, 'list_price'),
        (2350, 51, 1.12, 'list_price'),
        (2350, 52, 0.99, 'list_price'),
        (2350, 53, NULL, 'list_price'),
        (2350, 54, 0.99, 'list_price'),
        (2350, 55, NULL, 'list_price'),
        (2350, 56, 0.99, 'list_price'),
        (2352, 1, NULL, 'our_cost'),
        (2352, 2, 1.20, 'our_cost'),
        (2352, 3, NULL, 'our_cost'),
        (2352, 4, 1.15, 'our_cost'),
        (2352, 5, 1.20, 'our_cost'),
        (2352, 6, 1.15, 'our_cost'),
        (2352, 7, 1.15, 'our_cost'),
        (2352, 8, 1.15, 'our_cost'),
        (2352, 9, 1.15, 'our_cost'),
        (2352, 10, 1.15, 'our_cost'),
        (2352, 11, 1.15, 'our_cost'),
        (2352, 12, 1.15, 'our_cost'),
        (2352, 13, 1.20, 'our_cost'),
        (2352, 14, 1.15, 'our_cost'),
        (2352, 15, 1.15, 'our_cost'),
        (2352, 16, 1.15, 'our_cost'),
        (2352, 17, 1.15, 'our_cost'),
        (2352, 18, 1.15, 'our_cost'),
        (2352, 19, 1.15, 'our_cost'),
        (2352, 20, NULL, 'our_cost'),
        (2352, 21, 1.15, 'our_cost'),
        (2352, 22, 1.15, 'our_cost'),
        (2352, 23, NULL, 'our_cost'),
        (2352, 24, 1.15, 'our_cost'),
        (2352, 25, 1.15, 'our_cost'),
        (2352, 26, 1.15, 'our_cost'),
        (2352, 27, 1.15, 'our_cost'),
        (2352, 28, 1.15, 'our_cost'),
        (2352, 29, 1.15, 'our_cost'),
        (2352, 30, 1.15, 'our_cost'),
        (2352, 31, 1.15, 'our_cost'),
        (2352, 32, 1.15, 'our_cost'),
        (2352, 33, 1.15, 'our_cost'),
        (2352, 34, 1.15, 'our_cost'),
        (2352, 35, 1.15, 'our_cost'),
        (2352, 36, 1.15, 'our_cost'),
        (2352, 37, 1.15, 'our_cost'),
        (2352, 38, 1.15, 'our_cost'),
        (2352, 39, 1.15, 'our_cost'),
        (2352, 40, 1.25, 'our_cost'),
        (2352, 41, 1.15, 'our_cost'),
        (2352, 42, 1.25, 'our_cost'),
        (2352, 43, NULL, 'our_cost'),
        (2352, 44, 1.15, 'our_cost'),
        (2352, 45, 1.15, 'our_cost'),
        (2352, 46, 1.15, 'our_cost'),
        (2352, 47, 1.15, 'our_cost'),
        (2352, 48, 1.15, 'our_cost'),
        (2352, 49, 1.15, 'our_cost'),
        (2352, 50, 1.15, 'our_cost'),
        (2352, 51, 1.15, 'our_cost'),
        (2352, 52, 1.15, 'our_cost'),
        (2352, 53, 1.15, 'our_cost'),
        (2352, 54, 1.15, 'our_cost'),
        (2352, 55, 1.15, 'our_cost'),
        (2352, 56, 1.15, 'our_cost');
    ");

    $categoryIds = Category::item()->pluck('id')->all();
    Level::all()
      ->each(function ($level) use ($categoryIds) {
        $level->categories()->syncWithoutDetaching($categoryIds);
      });

    DB::table('category_level')
      ->whereNotNull('created_at')
      ->update(['pricing_method' => PriceCalculator::PRICING_ON_LIST_PRICE]);

    DB::table('category_level')
      ->whereNull('created_at')
      ->update([
        'created_at' => now()->toDateTimeString(),
        'updated_at' => now()->toDateTimeString()
      ]);
  }

  public function importCustomerSpecialPrices()
  {
    try {
      DB::table('customer_item')->truncate();

      Customer::where('options->special_pricing', '<>', 'null')
        ->get()
        ->each(function ($customer) {
          $specialPricing = explode("\n", $customer->special_pricing);

          $items = collect($specialPricing)
            ->mapWithKeys(function ($itemDetails) use($customer) {
              $itemPartNumber = Str::before($itemDetails, ' ');
              $itemPartPrice = Str::after($itemDetails, ' $');

              if (Str::contains($itemPartPrice, '$')) {
                // String contains two prices e.g 900.00$456.00, let's take the first price
                dump($customer->account_number . ' - ' . $customer->name . ': ' . $itemDetails . ' - unknown price');
                $itemPartPrice = Str::before($itemPartPrice, '$');
              } else if (Str::contains($itemPartPrice, '/')) {
                dump($customer->account_number . ' - ' . $customer->name . ': ' . $itemDetails . ' - unknown price');
                $itemPartPrice = Str::before($itemPartPrice, '/');
              }

              $item = Item::whereCode($itemPartNumber)->first();

              if (! empty($item)) {
                return [
                  $item->id => [
                    'fixed_price' => Str::after($itemPartPrice, '$')
                  ]
                ];
              }

              dump($customer->account_number . ' - ' . $customer->name . ': ' . $itemDetails . ' - item not found');
              return [
                []
              ];
            })
            ->all();

          $customer->specialPriceItems()->syncWithoutDetaching($items);
        })->all();
    } catch (\Exception $e) {
      logger()->debug($e->getMessage());
    }
  }

  public function importItemPriceLevels()
  {
    $priceLevelModels = Import::where('data_type', 'price_levels')->get();
    $levelIds = Level::whereIn('id', [2314, 2304, 2302, 2312, 2310, 2306, 2300])
      ->pluck('id')
      ->all();
    DB::table('item_level')->truncate();
    $priceLevelModels->each(function ($import) use ($levelIds) {
      collect($import->data)->each(function ($levelPrice) use ($levelIds) {

        $item = Item::whereCode($levelPrice['SOFTENERS'])->first();

        if (empty($item)) {
          return;
        }

        $levelPrices = collect(Arr::only($levelPrice, $levelIds))
          ->mapWithKeys(function ($price, $levelId) {
            return [
              $levelId => ['fixed_price' => $price]
            ];
          })->all();

        $item->levels()->syncWithoutDetaching($levelPrices);
      });
    });
  }

  /**
   * Extract data from the request and map to the part attributes
   * All other unknown attributes are saved in part options (json)
   *
   * @param array $row
   * @return array
   */
  public function extractItemData(array $row): array
  {
    $type = empty($row['BOM']) ? 'part' : 'product';
    $listPriceSpecial = floatval($row['ListPriceSpecial']);
    $multiplier = $listPriceSpecial > 0.0 ? $listPriceSpecial : 2.5; // TODO: Replace 2.5 with a setting value
    $costDate = Carbon::parseFromLocale($row['Date'], 'us-en')->toDateString();

    $record = [
      'code' => $row['OurPart#'],
      'number' => $row['NumericPartNo'],
      'name' => $this->getItemName($row),
      'type' => $type,
      'price' => $this->formatNumber($row['Price']),
      'freight_cost' => $this->formatNumber($row['Freight']),
      'misc_cost' => $this->formatNumber($row['MiscCost']),
      'cost' => $this->formatNumber($row['OurCost']),
      'list_price' => $this->formatNumber($row['ListPrice']),
      'multiplier' => $this->formatNumber($multiplier, 2),
      'unit' => $row['Unit'],
      'price_code' => $row['PriceCode'],
      'salable' => strtolower($row['SALESITEM']) === 'yes',
      'quantity' => (int)$row['InvQty'],
      'category_id' => $this->getCategoryId($row['Category']),
      'description' => $row['Description'],
      'build_description' => $row['BuildDescription'],
      'purchase_instructions' => $row['PurchaseDescription'],
      'receiving_instructions' => $row['ReceivingInstructions'],
      'rebuild_list' => $row['RebuildList'],
      'weight' => $this->formatNumber($row['Weight'], 3),
      'height' => $this->formatNumber($row['HEIGHT']),
      'width' => $this->formatNumber($row['WIDTH']),
      'depth' => $this->formatNumber($row['DEPTH']),
      'label' => $row['LABEL'],
      'created_at' => Carbon::parse($row['Date'])->toDateTimeString(),
      'cost_date' => $costDate,
      'options' => [
        'approved_by' => $row['ApprovedBy'],
        'bom' => $row['BOM'],
        'bom_cost' => $row['BOMCost'],
        'bom_list' => $row['BOMList'],
        'calc_price' => $row['CalcPrice'],
        'case_quantity' => $row['CaseQty'],
        'customer_codes' => $row['CustomerCodes'],
        'expanded_bom' => $row['EXPANDEDBOM'],
        'favorite' => (bool)$row['Favorite'],
        'list_price' => $row['ListPrice'],
        'list_price_special' => $row['ListPriceSpecial'],
        'manufacturer_part_number' => $row['MfgPart#'],
        'new_list_date' => $row['NEWLISTDATE'],
        'program_as' => $row['ProgramAs'],
        'purchase_unit' => $row['PurchaseUnit'],
        'real_value' => $row['REALVALUE'],
        'retail' => $row['Retail'],
        'soft_list' => $row['SOFTLIST'],
        'sync_time' => $row['SYNCTIME'],
        'sort' => $row['Sort'],
        'special_instruction' => $row['SpecialInstructions'],
        'special_price' => $row['SpecialPrice'],
        'specs_description' => $row['SpecsDescription'],
        'stock_quantity' => $row['StockQty'],
        'temp' => $row['Temp'],
        'test' => $row['Test'],
        'true_cost' => $row['TrueCost'],
        'options' => $row['Options']
      ],
      'notes' => [
        [
          'type' => 'general',
          'body' => $row['GeneralNotes'],
        ],
        [
          'type' => 'default',
          'body' => $row['Notes']
        ],
        [
          'type' => 'product',
          'body' => $row['ProductNotes']
        ]
      ]
    ];

    // Remove empty notes
    $record['notes'] = $this->getNotes($record['notes']);

    if (empty($row['Vendor'])) {
      // This item has no vendor information so let's just return the known item details
      return $record;
    }

    $vendor = Provider::whereAccountNumber($row['Vendor'])->first();

    if (empty($vendor)) {
      // This item has an unknown vendor. So let's just keep those details
      $record['options']['price'] = $row['Price'];
      $record['options']['vendor'] = $row['Vendor'];

      return $record;
    }

    // The item's vendor is known so let's extract the details
    $record['vendor'] = [
      $vendor->id => [
        'cost' => $row['Price'],
        'name' => $row['MfgPart#']
      ]
    ];

    return $record;
  }

  /**
   * Clean items to remove duplicates and items without part number
   *
   * @param array $sourceData
   * @return array
   */
  public function cleanItems(array $sourceData): array
  {
    return collect($sourceData)
      ->unique('OurPart#')
      ->reject(function ($row) {
        return empty($row['OurPart#']);
      })->all();
  }

  protected function getNotes(array $rawNotes): array
  {
    return collect($rawNotes)
      ->reject(function ($note) {
        return empty($note['body']);
      })
      ->map(function ($note) {
        return new Note([
          'body' => $note['body'],
          'type' => $note['type']
        ]);
      })
      ->all();
  }

  public function getExtractedData(Import $import)
  {
    if ($import->extracted) {
      return $import->extracted_data;
    }

    if (! $import->cleaned) {
      $import->cleaned_data = $this->cleanItems($import->data);
      $import->cleaned = true;
    }

    $import->extracted_data = $this->extractData($import->cleaned_data);
    $import->extracted = true;
    $import->save();

    return $import->extracted_data;
  }

  protected function extractData($cleanedData): array
  {
    return collect($cleanedData)->map(function ($row) {
      return $this->extractItemData($row);
    })->all();
  }

  protected function getItemName(array $row)
  {
    return !empty($row['Description']) ? $row['Description'] : $row['OurPart#'];
  }

  protected function getCategoryId($rawCategory)
  {
    $category = Category::type('item')->where('name', $rawCategory)->first();

    return optional($category)->id;
  }

  protected function formatNumber($num, int $decimals = 2): float
  {
    return floatval(number_format($num, $decimals, '.', ''));
  }
}
