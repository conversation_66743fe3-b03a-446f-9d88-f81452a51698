<?php


namespace App\Actions;


use App\Models\ImportInvoice;
use App\Traits\UpdatesImportedOrderAmounts;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Modules\Inventory\Entities\Item;
use Modules\Orders\Entities\Order;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\User;

class CreateOrderFromImportedDataAction
{
  use UpdatesImportedOrderAmounts;

  public function execute(Collection $rawOrders): bool
  {
    $importInvoiceModel = $rawOrders->first();
    $customer = $this->findOrCreateCustomer($importInvoiceModel->only(['customer_code', 'customer']));

    $salesperson = User::where('name', 'LIKE', "%$importInvoiceModel->salesperson%")->first();
    $rawShippingDate = $importInvoiceModel->ship_date;

    if (Str::contains($rawShippingDate, '/')) {
      $shippingDate = Carbon::parse($rawShippingDate)->toDateString();
      $shippingDateOption = null;
    } else {
      $shippingDate = null;
      $shippingDateOption = $rawShippingDate;
    }

    $orderDetails = [
      'created_at' => Carbon::parse($importInvoiceModel->date)->toDateTimeString(),
      'created_by' => optional($salesperson)->id ?? 3, // Pacific Test Account
      'number' => $this->getOrderNumber($importInvoiceModel->order_number),
      'full_number' => $importInvoiceModel->order_number,
      'customer_id' => $customer->user_id,
      'purchase_order_number' => $importInvoiceModel->purchase_order_number,
      'shop_notes' => $importInvoiceModel->shop_notes,
      'private_notes' => $importInvoiceModel->private_notes,
      'billing_address' => $importInvoiceModel->billing_address,
      'shipping_name' => $importInvoiceModel->ship_company,
      'shipping_actual_freight' => $importInvoiceModel->actual_freight,
      'shipping_address' => $importInvoiceModel->ship_address,
      'shipping_phone' => $importInvoiceModel->ship_phone,
      'shipping_note' => $importInvoiceModel->ship_date_notes,
      'shipping_tracking_number' => $importInvoiceModel->tracking,
      'shipping_date' => $shippingDate,
      'shipping_date_option' => $shippingDateOption,
      'customer_placed_by' => $importInvoiceModel->ordered_by,
      'customer_pickup_by' => $importInvoiceModel->picked_by,
      'salesperson' => optional($salesperson)->name ?? $importInvoiceModel->salesperson,
      'final_date' => Carbon::parse($importInvoiceModel->final_date)->toDateString(),
      'quotation_full_number' => $importInvoiceModel->quote_number,
      'imported' => true,
      'customer_taxable' => Str::contains($importInvoiceModel->taxable, ['No', 'NO']) ? 0 : 1,
      'editable' => false,
      'archived' => true,
      'status' => 'archived',
      'options' => [
        'printed' => $importInvoiceModel->printed,
        'statuses' => [
//          'created' => [
//            'name' => 'created',
//            'created_at' => now()->format('m-d-y H:i:s'),
//            'created_by' => auth()->id(),
//            'previous' => null,
//            'note' => ''
//          ]
        ]
      ]
    ];

    if (! empty($invoiceNumber = $importInvoiceModel->invoice_number)) {
      $orderDetails['number'] = $invoiceNumber;
      $orderDetails['invoiced'] = true;
      $orderDetails['invoiced_at'] = Carbon::parse($importInvoiceModel->final_date)->toDateString();
      $orderDetails['invoiced_by'] = optional($salesperson)->id ?? 3; // Pacific Test Account
    }

    $order = $customer->orders()->updateOrCreate([
      'number' => $orderDetails['number'],
      'customer_id' => $orderDetails['customer_id']
    ], $orderDetails);

    $shipViaItem = null;
    $taxItem = null;

    $orderItemRecords = $rawOrders->sortBy('LINE')
      ->map(function (ImportInvoice $importInvoice, $lineKey) use ($order, &$shipViaItem, &$taxItem) {
        $inventoryItem = Item::where('code', $importInvoice->part_number)->first();
        $itemCost = round($importInvoice->cost, 6);

        $record = [
          'quantity' => $importInvoice->quantity,
          'code' => $importInvoice->part_number,
          'description' => $importInvoice->product,
          'price' => $importInvoice->price ?? 0,
          'cost' => $itemCost,
          'build_instructions' => $importInvoice->notes,
          'location' => $importInvoice->unit_location,
        ];

        if (Str::contains(strtolower($itemCost), 'e+') || Str::contains(strtolower($itemCost), 'e-')) {
          logger()->debug('Item: ' . $record['code'] . ' ' . $record['cost']);
          $record['cost'] = $inventoryItem->cost ?? substr($itemCost, 0, 10);
        }

        if ($record['code'] === 'ZXUT') {
          // This is a tax record, we skip it but use it on the order
          $record['total'] = $importInvoice->total;
          $taxItem = $record;
          return null;
        }

        if (Str::contains($record['description'], 'Ship Via')) {
          // This is a Ship Via record, we skip it so that we have the details on the order.
          $shipViaItem = $record;

          return null;
        }

        if (! empty($inventoryItem)) {
          $record['item_id'] = $inventoryItem->id;
          $record['type'] = 'inventory';
        } else {
          $record['item_id'] = 'custom_item_' . $lineKey;
          $record['type'] = 'custom';
        }

        $record['order_id'] = $order->id;

        return $record;
      })
      ->filter()
      ->all();

    $order->items()->upsert(
      $orderItemRecords,
      [
        'item_id',
        'order_id',
        'quantity'
      ]);

    $order->refresh();

    $order = $this->addOutOfStateDetails($order, $importInvoiceModel);

    if (! is_null($shipViaItem)) {
      $order->shipping_via = Str::after($shipViaItem['description'], 'Ship Via ');
      $order->shipping_cost = $shipViaItem['price'];
    }

    if (is_null($taxItem)) {
      $taxAmount = 0;
      $taxable = false;
    } else {
      $taxable = true;
      $taxAmount = round($taxItem['total'], 2);
    }

    return $this->updateOrderAmounts($order, $taxable, $taxAmount);
  }

  protected function findOrCreateCustomer(array $customerDetails)
  {
    $customerCode = Arr::get($customerDetails, 'customer_code');

    return Customer::select('user_id')
        ->where('account_number', $customerCode)
        ->orWhere('code', $customerCode)
        ->first() ?? $this->createCustomer($customerDetails);
  }

  protected function createCustomer(array $details)
  {
    return User::create([
      'name' => Arr::get($details, 'customer'),
      'password' => Hash::make(Str::random()),
      'type' => User::CUSTOMER,
    ])->customer()->create([
      'account_number' => Arr::get($details, 'customer_code') ?? Customer::nextAccountNumber(),
      'type' => Customer::TYPE_RECEIVABLE,
    ]);
  }

  protected function getOrderNumber(string $orderNumber): string
  {
    $result = '';

    for($i = 0; $i < strlen($orderNumber); $i++) {
      if (is_numeric($char = $orderNumber[$i])) {
        $result .= $char;
      }
    }

    return $result;
  }

  protected function addOutOfStateDetails(Order $order, $orderData): Order
  {
    if (! empty($orderData->ship_address)) {
      return $order;
    }

    $shippingOutOfState = ! Str::contains($orderData->ship_address, [' Utah ', ' UT ', ' utah ', ' UTAH ']);
    $billingOutOfState = ! Str::contains($orderData->billing_address, [' Utah ', ' UT ', ' utah ', ' UTAH ']);

    if ($shippingOutOfState && $billingOutOfState) {
      $order->out_of_state = true;

      return $order;
    }

    $order->out_of_state = false;

    return $order;
  }
}
