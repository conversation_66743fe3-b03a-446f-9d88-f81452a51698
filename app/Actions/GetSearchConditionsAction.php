<?php


namespace App\Actions;


class GetSearchConditionsAction
{

  public function execute($term = ''): array
  {
    return [
      'like' => [
        'label' => 'Contains',
        'operator' => 'LIKE',
        'value' => "%{$term}%"
      ],
      'not_like' => [
        'label' => 'Does Not Contain',
        'operator' => 'NOT LIKE',
        'value' => "%{$term}%"
      ],
      'starts_with' => [
        'label' => 'Begins With',
        'operator' => 'LIKE',
        'value' => "{$term}%"
      ],
      'ends_with' => [
        'label' => 'Ends With',
        'operator' => 'LIKE',
        'value' => "%{$term}"
      ],
      'equal' => [
        'label' => 'Is Equal To',
        'operator' => '=',
        'value' => $term
      ],
      'not_equal' => [
        'label' => 'Is Not Equal To',
        'operator' => '<>',
        'value' => $term
      ],
      'less_than' => [
        'label' => 'Is Less Than',
        'operator' => '<',
        'value' => $term
      ],
      'greater_than' => [
        'label' => 'Is Greater Than',
        'operator' => '>',
        'value' => $term
      ],
      'less_than_equal' => [
        'label' => 'Is Less Than or Equal To',
        'operator' => '<=',
        'value' => $term
      ],
      'greater_than_equal' => [
        'label' => 'Is Greater Than or Equal To',
        'operator' => '>=',
        'value' => $term
      ],
      'between' => [
        'label' => 'Is Between',
        'operator' => 'BETWEEN',
        'value' => $term
      ],
      'not_between' => [
        'label' => 'Is Not Between',
        'operator' => 'NOT BETWEEN',
        'value' => $term
      ],
      'empty' => [
        'label' => 'Is Empty',
        'operator' => 'IS NULL',
        'value' => $term
      ],
      'not_empty' => [
        'label' => 'Is Not Empty',
        'operator' => 'IS NOT NULL',
        'value' => $term
      ]
    ];
  }
}
