<?php


namespace App\Traits;


use Illuminate\Support\Facades\DB;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Quotation;
use Money\Money;

/**
 * Trait UpdatesImportedQuotationAmounts
 * @package App\Traits
 * @codeCoverageIgnore
 */
trait UpdatesImportedQuotationAmounts
{
  public function updateQuotationAmounts(Quotation $quotation, $taxable = true, $tax = 0): bool
  {
    $serviceCost = Money::USD(str_money($quotation->service_cost));
    $itemAmount = Money::USD((str_money($quotation->items->sum('amount'))));
    $amount = $itemAmount->add($serviceCost);
    $taxAmount = Money::USD(str_money($tax));
    $grandTotal = $amount->add($taxAmount);

    $record = [
      'amount' => $amount->getAmount() / 100,
      'tax_amount' => $taxAmount->getAmount() / 100,
      'total_amount' => $grandTotal->getAmount() / 100,
      'taxed' => $taxable,
      'imported' => true,
    ];

    return $quotation->update($record);
  }
}
