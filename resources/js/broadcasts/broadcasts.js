/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allows your team to easily build robust real-time web applications.
 */

import <PERSON> from 'laravel-echo';

window.Pusher = require('pusher-js');

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: process.env.MIX_PUSHER_APP_KEY,
    cluster: process.env.MIX_PUSHER_APP_CLUSTER,
    forceTLS: true
    // wsHost: window.location.hostname,
    // encrypted: true,
    // wsPort: 6001,
    // wssPort: 6001,
    // disableStats: true,
});

if (process.env.NODE_ENV !== 'production') {
    Pusher.logToConsole = true;
}




