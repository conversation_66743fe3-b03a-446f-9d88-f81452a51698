<template>
  <div>
    <p id="typing-paragraph">{{ typingUser.name }} Is Editing...</p>
  </div>
</template>

<script>
export default {
  props: ["user"],

  data() {
    return {
      authUser: {},
      typingUser: {},
      channelName: "order",
      channelOrderInstance: null,
    };
  },

  created() {
    this.authUser = JSON.parse(this.user);
  },

  mounted() {
    this.channelOrderInstance = Echo.private(this.channelName);

    this.listenWhoIsTyping();

    this.listennigStaffAction();

    $("#typing-paragraph").hide();
  },

  destroyed() {
    Echo.leave(this.channelName);
  },
  methods: {
    listennigStaffAction() {
      $(document).on("click keyup keypress", this.broadcastAction);
    },

    broadcastAction() {
      this.channelOrderInstance.whisper("order-editing-action", {
        name: this.authUser.name,
        id: this.authUser.id,
      });
    },

    listenWhoIsTyping() {
      this.channelOrderInstance.listenForWhisper(
        "order-editing-action",
        (staff) => {
          this.typingUser = staff;
          this.displayAction();
        }
      );
    },

    displayAction() {
      $("#typing-paragraph").show().delay(5000).fadeOut();
    },
  },
};
</script>

<style>
#typing-paragraph {
  color: green;
}
</style>