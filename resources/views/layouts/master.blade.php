<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- CSRF Token -->
  <meta name="csrf-token" content="{{ csrf_token() }}">

  <title>@yield('title') - {{ config('app.name') }}</title>

  <!-- App favicon -->
  <link rel="shortcut icon" href="{{ asset('images/favicon.ico') }}" type="image/x-icon">
  <link rel="icon" href="{{ asset('images/favicon.ico') }}" type="image/x-icon">
  <!-- inline style to handle loading of various element-->
  <style>body.loading {
      visibility: hidden;
    }</style>

  <!-- App css -->
  <link href="{{ asset('css/icons.min.css') }}" rel="stylesheet" type="text/css"/>
  <link href="{{ asset('css/app.css') }}" rel="stylesheet" type="text/css" id="main-style-container"/>
  @stack('styles')
</head>
<body>

<!-- Begin page -->
<div id="app" class="wrapper" data-locked="{{ auth()->user()->screen_locked ? 1 : 0 }}">
@include('layouts.partials.sidebar')

<!-- ============================================================== -->
  <!-- Start Page Content here -->
  <!-- ============================================================== -->

  <div class="content-page">
    <div class="content">
    @include('layouts.partials.topbar')

    @include('shared.notifications')
    <!-- Start Content-->
    @yield('content')
    <!-- container -->

    </div>
    <!-- content -->

    @include('layouts.partials.footer')

    <div id="lockScreen" class="modal fade bg-primary-lighten" tabindex="-1" role="dialog" style="display: none;"
         aria-hidden="true">
      <div class="modal-dialog modal-sm">
        <div class="modal-content modal-filled bg-primary">
          <div class="modal-body p-4">
            <div class="text-center">
              <i class="dripicons-lock h1"></i>
              <h4 class="mt-2">Screen Locked!</h4>
              <p class="mt-3">You have been logged out due to inactivity. Enter your login credentials to unlock
                screen.</p>
              <form action="{{ route('auth.unlock') }}" method="post" id="unlock-screen-form">
                @csrf
                <div class="form-group">
                  <label for="user-email" class="text-left">Email</label>
                  <input class="form-control mb-3"
                         id="user-email"
                         type="email"
                         name="email"
                         value="{{ auth()->check() ? auth()->user()->email : '' }}"
                         placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                  <label for="user-password" class="text-left">Enter your password</label>
                  <input class="form-control mb-3" id="user-password"
                         type="password" name="password"
                         placeholder="*******************">
                </div>
                <div class="bg-danger mb-2 d-none py-2" id="lock-screen-error-message">
                  There was a problem unlocking screen. Try to <a
                    class="font-weight-bold"
                    href="{{ route('login') }}">Login</a> instead.
                </div>
                <div class="d-flex flex-column">
                  <button type="submit" class="btn btn-light btn-block">Unlock Screen!</button>
                  <span class="my-1">OR</span>
                  <a
                    class="btn btn-outline-light btn-block"
                    href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Login / Switch User</a>
                </div>
              </form>
            </div>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


  </div>

  <!-- ============================================================== -->
  <!-- End Page content -->
  <!-- ============================================================== -->

</div>
<!-- END wrapper -->

<!-- bundle -->
<script src="{{ asset('js/vendor/axios.min.js') }}"></script>
<script src="{{ asset('js/hyper.js') }}"></script>
<script src="{{ asset('js/app.js') }}"></script>
@stack('js')
</body>
</html>
