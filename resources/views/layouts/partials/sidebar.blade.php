<!-- ========== Left Sidebar Start ========== -->
<div class="left-side-menu">

  <div class="slimscroll-menu" id="left-side-menu-container">

    <!-- LOGO -->
    <a href="{{ url('/') }}" class="logo text-center">
      <span class="logo-lg">
          <img
            src="{{ asset('images/logo.png') }}"
            alt="{{ config('app.name') }} logo"
            id="side-main-logo">
      </span>
      <span class="logo-sm">
        <img
          src="{{ asset('images/logo_sm.png') }}"
          alt="{{ config('app.name') }} logo"
          height="20"
          id="side-sm-main-logo">
      </span>
    </a>

    <!--- Sidemenu -->
    <ul class="metismenu side-nav">

      <li class="side-nav-title side-nav-item">Main Menu</li>

      <li class="side-nav-item">
        <a href="{{ url('/') }}" class="side-nav-link">
          <i class="dripicons-meter"></i>
          <span> Dashboard </span>
        </a>
      </li>
      @can('view notification')
        <li class="side-nav-item">
          <a href="javascript: void(0);" class="side-nav-link">
            <i class="dripicons-bell"></i>
            <div class="d-inline-block flex align-items-center"><span>Notification</span>
              <span
                class="badge badge-danger ml-1 mt-0">{{ $notificationCounts->sum() === 0 ? '' : $notificationCounts->sum() }}</span>
            </div>
            <span class="menu-arrow"></span>
          </a>
          <ul class="side-nav-second-level" aria-expanded="false">
            <li>
              <a
                href="{{ route('orderModule.notifications.index', ['type' => 'Invoice Printing']) }}">
                Invoice Printing
                <span
                  class="badge badge-danger float-right">{{ $notificationCounts['Invoice Printing'] ?? '' }}</span>
              </a>
            </li>
            <li>
              <a
                href="{{ route('orderModule.notifications.index', ['type' => 'Finished Order']) }}">
                Finished Order
                <span
                  class="badge badge-danger float-right">{{ $notificationCounts['Finished Order'] ?? '' }}</span>
              </a>
            </li>
            <li>
              <a
                href="{{ route('orderModule.notifications.index', ['type' => 'Back Order']) }}">
                Back Orders
                <span
                  class="badge badge-danger float-right">{{ $notificationCounts['Back Order'] ?? '' }}</span>
              </a>
            </li>
            <li>
              <a
                href="{{ route('orderModule.notifications.index', ['type' => 'General']) }}">
                General Notifications
                <span
                  class="badge badge-danger float-right">{{ $notificationCounts['General'] ?? '' }}</span>
              </a>
            </li>
          </ul>
        </li>
      @endcan

      @can('view customer')
        <li>
          <a href="{{ route('users.customers.index') }}" class="side-nav-link">
            <i class="dripicons-user-group"></i>
            <span>Customers</span>
          </a>
        </li>
      @endcan

      @can('view vendor')
        <li class="side-nav-item">
          <a href="{{ route('general.vendors.index') }}" class="side-nav-link">
            <i class="dripicons-user-group"></i>
            <span> Vendors </span>
          </a>
          </a>
        </li>
      @endcan

      @can('view purchase order')
        <li class="side-nav-item">
          <a href="javascript: void(0);" class="side-nav-link">
            <i class="mdi mdi-carry-on-bag-check"></i>
            <div class="d-inline-block flex align-items-center">
              <span>Purchase Orders</span></div>
            <span class="menu-arrow"></span>
          </a>
          <ul class="side-nav-second-level" aria-expanded="false">
            <li>
              <a href="{{ route('general.purchase-orders.received.index') }}">
                Received
              </a>
            </li>
            <li>
              <a href="{{ route('general.purchase-orders.index') }}">
                Show All
              </a>
            </li>
            <li>
              <a href="{{ route('general.purchase-order-requests.create') }}">
                Need to Order
              </a>
            </li>
          </ul>
        </li>
      @endcan
    <!-- General Menu -->
      @canany(['view category', 'view vendor', 'manage settings'])
        <li class="side-nav-item">
          <a href="javascript: void(0);" class="side-nav-link">
            <i class="dripicons-star"></i>
            <span> General </span>
            <span class="menu-arrow"></span>
          </a>
          <ul class="side-nav-second-level" aria-expanded="false">
            @can('view category')
              <li>
                <a href="{{ route('general.categories.index') }}">Categories</a>
              </li>
            @endcan
            @can('manage settings')
              <li>
                <a href="{{ route('general.settings.index') }}">Settings</a>
              </li>
            @endcan
          </ul>
        </li>
      @endcanany
      @canany(['view order', 'view quote'])
        <li class="side-nav-item">
          <a href="javascript: void(0);" class="side-nav-link">
            <i class="dripicons-cart"></i>
            <span> Order Management </span>
            <span class="menu-arrow"></span>
          </a>
          <ul class="side-nav-second-level" aria-expanded="false">
            @can('view order')
              <li>
                <a href="{{ route('orderModule.orders.index') }}">Orders</a>
              </li>
              <li>
                <a href="{{ route('orderModule.unassigned.index') }}">Orders Created, Not
                  Printed</a>
              </li>
              <li>
                <a href="{{ route('orderModule.completed.index') }}">Ready to Invoice</a>
              </li>
              <li>
                <a href="{{ route('orderModule.backOrders.index') }}">Back Orders</a>
              </li>
              <li>
                <a href="{{ route('orderModule.orders.archived.index') }}">Archived Orders</a>
              </li>
            @endcan
            @can('view quote')
              <li>
                <a href="{{ route('orderModule.quotations.index') }}">Quotations</a>
              </li>
            @endcan
            @can('view commission')
              <li>
                <a href="{{ route('orderModule.commissions.index') }}">Commissions</a>
              </li>
            @endcan
          </ul>
        </li>
      @endcanany
      @canany(['view invoice', 'view statement', 'batch print statement', 'delete payment', 'view refund'])
        <li class="side-nav-item">
          <a href="javascript: void(0);" class="side-nav-link">
            <i class="dripicons-shopping-bag"></i>
            <span> Receivables </span>
            <span class="menu-arrow"></span>
          </a>

          <ul class="side-nav-second-level" aria-expanded="false">
            @can('view invoice')
              <li>
                <a href="{{ route('orderModule.invoices.index') }}">Invoices</a>
              </li>
              <li>
                <a href="{{ route('orderModule.invoices.recent.index') }}">Invoices past 30days</a>
              </li>
              <li>
                <a href="{{ route('orderModule.invoiced-orders.index') }}">Invoiced Orders</a>
              </li>
            @endcan
            @can('view statement')
              <li>
                <a href="{{ route('users.statements.index') }}">Statements</a>
              </li>
            @endcan
            @can('batch print statement')
              <li>
                <a href="{{ route('users.statements.zipped.index') }}">Zipped Batch
                  Statements</a>
              </li>
              <li>
                <a href="{{ route('orderModule.zipped-invoiced-orders.index') }}">Zipped Invoice Orders</a>
              </li>
            @endcan
            @can('delete payment')
              <li>
                <a href="{{ route('users.approvals.index') }}">Payments Deleted</a>
              </li>
            @endcan
            @can('view refund')
              <li>
                <a href="{{ route('users.refunds.index') }}">Refunds</a>
              </li>
            @endcan
          </ul>
        </li>
      @endcanany
    <!-- Inventory Menu -->
      @canany(['view item', 'view store', 'view level'])
        <li class="side-nav-item">
          <a href="javascript: void(0);" class="side-nav-link">
            <i class="dripicons-basket"></i>
            <span> Inventory </span>
            <span class="menu-arrow"></span>
          </a>
          <ul class="side-nav-second-level" aria-expanded="false">
            @can('view item')
              <li>
                <a href="{{ route('inventory.items.index') }}">Items</a>
              </li>
              <li>
                <a href="{{ route('inventory.items.count.index') }}">Items Count List</a>
              </li>
              <li>
                <a href="{{ route('inventory.items.outOfStock') }}">Items Out of Stock</a>
              </li>
            @endcan
            @can('view store')
              <li>
                <a href="{{ route('inventory.stores.index') }}">Stores</a>
              </li>
            @endcan
            @can('view level')
              <li>
                <a href="{{ route('inventory.levels.index') }}">Price Levels</a>
              </li>
            @endcan
          </ul>
        </li>
      @endcanany
    <!-- Users -->
      @canany(['view customer', 'view preference', 'view staff', 'view role', 'view log'])
        <li class="side-nav-item">
          <a href="javascript: void(0);" class="side-nav-link">
            <i class="dripicons-user-group"></i>
            <span> Users </span>
            <span class="menu-arrow"></span>
          </a>
          <ul class="side-nav-second-level" aria-expanded="false">
            @can('view customer')
              <li>
                <a href="{{ route('users.customers.inactive.index') }}">
                  Inactive Customers
                </a>
              </li>
              <li>
                <a href="{{ route('users.customers.rentals.index') }}">
                  Rental Customers
                </a>
              </li>
              <li>
                <a href="{{ route('users.customers.exRentals.index') }}">
                  Ex-Rental Customers
                </a>
              </li>
            @endcan
            @can('view staff')
              <li><a href="{{ route('users.staff.index', ['active' => true]) }}">Active
                  Staff</a></li>
              <li><a href="{{ route('users.staff.index', ['active' => false]) }}">Inactive
                  Staff</a></li>
            @endcan

            @can('view department')
              <li>
                <a href="{{ route('users.departments.index') }}">Departments</a>
              </li>
            @endcan
            @can('view role')
              <li>
                <a href="{{ route('users.roles.index') }}">Roles</a>
              </li>
            @endcan
            @can('view activity')
              <li>
                <a href="{{ route('users.audits.index') }}">Activity Log</a>
              </li>
            @endcan
          </ul>
        </li>
      @endcan

      @can('view report')
        <li class="side-nav-item">
          <a href="javascript: void(0);" class="side-nav-link">
            <i class="mdi mdi-paperclip"></i>
            <span> Reports </span>
            <span class="menu-arrow"></span>
          </a>
          <ul class="side-nav-second-level" aria-expanded="false">
            <li>
              <a href="{{ route('orderModule.reports.customerInvoiceSummary.index') }}">
                Customer Invoice Summary
              </a>
            </li>
            <li>
              <a href="{{ route('users.customers.reports.sales.index') }}">
                Customer Sales
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.paymentReceipts.create') }}">
                Payment Receipts
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.paymentDeposits.create') }}">
                Payment Deposits
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.pendingOrders.index') }}">
                Pending Orders
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.openInvoices.index') }}">
                Open Invoices
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.salesTax.index') }}">
                Sales Tax
              </a>
            </li>
            <li>
              <a href="{{ route('general.statistics.index') }}">
                Statistics
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.commissions.commercialRO.index') }}">
                Commercial RO Commission
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.commissions.wes.index') }}">
                Wesley Commission
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.commissions.woodruff.index', ['standard' => true]) }}">
                Woodruff Sales Commission
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.commissions.woodruff.index', ['standard' => false]) }}">
                Woodruff Sales for Commercial Softeners Commission
              </a>
            </li>
            <li>
              <a href="{{ route('orderModule.reports.commissions.referrals.index') }}">
                Referrals Commission
              </a>
            </li>
            <li>
              <a href="{{ route('general.email-log.index') }}">
                Email Log
              </a>
            </li>
          </ul>
        </li>
      @endcan
    </ul>
    <!-- End Sidebar -->

    <div class="clearfix"></div>
  </div>
  <!-- Sidebar -left -->

</div>
<!-- Left Sidebar End -->
