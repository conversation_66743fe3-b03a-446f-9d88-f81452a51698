<div
  class="relative"
  wire:ignore
  x-data
  x-init="
        FilePond.registerPlugin(FilePondPluginFileValidateType);
        FilePond.setOptions({
            server: {
                process: (fieldName, file, metadata, load, error, progress, abort, transfer, options) => {
                    @this.upload('{{ $attributes['wire:model'] }}', file, load, error, progress);
                },
                revert: (filename, load) => {
                    @this.removeUpload('{{ $attributes['wire:model'] }}', filename, load);
                }
            }
        });

        var pond = FilePond.create($refs.fileInput, {
            allowMultiple: {{ isset($attributes['multiple']) ? 'true' : 'false' }},
            credits: {},
            required: true,
            maxFiles: 10
        });

        this.addEventListener('pond-reset', e => {
            pond.removeFiles();
        })
    "
>
  <input class="form-control-file" type="file" x-ref="fileInput" multiple/>
</div>
