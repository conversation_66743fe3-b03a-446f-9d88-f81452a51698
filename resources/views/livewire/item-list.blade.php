<div>
  <button class="btn btn-link {{ $advancedSearchButtonDisplayStatus }}" type="button" id="advanced-search-btn"
          wire:click="changeAdvancedSearchDisplay()">{{ $advancedSearchButtonText }}</button>
  <div class="{{ $advancedSearchDisplayStatus }}" id="advanced-search-wrapper">
    <form class="mb-3" enctype="multipart/form-data">
      <fieldset>
        <div class="form-row">
          <div class="col-md-4">
            <select
              name="fields[]"
              class="form-control search-field" wire:model="searchFields.0">
              @foreach($searchFieldOptions as $fieldKey => $fieldName)
                <option value="{{ $fieldKey }}">{{ $fieldName }}</option>
              @endforeach
            </select>
          </div>
          <div class="col-md-3">
            <select
              name="conditions[]"
              class="form-control search-condition">
              @foreach($searchConditionOptions as $conditionKey => $condition)
                <option value="{{ $conditionKey }}">{{ $condition['label'] }}</option>
              @endforeach
            </select>
          </div>
          <div class="col-md-3">
            <input class="form-control search-term" name="search_terms[]" wire:model="searchTerms.0">
            <div class="small terms-description">
              E.g. 1000 AND 5000 (only for Between and Not Between conditions)
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <button type="button" class="btn btn-link duplicate-btn"
                      wire:click.prevent="addConditionInput({{$conditionInputIndex}})"><i class="mdi mdi-plus"></i> Add
              </button>
            </div>
          </div>
        </div>
        @foreach($conditionInputs as $inputKey => $conditionInput)
          <div class="form-row">
            <div class="col-md-4">
              <select
                name="fields[]"
                class="form-control search-field" wire:model="searchFields.{{ $conditionInput }}">
                @foreach($searchFieldOptions as $fieldKey => $fieldName)
                  <option value="{{ $fieldKey }}">{{ $fieldName }}</option>
                @endforeach
              </select>
            </div>
            <div class="col-md-3">
              <select
                name="conditions[]"
                class="form-control search-condition" wire:model="searchConditions.{{ $conditionInput }}">
                @foreach($searchConditionOptions as $conditionKey => $condition)
                  <option value="{{ $conditionKey }}">{{ $condition['label'] }}</option>
                @endforeach
              </select>
            </div>
            <div class="col-md-3">
              <input class="form-control search-term" name="terms[]" wire:model="searchTerms.{{ $conditionInput }}">
              <div class="small terms-description">
                E.g. 1000 AND 5000 (only for Between and Not Between conditions)
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <button type="button" class="btn btn-link remove-duplicate-btn text-danger"
                        wire:click.prevent="removeConditionInput({{$conditionInputIndex}})"><i
                    class="mdi mdi-close"></i> Remove
                </button>
              </div>
            </div>
          </div>
        @endforeach
      </fieldset>

      <div class="text-right">
        <button class="btn btn-outline-secondary" type="button" id="clear-advanced-form-btn"
                wire:click.prevent="clearConditionInputs()"><i class="mdi mdi-close"></i> Clear
        </button>
      </div>
    </form>
  </div>
  <div class="table-responsive">
    <table class="table table-centered w-100 dt-responsive nowrap" id="items-table">
      <thead class="thead-light">
      <tr>
        <th>Part#</th>
        <th>Vendor Part#</th>
        <th>Description</th>
        <th>Category</th>
        <th class="text-right">Our Cost</th>
        <th class="text-right">List Price</th>
      </tr>
      </thead>
      <tbody>
      @foreach($items as $item)
        <tr>
          <td><a class="text-info" href="{{ route('inventory.items.show', $item->id) }}">{{ $item->code }}</a></td>
          <td>{{ $item->vendor_part_number }}</td>
          <td>{{ $item->description }}</td>
          <td>{{ $item->category_name }}</td>
          <td class="text-right">{{ number_format($item->cost, 2) }}</td>
          <td class="text-right">{{ number_format($item->list_price, 2) }}</td>
        </tr>
      @endforeach
      </tbody>
    </table>
  </div>
  {{ $items->links() }}
</div>
