<tr>
  <td style="border-bottom: 1px solid #333;">
    <input class="border" type="date" wire:model="customOrderDate" style="max-width: 150px;">
  </td>
  @if(in_array($commissionType, ['WS', 'WS_CS']))
    <td style="border-bottom: 1px solid #333;">
      <input class="border" type="text" wire:model="customOrderFullNumber" style="max-width: 150px;">
    </td>
  @endif
  <td style="border-bottom: 1px solid #333;">
    <input class="border" type="text" wire:model="customPurchaseOrderNumber" style="max-width: 150px;">
  </td>
  <td class="text-right" style="border-bottom: 1px solid #333;">
    <input class="border" type="number" step="0.01" wire:model="customQuantity" placeholder="1" style="max-width: 150px;">
  </td>
  <td class="" style="border-bottom: 1px solid #333;">
    <input class="border" type="text" wire:model="customDescription" placeholder="Description">
  </td>
  <td class="" style="border-bottom: 1px solid #333;">
    <input class="border" type="text" wire:model="customPartNumber" placeholder="part#">
    @error('customPartNumber') <span class="error">{{ $message }}</span> @enderror
  </td>
  <td class="text-right" style="border-bottom: 1px solid #333;">
    <input class="border" type="number" step="0.01" wire:model="customPrice" placeholder="0.00">
  </td>
  <td class="text-right" style="border-bottom: 1px solid #333;">{{ $customAmount ?? 0 }}</td>
  <td class="text-right" style="border-bottom: 1px solid #333;">
    <input class="border" type="number" step="0.01" wire:model="customCommission" placeholder="0.00">
    @error('customCommission') <span class="error">{{ $message }}</span> @enderror
  </td>
  <td class="" style="border-bottom: 1px solid #333;">
    <input class="border" type="date" wire:model="customFinalDate" style="max-width: 150px;">
  </td>
  <td class="" style="border-bottom: 1px solid #333;">
    <input class="border" type="text" wire:model="customInvoiceNumber" style="max-width: 150px;">
  </td>
  <td style="border-bottom: 1px solid #333; padding-bottom: 15px;" class="d-flex align-items-center">
    <button type="button" class="btn btn-outline-info text-decoration-none btn-sm px-1 py-0" wire:click="addCustomEntry"><i class="mdi mdi-check"></i></button>
    <button type="button" class="btn btn-outline-secondary text-decoration-none btn-sm px-1 py-0" wire:click="$set('showCustomEntryForm', false)"><i class="mdi mdi-cancel"></i></button>
  </td>
</tr>
