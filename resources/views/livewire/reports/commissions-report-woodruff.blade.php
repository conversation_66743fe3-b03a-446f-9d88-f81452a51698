<div>
  @include('livewire.reports.partials.commission-actions')

  <div class="table-responsive">
    <table class="table table-sm mb-0">
      <tr>
        <th class="bg-transparent" style="border-bottom: 3px solid #333;">Order Date</th>
        <th class="bg-transparent" style="border-bottom: 3px solid #333;">Order#</th>
        <th class="bg-transparent" style="border-bottom: 3px solid #333;">PO#</th>
        <th class="bg-transparent text-right" style="border-bottom: 3px solid #333;">Qty</th>
        <th class="bg-transparent" style="border-bottom: 3px solid #333;">Description</th>
        <th class="bg-transparent" style="border-bottom: 3px solid #333;">Part#</th>
        <th class="bg-transparent text-right" style="border-bottom: 3px solid #333;">Price</th>
        <th class="bg-transparent text-right" style="border-bottom: 3px solid #333;">Total</th>
        <th class="bg-transparent text-right" style="border-bottom: 3px solid #333;">Comm</th>
        <th class="bg-transparent" style="border-bottom: 3px solid #333;">Final Date</th>
        <th class="bg-transparent" style="border-bottom: 3px solid #333;">Invoice #</th>
        <th class="bg-transparent" style="border-bottom: 3px solid #333;"></th>
      </tr>
      @php $totalCommissions = 0; $totalOrders = 0; @endphp
      @foreach($commissionGroups as $commissionGroup)
        @php
          $totalCommissions += $commissionGroup->sum('commission');
          $totalOrders += $commissionGroup->sum('amount');
        @endphp
        @foreach($commissionGroup as $commission)
          @if($loop->first)
          <tr>
            <td style="border-bottom: 1px solid #333;">
              {{ $commission->order->formatted_date }}
            </td>
            <td style="border-bottom: 1px solid #333;">
              {{ $commission->order->full_number }}
            </td>
            <td style="border-bottom: 1px solid #333;">
              {{ $commission->order->purchase_order_number }}
            </td>
            <td class="text-right" style="border-bottom: 1px solid #333;">
              {{ $commission->quantity }}
            </td>
            <td class="" style="border-bottom: 1px solid #333;">
              {{ $commission->description }}
            </td>
            <td class="" style="border-bottom: 1px solid #333;">
              {{ $commission->part_number }}
            </td>
            <td class="text-right" style="border-bottom: 1px solid #333;">
              {{ number_format($commission->price, 2) }}
            </td>
            <td class="text-right" style="border-bottom: 1px solid #333;">
              {{ number_format($commission->amount, 2) }}
            </td>
            <td class="text-right" style="border-bottom: 1px solid #333;">
              {{ number_format($commission->amount * ($commissionDiscount / 100), 2) }}
            </td>
            <td class="" style="border-bottom: 1px solid #333;">
              {{ $commission->order->formatted_invoice_date }}
            </td>
            <td class="" style="border-bottom: 1px solid #333;">
              {{ $commission->order->number }}
            </td>
            <td style="border-bottom: 1px solid #333;">
              <a
                class="text-muted delete-item-btn"
                href="#"
                data-toggle="modal"
                data-target="#deleteItem"
                data-url="{{ route('orderModule.commissions.destroy', $commission)}}">
                <i class="mdi mdi-delete"></i>
              </a>
            </td>
          </tr>
          @else
          <tr>
            <td style="border-bottom: 1px solid #333;" colspan="3"></td>
            <td class="text-right" style="border-bottom: 1px solid #333;">
              {{ $commission->quantity }}
            </td>
            <td class="" style="border-bottom: 1px solid #333;">
              {{ $commission->description }}
            </td>
            <td class="" style="border-bottom: 1px solid #333;">
              {{ $commission->part_number }}
            </td>
            <td class="text-right" style="border-bottom: 1px solid #333;">
              {{ number_format($commission->price, 2) }}
            </td>
            <td class="text-right" style="border-bottom: 1px solid #333;">
              {{ number_format($commission->amount, 2) }}
            </td>
            <td class="text-right" style="border-bottom: 1px solid #333;">
              {{ number_format($commission->amount * ($commissionDiscount / 100), 2) }}
            </td>
            <td class="" style="border-bottom: 1px solid #333;">
              {{ $commission->order->formatted_invoice_date }}
            </td>
            <td class="" style="border-bottom: 1px solid #333;">
              {{ $commission->order->number }}
            </td>
            <td style="border-bottom: 1px solid #333;">
              <a
                class="text-muted delete-item-btn"
                href="#"
                data-toggle="modal"
                data-target="#deleteItem"
                data-url="{{ route('orderModule.commissions.destroy', $commission)}}">
                <i class="mdi mdi-delete"></i>
              </a>
            </td>
          </tr>
          @endif

        @endforeach
        <tr>
          <td colspan="4">
            <strong class="text-uppercase">{{ $commission->order->customer->name }}</strong>
          </td>
          <td class="text-right pb-3" colspan="4">
            <strong>Order Total Less Sales Tax: {{ number_format($commissionGroup->sum('amount'), 2) }}</strong>
          </td>
          <td colspan="4"></td>
        </tr>
      @endforeach

      @includeWhen($customCommissions->count(), 'livewire.reports.partials.custom-commissions')

      @includeWhen($showCustomEntryForm, 'livewire.reports.partials.custom-commission-form')

      <tr>
        <td colspan="11" style="border-bottom: 1px solid #333;"></td>
      </tr>
      <tr>
        <td class="text-right" colspan="8">
          <strong>Report Total: ${{ number_format($totalOrders, 2)}}</strong><br>
          <strong>{{ $commissionDiscount }}% Total Commission: ${{ number_format($totalOrders * ($commissionDiscount / 100), 2)}}</strong>
        </td>
        <td colspan="4"></td>
      </tr>
    </table>
  </div>
</div>
