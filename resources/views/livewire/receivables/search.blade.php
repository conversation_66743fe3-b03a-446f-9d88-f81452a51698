<div>
  <button
    class="btn btn-link {{ $showAdvancedForm ? '' : 'd-none' }}"
    type="button"
    wire:click="$set('showAdvancedForm', false)"><i class="mdi mdi-chevron-up"></i> Hide Advanced Search
  </button>
  <button
    class="btn btn-link {{ $showAdvancedForm ? 'd-none' : '' }}"
    type="button"
    wire:click="$set('showAdvancedForm', true)">
    <i class="mdi mdi-chevron-down"></i> Show Advanced Search
  </button>
  <div class="{{ $showAdvancedForm ? '' : 'd-none' }}">
    <div class="form-row">
      <div class="col-md-4">
        <select
          class="form-control search-field" wire:model="fields.0">
          @foreach($searchFields as $fieldKey => $fieldName)
            <option value="{{ $fieldKey }}">{{ $fieldName }}</option>
          @endforeach
        </select>
      </div>
      <div class="col-md-3">
        <select
          class="form-control search-condition" wire:model="conditions.0">
          @foreach($searchConditions as $conditionKey => $condition)
            <option value="{{ $conditionKey }}">{{ $condition['label'] }}</option>
          @endforeach
        </select>
      </div>
      <div class="col-md-3">
        <input class="form-control search-term" wire:model="terms.0">
        <div class="d-none small">
          E.g. 1000 AND 5000
        </div>
      </div>
      <div class="col-md-2">
        <div class="form-group">
          <button type="button" class="btn btn-link" wire:click="addFilter({{$i}})">
            <i class="mdi mdi-plus"></i>
            Add
          </button>
        </div>
      </div>
    </div>
    @foreach($inputs as $key => $value)
      <div class="form-row">
        <div class="col-md-4">
          <select
            class="form-control search-field" wire:model="fields.{{ $value }}">
            @foreach($searchFields as $fieldKey => $fieldName)
              <option value="{{ $fieldKey }}">{{ $fieldName }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-3">
          <select
            class="form-control search-condition"
            wire:model="conditions.{{ $value }}">
            @foreach($searchConditions as $conditionKey => $condition)
              <option value="{{ $conditionKey }}">{{ $condition['label'] }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-3">
          <input class="form-control search-term" wire:model="terms.{{ $value }}">
          <div class="d-none small terms-description">
            E.g. 1000 AND 5000
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <button
              type="button"
              class="btn btn-link text-danger"
              wire:click="removeFilter({{ $key }})">
              <i class="mdi mdi-close"></i> Remove
            </button>
          </div>
        </div>
      </div>
    @endforeach

    <div class="row">
      <div class="col-10 text-right">
        <button
          class="btn btn-sm btn-outline-secondary"
          type="button"
          wire:click="clearFilter">
          <i class="mdi mdi-close"></i>
          Clear
        </button>
        <button
          class="btn btn-sm btn-outline-primary"
          type="button" wire:click="search">
          <i class="mdi mdi-account-search"></i> Search Now
        </button>
      </div>
    </div>
  </div>
</div>
