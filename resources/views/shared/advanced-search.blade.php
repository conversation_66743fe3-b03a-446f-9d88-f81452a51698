@if(request()->filled('fields'))
  <button class="btn btn-link" type="button" id="advanced-search-btn"><i class="mdi mdi-chevron-up"></i> Hide Advanced
    Search
  </button>
@else
  <button class="btn btn-link" type="button" id="advanced-search-btn"><i class="mdi mdi-chevron-down"></i> Show Advanced
    Search
  </button>
@endif
<div class="{{ ! request()->filled('fields') ? 'd-none' : '' }}" id="advanced-search-wrapper">
  <form class="mb-3" enctype="multipart/form-data">
    <fieldset>
      <div class="form-row duplicate">
        <div class="col-md-4">
          <select
            name="fields[]"
            class="form-control search-field">
            {{--                                                        <option value="all">Any field</option>--}}
            @foreach($searchFields as $fieldKey => $fieldName)
              <option value="{{ $fieldKey }}">{{ $fieldName }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-3">
          <select
            name="conditions[]"
            class="form-control search-condition">
            @foreach($searchConditions as $conditionKey => $condition)
              <option value="{{ $conditionKey }}">{{ $condition['label'] }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-3">
          <input class="form-control search-term" name="terms[]">
          <div class="d-none small terms-description">
            E.g. 1000 AND 5000
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none"><i
                class="mdi mdi-close"></i> Remove
            </button>
            <button type="button" class="btn btn-link duplicate-btn" data-id="duplicate0"><i class="mdi mdi-plus"></i>
              Add
            </button>
          </div>
        </div>
      </div>
    </fieldset>
    @if(Route::is('users.customers.index'))
    <fieldset>
      <div class="form-row">
        <div class="col d-flex">
          <label class="mr-2" for="switch0">Has Special Price items</label>
          <input type="checkbox"
                 id="switch0"
                 data-switch="none"
                 value="1"
                 name="has_special_price_items" {{ request('has_special_price_items') === '1' ? 'checked' : '' }}>
          <label for="switch0" data-on-label="Yes" data-off-label="No"></label>
        </div>
      </div>
    </fieldset>
    @endif
    <div class="text-right">
      <button class="btn btn-outline-primary" type="submit"><i class="mdi mdi-account-search"></i> Search Now</button>
      <button class="btn btn-outline-secondary" type="button" id="clear-advanced-form-btn"><i class="mdi mdi-close"></i>
        Clear
      </button>
    </div>
  </form>
</div>
@include('inventory::shared.forms.duplicate')
@push('js')
  <script>
    (function () {
      $(document).on('change', '.search-condition', function () {
        let conditionElement = $(this);
        if (conditionElement.val() === 'between' || conditionElement.val() === 'not_between') {
          conditionElement.parent().parent().find('.terms-description').removeClass('d-none');
        } else {
          conditionElement.parent().parent().find('.terms-description').addClass('d-none');
        }
      }).on('click', '#clear-advanced-form-btn', function () {
        window.location = window.location.origin + window.location.pathname;
      }).on('click', '#advanced-search-btn', function () {
        let advancedSearchWrapper = $('#advanced-search-wrapper');
        if (advancedSearchWrapper.hasClass('d-none')) {
          advancedSearchWrapper.slideToggle(function () {
            $(this).removeClass('d-none');
          });
          $(this).html('<i class="mdi mdi-chevron-up"></i> Hide Advanced Search');
        } else {
          advancedSearchWrapper.slideToggle(function () {
            $(this).addClass('d-none');
          });

          $(this).html('<i class="mdi mdi-chevron-down"></i> Show Advanced Search');
        }
      });
    }());
  </script>
@endpush
