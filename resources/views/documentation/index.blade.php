@extends('layouts.docs')

@section('title', 'Documentation')

@prepend('styles')
  <!-- third party css -->
  <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <!-- third party css end -->
@endprepend

@section('content')
  <!-- Start Content-->
  <div class="container-fluid bg-white">

    <!-- start page title -->
    <div class="row mb-5">
      <div class="col-12 pt-4 px-5 font-16">
        <h1 class="mb-4">{{ config('app.name') }} System Documentation</h1>
        <article>
          <h1>User Accounts</h1>
          <p>In any web application, users are needed to perform actions and thus achieve the functions available
            therein. This system is no different. There are currently three types of supported accounts, that is,
            <strong>Staff</strong> and <strong>Customers</strong>. Currently only users with a <strong>Staff</strong>
            account have access to the system using their respective credentials to log in.</p>
          <h2 id="user-sessions">User Sessions</h2>
          <p>A session is created for each user whenever the user accesses the system by logging in with their credentials (username and password). This session maintains the details of the user i.e. account details and activity within the system. The activities of the user depend on the permissions that each user is assigned. However some of the activities will be related to:</p>
          <ul>
            <li>Creating an order</li>
            <li>Invoicing an order</li>
            <li>Printing a shop order</li>
            <li>Cancelling an order</li>
            <li>Assigning a Staff on an Order</li>
            <li>Creating a payment record for an invoice</li>
            <li>Requesting to delete a payment</li>
            <li>Requisitioning a Purchase Order</li>
            <li>Printing a Purchase Order</li>
            <li>Uploading an Acknowledgement to a Purchase Order</li>
            <li>Generating a Sales Tax Report</li>
            <li>Transferring Credit from on Customer Account to another</li>
          </ul>
          <h2 id="customer-account">Customer Account</h2>
          <p>Every customer has an account, on which tasks such as placing of orders is accomplished. There are currently four types of Customer Accounts. That is: </p>
          <ul>
            <li>Receivable (Regular Customer)</li>
            <li>Rental</li>
            <li>Ex-Rental</li>
            <li>Inactive Customers</li>
          </ul>
          <p>A customer can be switched from one account type to another by editing their profile.</p>
          <h3 id="customer-credit">Customer Credit</h3>
          <p>Customer Credit is money we owe the Customer. Credit accumulated whenever there's a balance on the payment they make for invoices. This balance is represented as a negative amount e.g <code>-499.99</code>. A customer can then use their credit to pay for invoices that are due.</p>
          <p>When making a payment for an invoice, the credit is shown in the payment method select box, split up into the different payments from which the credit was accumulated. You will see something like: <code>Payment# 160001 ($103.45)</code> in the payment method list. Once you choose the payment with a balance, that balance can be used to settle some or all of the invoices. At the moment it is not possible to use more than one payment to pay for invoices.</p>
          <p>Customer credit will be reduced whenever the balance on a payment is used to pay for invoices.</p>

          <h2 id="staff-account">Staff Accounts</h2>
          <h3 id="staff-departments">Departments</h3>
          <p>Staff can be grouped according to departments to allow for a resemblance to actual departments or sections
            within the human resource of pacific. You can manage departments from the <code>Users -> Departments</code>
            menu</p>
          <h3 id="user-authorization">Roles and Permissions</h3>
          <p>A role is simply a group of permissions assigned to a user. A permission is simply an authorization to
            perform a single action. You can create as many roles as needed. Permissions on the other hand are embedded
            within the system and would thus require a system update. Let's look at what permissions are available and
            their use.</p>
          <h4>Available Permissions</h4>
          <p>We have grouped permissions according to where they are used most so you can see where they will take
            effect. By doing this, we will avoid repetition of explanations of a particular permission by simply
            mentioning it.</p>
          <h5 class="mb-0">Dashboard</h5>
          <ul class="list-group mb-4">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Statistics</h6>
              <p class="mb-0">Allow staff to view statistics or summaries. Such statistics exist on the Dashboard,
                Customer and Staff Profile pages.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Order</h6>
              <p class="mb-0">Allows staff to view list of orders or details of any order.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Invoice</h6>
              <p class="mb-0">Allows staff to view list of invoices or details of any invoice.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Quote</h6>
              <p class="mb-0">Allows staff to view list of quotations or details of any quotation.</p>
            </li>
          </ul>
          <h5 class="mb-0">Notifications</h5>
          <ul class="list-group mb-4">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Notification</h6>
              <p class="mb-0">Allow staff to view system notifications.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Notification</h6>
              <p class="mb-0">Allows staff to delete system notifications. A staff with this notification will need the
                <code>view notification</code> permission as well.</p>
            </li>
          </ul>
          <h5 class="mb-0">Categories</h5>
          <ul class="list-group mb-4">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Category</h6>
              <p class="mb-0">Add new categories. Assign a user the <code>view category</code> permission since after
                creating a category, the system shows the list of categories.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Category</h6>
              <p class="mb-0">View list of categories or details of any category.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Category</h6>
              <p class="mb-0">Update any category.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Category</h6>
              <p class="mb-0">Delete any category from the system.</p>
            </li>
          </ul>
          <h5 class="mb-0">Vendors</h5>
          <ul class="list-group mb-4">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Vendor</h6>
              <p class="mb-0">Add new vendors.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Vendor</h6>
              <p class="mb-0">View list of vendors or details of any vendor.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Vendor</h6>
              <p class="mb-0">Update any vendor.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Vendor</h6>
              <p class="mb-0">Delete any vendor from the system.</p>
            </li>
          </ul>
          <h5 class="mb-0">Settings</h5>
          <ul class="list-group mb-4">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Manage Settings</h6>
              <p class="mb-0">Allow staff to view and update system settings.</p>
            </li>
          </ul>
          <h5 class="mb-0">Order Management</h5>
          <ul class="list-group mb-4">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Order</h6>
              <p class="mb-0">Create new order.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Order</h6>
              <p class="mb-0">View list of orders or details of any order.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Order</h6>
              <p class="mb-0">Update any details on the order form.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Order</h6>
              <p class="mb-0">Delete any order from the system.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Order Item</h6>
              <p class="mb-0">Delete a line item from any order.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Cancel Order</h6>
              <p class="mb-0">Cancel any order from the system.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Invoice Order</h6>
              <p class="mb-0">Invoice any order. Also allows staff to print or email any invoice.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Back Order</h6>
              <p class="mb-0">Create back order from any existing order.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Order Assignment</h6>
              <p class="mb-0">Assign staff to any order.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Quote</h6>
              <p class="mb-0">Create new quotation.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Quote</h6>
              <p class="mb-0">View list of quotations or details of any quotation.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Quote</h6>
              <p class="mb-0">Update any details on the quotation form.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Quote</h6>
              <p class="mb-0">Delete any quotation from the system.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Print Quote</h6>
              <p class="mb-0">Print any quotation.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Email Quote</h6>
              <p class="mb-0">Print any quotation.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Convert Quote</h6>
              <p class="mb-0">Convert any quotation into an order.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Duplicate Quote</h6>
              <p class="mb-0">Duplicate any quote.</p>
            </li>
          </ul>

          <h5 class="mb-0">Receivables</h5>
          <ul class="list-group mb-4">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Invoice</h6>
              <p class="mb-0">See list of invoices and payments. Also allows staff to view details of any invoice</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Statement</h6>
              <p class="mb-0">See list of statements or details of any customer statement.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Print Statement</h6>
              <p class="mb-0">Print any customer statement.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Email Statement</h6>
              <p class="mb-0">Print any customer statement.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Batch Print Statement</h6>
              <p class="mb-0">Print listed statements in a batch.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Payment</h6>
              <p class="mb-0">Create a payment record for a customer. Staff will need <code>view customer</code> and
                <code>view invoice</code> permissions.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Payment</h6>
              <p class="mb-0">This only allows staff to update the payment note and to request deletion of a
                payment.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Payment</h6>
              <p class="mb-0">Allows deletion of a payment. Also approves deletion of a payment when a payment deletion
                has been requested by another staff.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Payment</h6>
              <p class="mb-0">Allows deletion of a payment. Also approves deletion of a payment when a payment deletion
                has been requested by another staff.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Apply Payment</h6>
              <p class="mb-0">Allows applying of the payment amount on all invoices.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Apply Payment</h6>
              <p class="mb-0">Allows applying of the payment amount on all invoices.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Apply Credit</h6>
              <p class="mb-0">Use customer credit to pay for invoices.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Refund</h6>
              <p class="mb-0">Create a refund for a customer's payment. Staff will also need <code>view refund</code>
                permission.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Refund</h6>
              <p class="mb-0">View list of refunds.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Refund</h6>
              <p class="mb-0">Update refund details.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Refund</h6>
              <p class="mb-0">Delete a refund.</p>
            </li>
          </ul>

          <h5 class="mb-0">Inventory</h5>
          <ul class="list-group mb-4">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Item</h6>
              <p class="mb-0">Add items to the inventory.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Item</h6>
              <p class="mb-0">See list of items in inventory and details of any particular item.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Item</h6>
              <p class="mb-0">Update details of any item</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Delete Item</h6>
              <p class="mb-0">Delete an item from the system</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update BOM</h6>
              <p class="mb-0">Update an item's Bill of Material details.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Item Pricing</h6>
              <p class="mb-0">Update only the pricing details of an item.</p>
            </li>
          </ul>

          <h5 class="mb-0">Users</h5>
          <ul class="list-group">
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Create Customer</h6>
              <p class="mb-0">Add a customer to the system. Grants access to all form fields necessary to create a
                complete customer record.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>View Customer</h6>
              <p class="mb-0">See list of customers and details of a specific customer.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Customer</h6>
              <p class="mb-0">Allows user to update any detail about the customer</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Customer Price Level</h6>
              <p class="mb-0">Allows user to update only the price level of the customer. All other details will be
                uneditable.</p>
            </li>
            <li class="list-group-item border-0 pl-0 pb-0">
              <h6>Update Customer Payment Terms</h6>
              <p class="mb-0">Allows user to update only the payment terms of the customer. All other details will be
                uneditable.</p>
            </li>
          </ul>
        </article>


        <article class="py-4">
          <h1 class="mb-3">Inventory</h1>
          <h2 id="items">Items</h2>
          <h3>Item Bill of Materials (BOM)</h3>
          <p>The BOM is a list of parts required to build a particular item. An item can have any number of parts in its
            BOM. A staff can add BOM to any item.</p>
          <p>Items that have a BOM list will derive their cost from the costs of parts in the BOM list. However, those
            items without a BOM list will simply use their original cost calculated as <code>price + freight cost + misc
              cost</code>. Also, the List Price will be calculated based on the total cost of items in the BOM list.
            Otherwise, the original cost will be used to get the item's list price.</p>
          <h5>Directions</h5>
          <ol>
            <li>Go to any item's details page. From the menu: <code>Inventory > Items > [click Details]</code></li>
            <li>Scroll to the <span class="text-uppercase font-weight-bold">Bill of Materials</span> section and click
              on <code>Add to BOM List</code> button. This should take you to another page to add parts to the BOM List.
            </li>
            <li>Use the <code>Add</code> button to add more parts to the BOM List at once. Once you are done, click the
              <code>Save Changes</code> button.
            </li>
          </ol>
          <h3>Swapping a part in a BOM List</h3>
          <p>Deleting an item from the BOM is perfectly fine when you simply want to remove that it, and that's it.
            However, instead of deleting an item only to add another, you can simply swap the item. Swapping an item
            simply replaces an item in the BOM with another. The new item will be added with it's pricing details.</p>
          <h5>Directions</h5>
          <ol>
            <li>Click on the
              <button class="btn btn-sm text-info"><i class="mdi mdi-swap-horizontal"></i> Swap</button>
              button of the item you want to replace. This will open a new page with list of items to swap.
            </li>
            <li>Choose the item to swap with and click the <code>Save Changes</code> button. That's it!</li>
          </ol>
        </article>

        <article class="py-4">
          <h1 class="mb-3">Order Management</h1>
          <h2 id="items">Orders</h2>
          <h3 class="mb-3" id="orders-create-order">Creating an Order</h3>
          <div class="mb-4">
            <h4>How Tax is handled</h4>
            <p>By default all orders by customers within Utah are taxed unless such an order is for a customer who is
              not taxable. A customer is not taxable if they have a tax exemption number and a tax exemption certificate
              has been provided.</p>
            <p>When creating an order for customers out of state, a dialogue pops up asking whether tax should be
              calculated for such a customer or not. A customer is said to be out of state if the billing and shipping
              address provided on the order are both out of Utah.</p>
            <p>When you click <code>No</code>, which is usually the most common option, no tax will be calculated on the
              order. This is the same as Forcing No Tax on the order. However, when you click <code>Yes</code>, tax will
              be calculated for the order if the Customer is taxable. Any response to the dialog box will automatically
              save the changes made on the order.</p>
            <p>When the <code>Ship Via</code> field affects is changed to "Customer Pickup" or "Will Call", tax will be
              calculated. Also, the shipping address field will be cleared to remain empty. </p>
          </div>
          <div class="mb-4">
            <h4>Calculating Item Price</h4>
            <p>The price of the item for the customer is calculated when adding the item to an order. To get this price,
              the system goes through a number of steps as shown and explained below:</p>
            <ol>
              <li>
                <p><strong>Get price from Customer Special Pricing (fixed or discount):</strong><br>The system starts by
                  checking if the customer has a special fixed price or discount on the item. Fixed price takes
                  precedence so it will be considered first. If the customer has a discount instead, the discount will
                  be calculated based on the list price. Customer special priced items are found on the Customer's
                  profile page. If the customer does not have any special pricing on the item, the system skips to the
                  next step.</p>
              </li>
              <li>
                <p><strong>Get Customer Level Special Price: </strong><br>Here, the system checks if the item has any
                  special price matching the customer's price level. For example, if a customer belongs to the <strong>Dealer
                    Standard</strong> price level, the system will check if the item has special prices in that price
                  level. If the item has any special pricing, the fixed price will take precedence. The discount will be
                  calculated based on the item list price. If the item doesn't have any special pricing in the price
                  level, the system will skip to the next step.</p>
              </li>
              <li>
                <p><strong>Item Category Level Discount Price:</strong><br>In this step, the system checks whether the
                  category to which the item belongs has any discount in the price price level of the customer. Once
                  found, the discount is calculated based on the item pricing. If no discount exists, the system skips
                  to the next step.</p>
              </li>
              <li>
                <p><strong>Item Category Discount:</strong><br>At this point, the system checks to see whether the
                  customer has a discount on the category to which item belongs. This is different from the previous
                  step which checks for a discount on a category in a particular price level. If a customer has a
                  discount, then the discount in applied on the item's price level, otherwise the system will skip to
                  the next step. Customer category discounts are managed from the Customer Profile page.</p>
              </li>
              <li>
                <p><strong>Item Fixed Price:</strong><br>Here the system simply uses the item's fixed price if it
                  exists. You can find this on the Item edit or create page.</p>
              </li>
              <li>
                <p><strong>Item List Price:</strong><br>This is the last step when no particular price could be
                  calculated or picked from all the steps above. The list price is calculated from the item's cost and
                  multiplier values as: <code>cost x multiplier</code>.</p>
              </li>
            </ol>
          </div>
          <h3 class="mb-3" id="orders-commissions">Commission</h3>
          <p>There are currently three (3) types of Commissions earned by Customers. These are: Wesley, Woodruff Sales and Commercial RO. A customer earns commission when an order is invoiced. The commission record is automatically generated following the conditions specified in the section below:</p>
          <div class="mb-4">
            <h4>How Commission Works</h4>
            <p>There's a field on each line item on orders and quotes that lets a user to manually select who gets permissions on that item. You can find this among the hidden details, and it is simply a drop down menu that lets a user choose from: WES, RO, and WS. These make it possible to select individual lines to commission when not commissioning the entire order. These are for a few custom commissions that we have whose details follow.</p>

            <h5>Wesley Commission Report For Wesley</h5>
            <p>Customers who are assigned a commission type of Wesley Commission earn a set percentage of commission whenever:</p>
            <ol>
              <li>Wesley prints the invoice (in this case, he would get commission on the entire order, unless one of the items has WS or RO manually selected. He wouldn’t get that one) </li>
              <li>The commission drop down on a line item has “Wes” selected. (If someone else has printed this invoice, Wes would still get commission on that line item)</li>
            </ol>
            <h5>Commercial RO Commission Report for the Commercial ROs</h5>
            <p>Customers who are assigned a commission type of Commercial RO Commission earn a set percentage of commission whenever:</p>
            <ol>
              <li>The item sold is in the category of “Commercial Ro’s”</li>
              <li>The commission drop down on a line item has “RO” selected.</li>
            </ol>
            <h5>Woodruff Sales Commission Report for Woodruff Sales</h5>
            <p>Customers who are assigned a commission type of Woodruff Sales Commission earn a set percentage of commission whenever:<p>
            <ol>
              <li>The part # on a line item begins with <code>WS</code></li>
              <li>The commission drop down on a line item has “WS” selected.</li>
            </ol>
            <p>Woodruff Sales Commission has two different reports. One of them shows the description above. The other shows the following:<p>
            <p><strong>Report for all other commercial softeners sold</strong>: All items in the category of Commercial Softeners that are on orders that have a Part# that doesn’t start with WS, or which don’t have WS selected in their drop down.</p>
            <h4 id="how-commission-works">How a Commission Record is created</h4>
            <p>A commission record is created automatically whenever an order is invoiced (i.e invoice is printed for the very first time). The system checks the order if it has any items that meet the conditions specified above i.e. if any item has a <code>commission type</code> specified, or its part number begins with <code>WS</code>, or it belongs to either Commercial Softeners or Commercial RO categories.</p>
            <h4>How Commission Percentages are used</h4>
            <p>The commission percentage are picked from settings or referrals. When generating a commission record, the system picks the appropriate commission percentage based on the conditions mentioned in the section above (<a href="#how-commission-works">How Commission Works</a>). This percentage is then saved on the commission record and calculated accordingly to get the commission amount. This way, when the administrator changes the percentage in the settings for example after 6 months, the commission record therefore retains the initial percentage that was used to generate it. Also, any reports that may have been generated before the change in the commission settings would not alter commission amounts already reported.</p>
            <h4>Referral Commissions</h4>
            <p>A customer (referrer) earns a commission when an invoice is made for another customer who they referred to the company. In this case, the referrer earns from all the items of the invoice a commission as per the percentage set on the referrer's profile. To add a referral, go to referrer's profile, and click on the <code>+ Add Referral</code> button.</p>
            <h4>Commissions List and Reports</h4>
            <p>You can find all the commissions generated in the <code>Order Management --> Commissions</code> menu.</p>
            <p>There are four (4) Commission reports generated. These can be found under the <code>Reports</code> menu listed as:</p>
            <ol>
              <li>Wesley Commission</li>
              <li>Commercial RO Commission</li>
              <li>Woodruff Sales Commission</li>
              <li>Woodruff Sales for Commercial Softeners Commission</li>
            </ol>
          </div>
        </article>
      </div>
    </div>
    <!-- end page title -->
    <div class="row">

    </div> <!-- end row -->

  </div>
  <!-- container -->
@endsection
@push('js')
  <!-- third party js -->
  <script src="{{ asset('js/vendor/responsive.bootstrap4.min.js') }}"></script>
  <!-- third party js ends -->
@endpush
