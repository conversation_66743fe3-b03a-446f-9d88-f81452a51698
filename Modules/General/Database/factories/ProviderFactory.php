<?php

use Faker\Generator as Faker;
use Modules\General\Entities\Provider;
use Modules\Users\Entities\Address;
use Modules\Users\Entities\Contact;

$factory->define(Provider::class, function (Faker $faker) {
  return [
    'name' => $faker->company(),
    'account_number' => $faker->unique()->randomNumber(5, true),
    'type' => Provider::TYPE_VENDOR,
  ];
});

$factory->afterCreating(Provider::class, function (Provider $vendor, Faker $faker) {
  $vendor->shippingAddress()->create(factory(Address::class)->make()->toArray());
  $vendor->contacts()->create(factory(Contact::class)->make()->toArray());
});

