<?php

use Faker\Generator as Faker;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrderRequest;
use Modules\Users\Entities\User;


$factory->define(PurchaseOrderRequest::class, function (Faker $faker) {
  return [
    'vendor_id' => fn() => factory(Provider::class)->create()->id,
    'created_by' => fn() => factory(User::class)->state('staff')->create()->id,
    'quantity' => 10,
    'part_number' => $this->faker->word(),
    'manufacturer_part_number' => $this->faker->word(),
    'description' => $this->faker->sentence(),
  ];
});
