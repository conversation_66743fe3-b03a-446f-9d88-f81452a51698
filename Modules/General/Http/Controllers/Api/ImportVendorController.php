<?php

namespace Modules\General\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\General\Services\ImportVendors;

/**
 * Class ImportVendorController
 * @package Modules\General\Http\Controllers\Api
 * @codeCoverageIgnore
 */
class ImportVendorController extends Controller
{
  protected ImportVendors $importService;

  public function __construct(ImportVendors $importService)
  {
    $this->importService = $importService;
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function store(Request $request)
  {
    try {
      dispatch(new \App\Jobs\ImportVendors($request->all()));

      return response()
        ->json(
          [
            'message' => 'Vendors imported successfully',
            'status' => true
          ],
          Response::HTTP_CREATED
        );
    } catch (\Exception $e) {

      return response()
        ->json(
          ['error' => $e->getMessage()],
          Response::HTTP_INTERNAL_SERVER_ERROR
        );
    }
  }

  public function view($num)
  {
    return view('source.Vendors' . $num);
  }
}
