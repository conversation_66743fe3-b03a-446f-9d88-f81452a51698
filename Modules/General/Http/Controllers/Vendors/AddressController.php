<?php

namespace Modules\General\Http\Controllers\Vendors;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\General\Entities\Provider;
use Modules\Users\Entities\Address;

class AddressController extends Controller
{

    /**
     * Show the form for creating a new resource.
     * @param Provider $vendor
     * @return Response
     */
    public function create(Provider $vendor)
    {
        $address = new Address();

        return view('general::providers.addresses.create', compact('vendor', 'address'));
    }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @param Provider $vendor
   * @return Response
   */
    public function store(Request $request, Provider $vendor)
    {
        $attributes = $request->validate([
            'state' => 'required|string|max:10',
            'city' => 'required|string|max:50',
            'zip' => 'nullable|string|max:25',
            'unit' => 'nullable|string|max:255',
            'type' => 'nullable|string|max:25',
            'name' => 'nullable|string|max:255'
        ]);

        $address = $vendor->addresses()->create($attributes);
        $vendor->{$address->type . '_address'} = $address->full_address;
        $vendor->save();

        return redirect()->route('general.vendors.show', $vendor)->with('success', 'Address added successfully');
    }

    /**
     * Show the form for editing the specified resource.
     * @param Provider $vendor
     * @param Address $address
     * @return Response
     */
    public function edit(Provider $vendor, Address $address)
    {
        return view('general::providers.addresses.edit', compact('vendor', 'address'));
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param Provider $vendor
     * @param Address $address
     * @return Response
     */
    public function update(Request $request, Provider $vendor, Address $address)
    {
        $request->validate([
            'state' => 'required|string|max:10',
            'city' => 'required|string|max:50',
            'zip' => 'nullable|string|max:25',
            'unit' => 'nullable|string|max:255',
            'type' => 'required|string|max:25'
        ]);

        $address->update($request->all());
        $vendor->{$address->type . '_address'} = $address->full_address;
        $vendor->save();

        return redirect()
                ->route('general.vendors.show', $vendor)
                ->with('success', 'Address updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     * @param Provider $vendor
     * @param Address $address
     * @return Response
     */
    public function destroy(Provider $vendor, Address $address)
    {
      $address->delete();

      return redirect()->route('general.vendors.show', $vendor)
        ->with('success', 'Address deleted successfully');
    }
}
