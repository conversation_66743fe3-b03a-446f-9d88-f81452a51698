<?php

namespace Modules\General\Http\Controllers;

use App\Services\ListService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Modules\General\Actions\CreateVendorAction;
use Modules\General\Actions\UpdateVendorAction;
use Modules\General\Entities\Category;
use Modules\General\Entities\Provider;
use Modules\General\Services\VendorSearchService;

class ProviderController extends Controller
{
  use AuthorizesRequests;

  protected $lists;

  public function __construct(ListService $lists)
  {
    $this->authorizeResource(Provider::class, 'vendor');
    $this->lists = $lists;
  }

  /**
   * Display a listing of the resource.
   * @param VendorSearchService $searchService
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index(VendorSearchService $searchService)
  {
    $this->authorize('viewAny', Provider::class);

    return view('general::providers.index', [
      'vendors' => $searchService->find(),
      'searchFields' => $searchService->searchFields(),
      'searchConditions' => $searchService->conditions(),
    ]);
  }

  /**
   * Show the form for creating a new resource.
   * @return Response
   */
  public function create()
  {
    $itemList = $this->lists->itemsFromCategoryList(Category::COMMERCIAL_SOFTENERS);

    return view('general::providers.create', compact('itemList'));
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @param CreateVendorAction $action
   * @return \Illuminate\Http\RedirectResponse
   */
  public function store(Request $request, CreateVendorAction $action)
  {
    $record = $request->validate([
      'name' => 'required|string|max:255',
      'phone' => 'nullable|string|max:25',
      'fax' => 'nullable|string|max:25',
    ]);

    $action->execute($record);

    return redirect()
      ->route('general.vendors.index')
      ->with('success', 'Vendor created successfully');
  }

  /**
   * Show the specified resource.
   * @param Provider $vendor
   * @return Response
   */
  public function show(Provider $vendor)
  {
    return view('general::providers.show', compact('vendor'));
  }

  /**
   * Show the form for editing the specified resource.
   * @param Provider $vendor
   * @return Response
   */
  public function edit(Provider $vendor)
  {
    $itemList = $this->lists->itemsFromCategoryList(Category::COMMERCIAL_SOFTENERS);

    return view('general::providers.edit', compact('vendor', 'itemList'));
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param UpdateVendorAction $action
   * @param Provider $vendor
   * @return \Illuminate\Http\RedirectResponse
   */
  public function update(Request $request, UpdateVendorAction $action, Provider $vendor)
  {
    $details = $request->validate([
      'name' => 'required|string|max:255',
      'phone' => 'nullable|string|max:25',
      'fax' => 'nullable|string|max:25'
    ]);

    return redirect()
      ->route('general.vendors.show', $action->execute($vendor, $details))
      ->with('success', 'Vendor updated successfully');
  }

  /**
   * Remove the specified resource from storage.
   * @param Provider $vendor
   * @return \Illuminate\Http\RedirectResponse
   */
  public function destroy(Provider $vendor)
  {
    $vendor->delete();
    Cache::forget('vendorNumberList');

    return redirect()
      ->route('general.vendors.index')
      ->with('success', 'Vendor deleted successfully');
  }
}
