<?php

namespace Modules\General\Http\Controllers\PurchaseOrders;

use Illuminate\Routing\Controller;
use Modules\General\Actions\SendPurchaseOrderAction;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Http\Requests\SendEmailRequest;

class SendPurchaseOrder extends Controller
{
  public function __invoke(SendEmailRequest $request, SendPurchaseOrderAction $action, PurchaseOrder $purchaseOrder)
  {
    $action->execute($purchaseOrder, $request->all());

    return back()
      ->with('success', 'Purchase Order sent successfully');
  }
}
