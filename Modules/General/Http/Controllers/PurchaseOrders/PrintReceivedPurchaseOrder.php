<?php

namespace Modules\General\Http\Controllers\PurchaseOrders;

use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Entities\PurchaseOrderRequest;

class PrintReceivedPurchaseOrder extends Controller
{
  use AuthorizesRequests;


  /**
   * Show form with the items on the need to order list for the selected vendor.
   *
   * @param PurchaseOrder $purchaseOrder
   * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
   * @throws \Illuminate\Contracts\Container\BindingResolutionException
   */
  public function __invoke(PurchaseOrder $purchaseOrder)
  {
    if ($purchaseOrder->notPrinted()) {
      $purchaseOrder->print();
    }

    $pdf = app()->make('snappy.pdf.wrapper')
      ->loadView('general::purchase-orders.print-received', compact('purchaseOrder'));

    return $pdf->inline('purchase-order-received-' . $purchaseOrder->number . '.pdf');
  }
}
