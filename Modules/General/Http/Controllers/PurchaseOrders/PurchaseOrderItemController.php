<?php

namespace Modules\General\Http\Controllers\PurchaseOrders;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Routing\Controller;
use Modules\General\Entities\PurchaseOrderItem;

/**
 * Class PurchaseOrderItemController
 * @package Modules\General\Http\Controllers\PurchaseOrders
 * @codeCoverageIgnore
 */
class PurchaseOrderItemController extends Controller
{
  use AuthorizesRequests;

  /**
   * @param PurchaseOrderItem $purchaseOrderItem
   * @return \Illuminate\Http\RedirectResponse
   */
  public function destroy(PurchaseOrderItem $purchaseOrderItem)
  {
    try {
      // Check if item is the only one left on the purchase order.
      if ($purchaseOrderItem->purchaseOrder->purchaseOrderItems->count() === 1) {
        return back()
          ->withError('Purchase order cannot remain without an item to order.');
      }

      $purchaseOrderItem->delete();

      return back()->withSuccess('Purchase order item deleted successfully');
    } catch (\Exception $e) {
      \Log::debug($e->getMessage());
      return back()->withError('Could not delete purchase-order item at this time.');
    }
  }
}
