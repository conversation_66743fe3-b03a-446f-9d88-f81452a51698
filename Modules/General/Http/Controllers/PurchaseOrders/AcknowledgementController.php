<?php

namespace Modules\General\Http\Controllers\PurchaseOrders;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Storage;
use Modules\General\Entities\PurchaseOrder;

class AcknowledgementController extends Controller
{
  public function store(Request $request, PurchaseOrder $purchaseOrder)
  {
    $request->validate([
      'acknowledgement_file' => 'required|file|mimes:jpg,jpeg,png,docx,doc,pdf',
    ]);

    $filename = $purchaseOrder->number . '-acknowledgement-file.' . $request->file('acknowledgement_file')->getClientOriginalExtension();

    $purchaseOrder->addMediaFromRequest('acknowledgement_file')
      ->usingFileName($filename)
      ->toMediaCollection();

    $purchaseOrder->update([
      'acknowledgement_uploaded' => true,
      'acknowledgement_uploaded_at' => now(),
      'acknowledgement_uploaded_by' => auth()->id()
    ]);

    return back()
      ->with('success', 'Acknowledgement file uploaded successfully');
  }

  /**
   * @codeCoverageIgnore
   * @param PurchaseOrder $purchaseOrder
   * @param string $type
   * @return \Symfony\Component\HttpFoundation\StreamedResponse
   */
  public function show(PurchaseOrder $purchaseOrder, $type = 'download')
  {
    if ($type === 'download') {
      return Storage::disk('s3')->download($purchaseOrder->acknowledgement_file);
    }

    return Storage::disk('s3')->response($purchaseOrder->acknowledgement_file);
  }

  /**
   * @codeCoverageIgnore
   * @param PurchaseOrder $purchaseOrder
   * @return \Illuminate\Http\RedirectResponse
   */
  public function destroy(PurchaseOrder $purchaseOrder)
  {
    $purchaseOrder->getFirstMedia()->delete();

    return back()->with('success', 'Acknowledgement file deleted successfully');
  }
}
