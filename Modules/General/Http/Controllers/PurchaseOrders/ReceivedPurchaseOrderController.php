<?php

namespace Modules\General\Http\Controllers\PurchaseOrders;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Entities\PurchaseOrderItem;
use Modules\General\Events\PurchaseOrderReceived;
use Modules\General\Services\PurchaseOrderSearchService;

class ReceivedPurchaseOrderController extends Controller
{
  use AuthorizesRequests;

  public function index(PurchaseOrderSearchService $service)
  {
    return view('general::purchase-orders.received.index', [
      'searchFields' => $service->searchFields(),
      'searchConditions' => $service->conditions(),
      'searchParams' => request()->only(['fields', 'conditions', 'terms'])
    ]);
  }

  public function create(PurchaseOrder $purchaseOrder)
  {
    return view('general::purchase-orders.received.create', [
      'purchaseOrder' => $purchaseOrder
    ]);
  }

  public function store(Request $request, PurchaseOrder $purchaseOrder)
  {
    $request->validate([
      'purchase_order_items' => 'array|required|min:1',
      'quantity_received' => 'array|required|min:1'
    ]);

    $purchaseOrderItems = PurchaseOrderItem::find($request->purchase_order_items);
    $backOrderItems = $purchaseOrderItems->filter(function ($poItem, $key) {
      $qtyReceived = request('quantity_received')[$key];

      $poItem->update([
        'quantity_received' => $qtyReceived
      ]);

      return $qtyReceived < $poItem->quantity;
    })->values()->map(function ($item, $key) {
      $backOrderItem = Arr::only(
        $item->toArray(),
        [
          'part_number',
          'manufacturer_part_number',
          'unit',
          'ordered_for',
          'description',
          'price',
        ]
      );
      $backOrderItem['line_number'] = $key + 1;
      $backOrderItem['quantity'] = $item->quantity - $item->quantity_received;

      return $backOrderItem;
    })->all();

    if (count($backOrderItems) === 0) {
      // No backorder items so just receive the purchase order
      $this->receivePurchaseOrder($purchaseOrder);

      return redirect()->route('general.purchase-orders.show', $purchaseOrder)
        ->withSuccess('Purchase Order received successfully');
    }


    $backPurchaseOrder = $purchaseOrder->replicate();
    $backPurchaseOrder->is_back_order = true;
    $backPurchaseOrder->number = $backPurchaseOrder->nextBackOrderNumber();
    $backPurchaseOrder->purchase_order_id = $purchaseOrder->id;
    $backPurchaseOrder->created_by = auth()->id();
    $backPurchaseOrder->created_at = $purchaseOrder->created_at;
    $backPurchaseOrder->save();

    $backPurchaseOrder->purchaseOrderItems()->createMany($backOrderItems);
    $this->receivePurchaseOrder($purchaseOrder);

    return redirect()
      ->route('general.purchase-orders.show', $backPurchaseOrder)
      ->withSuccess('Back Purchase Order created successfully');
  }

  /**
   * @param PurchaseOrder $purchaseOrder
   * @return bool
   */
  protected function receivePurchaseOrder(PurchaseOrder $purchaseOrder): bool
  {
    $purchaseOrder->update([
      'received' => true,
      'received_at' => now(),
      'received_by' => auth()->user()->name,
      'status' => PurchaseOrder::STATUS_RECEIVED,
    ]);

    event(new PurchaseOrderReceived($purchaseOrder));

    return true;
  }
}
