<?php

namespace Modules\General\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\User;

class CategoryPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any category.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->can('view category');
    }

    /**
     * Determine whether the user can view the category.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function view(User $user)
    {
        return $user->can('view category');
    }

    /**
     * Determine whether the user can create categorys.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->can('create category');
    }

    /**
     * Determine whether the user can update the category.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function update(User $user)
    {
        return $user->can('update category');
    }

    /**
     * Determine whether the user can delete the category.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function delete(User $user)
    {
        return $user->can('delete category');
    }

    /**
     * Determine whether the user can restore the category.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function restore(User $user)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the category.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function forceDelete(User $user)
    {
        return $user->isAdmin();
    }
}
