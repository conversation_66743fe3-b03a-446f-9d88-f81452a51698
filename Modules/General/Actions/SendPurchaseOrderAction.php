<?php


namespace Modules\General\Actions;


use Illuminate\Support\Facades\Mail;
use Modules\General\Emails\PurchaseOrderCreated;
use Modules\General\Entities\PurchaseOrder;

class SendPurchaseOrderAction
{
  protected PrintPurchaseOrderAction $printer;

  public function __construct(PrintPurchaseOrderAction $printer)
  {
    $this->printer = $printer;
  }

  public function execute(PurchaseOrder $purchaseOrder, array $data): bool
  {
    $this->printer->execute($purchaseOrder);

    Mail::to($data['recipient_emails'])
      ->send(new PurchaseOrderCreated($purchaseOrder, $data['body']));

    return true;
  }
}
