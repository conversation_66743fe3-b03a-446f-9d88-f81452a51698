<?php

namespace Modules\General\Actions;

use App\Services\PdfGeneratorService;
use Illuminate\Support\Facades\Storage;
use Modules\General\Entities\PurchaseOrder;

class PrintPurchaseOrderAction
{
  protected $pdfService;

  protected string $view = 'general::purchase-orders.print';

  /**
   * @var string
   */
  public string $filename;

  public function __construct(PdfGeneratorService $pdfGeneratorService)
  {
    $this->pdfService = $pdfGeneratorService;
  }

  public function execute(PurchaseOrder $purchaseOrder): \Barryvdh\DomPDF\PDF
  {
    if (empty($this->filename)) {
      $this->filename($purchaseOrder->number);
    }

    return $this->pdfService
      ->view($this->view)
      ->content(['purchaseOrder' => $purchaseOrder])
      ->save($this->filename);
  }

  public function filename(string $name): self
  {
    Storage::disk('local')
      ->makeDirectory('generated/purchase-orders');

    $this->filename = storage_path('app/generated/purchase-orders') . "/{$name}.pdf";

    return $this;
  }
}
