<?php


namespace Modules\General\Actions;


use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Modules\General\Entities\Category;
use Modules\General\Entities\Provider;
use Modules\Inventory\Entities\Level;

class UpdateCategoryAction
{
  public function execute(Category $category, array $details): Category
  {
    $parentCategory = Category::find(Arr::get($details, 'category_id'));

    if ($parentCategory && $parentCategory->type !== $details['type']) {
      // Match the category type to its parent's type
      // Avoids having mixed type categories e.g Cat A of type Item with parent category of type Product
      $details['type'] = $parentCategory->type;
    }

    $category->update($details);

    Cache::forget('categories');
    Cache::forget('levels');

    return $category;
  }
}
