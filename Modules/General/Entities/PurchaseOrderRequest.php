<?php

namespace Modules\General\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Inventory\Entities\Item;
use Modules\Users\Entities\Staff;
use OwenIt\Auditing\Contracts\Auditable;

class PurchaseOrderRequest extends Model implements Auditable
{
  use \OwenIt\Auditing\Auditable, SoftDeletes;

  const FIRST_ORDER_NUMBER = 5000;
  const STATUS_PENDING = 'pending';
  const STATUS_COMPLETED = 'completed';

  protected $guarded = [];

  protected $dates = [
    'completed_at'
  ];

  /**
   * Get the items in the PO Request
   */
  public function vendor(): \Illuminate\Database\Eloquent\Relations\BelongsTo
  {
    return $this->belongsTo(Provider::class);
  }

  public function item(): \Illuminate\Database\Eloquent\Relations\BelongsTo
  {
    return $this->belongsTo(Item::class, 'part_number', 'code');
  }

  public function createdBy(): \Illuminate\Database\Eloquent\Relations\BelongsTo
  {
    return $this->belongsTo(Staff::class, 'created_by', 'user_id');
  }

  public function scopePending(Builder $query)
  {
    $query->whereStatus(self::STATUS_PENDING);
  }

  public function scopeByVendor(Builder $query, $vendorId)
  {
    $query->whereVendorId($vendorId);
  }

  public function getFormattedDateAttribute()
  {
    return $this->created_at->format('m/d/y');
  }

  public function getFormattedCompletedDateAttribute()
  {
    return $this->completed_at->format('m/d/y');
  }

  public function getPendingAttribute(): bool
  {
    return $this->status === self::STATUS_PENDING;
  }

  public function getCompletedAttribute(): bool
  {
    return $this->status === self::STATUS_COMPLETED;
  }
}
