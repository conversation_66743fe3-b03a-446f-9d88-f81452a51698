<?php


namespace Modules\General\Services;

use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Class VendorSearchService
 * @package Modules\General\Services
 * @codeCoverageIgnore
 */
class VendorSearchService
{
  const FIELD_ALL = 'all';
  const OPERATOR_BETWEEN = 'BETWEEN';
  const OPERATOR_NOT_BETWEEN = 'NOT BETWEEN';
  const OPERATOR_IS_NULL = 'IS NULL';
  const OPERATOR_IS_NOT_NULL = 'IS NOT NULL';

  public function find()
  {
    $request = request();

    if (! $request->filled('terms') || (! in_array('empty', $request->conditions) && ! empty($request->terms[0]) && ($request->filled('terms') && empty($request->terms[0])))) {
      return $this->getVendors();
    }

    $query = $this->defaultQuery();

    $this->getFilters($request)
      ->each(function ($condition, $field) use (&$query) {
        $query = $this->buildQuery($query, $condition, $field);
      });

    return $query->groupBy(
      ['p.id']
    )
      ->orderBy('p.name')
      ->get();
  }

  protected function buildQuery(Builder $query, $condition, $field = null)
  {
    $value = $condition['value'];
    $operator = $condition['operator'];

    if ($field === self::FIELD_ALL) {
      return $this->searchAnyFields($query, $condition);
    }

    if ($operator === self::OPERATOR_BETWEEN) {
      $pieces = explode(' ', $value);
      return $query->whereBetween($field, [$pieces[0], $pieces[2]]);
    }

    if ($operator === self::OPERATOR_NOT_BETWEEN) {
      $pieces = explode(' ', $value);
      return $query->whereNotBetween($field, [$pieces[0], $pieces[2]]);
    }

    if ($operator === self::OPERATOR_IS_NULL) {
      return $query->whereNull($field);
    }

    if ($operator === self::OPERATOR_IS_NOT_NULL) {
      return $query->whereNotNull($field);
    }

    return $query->where($field, $operator, $value);

  }

  protected function getFilters(Request $request)
  {
    return collect($request->fields)
      ->mapWithKeys(function ($field, $key) use ($request) {
        return [
          $field => Arr::get(
            self::conditions($request->terms[$key]),
            $request->conditions[$key],
            []
          )
        ];
      });
  }

  protected function getVendors()
  {
    return $this->defaultQuery()
      ->groupBy(['p.id'])
      ->orderBy('p.name')
      ->get();
  }

  protected function defaultQuery()
  {
    $addressTypeSubQuery = DB::table('addresses')
      ->selectRaw("addressable_id,
        CASE WHEN TYPE = 'account' THEN concat(unit, '\n', city, ', ', state, ' ', zip) END AS account_address,
        CASE WHEN TYPE = 'billing' THEN concat(unit, '\n', city, ', ', state, ' ', zip) END AS billing_address,
        CASE WHEN TYPE = 'shipping' THEN concat(unit, '\n', city, ', ', state, ' ', zip) END AS shipping_address")
      ->where('addressable_type', 'LIKE', '%Provider')
      ->whereNull('addresses.deleted_at');

    $providerAddresses = DB::table(DB::raw("({$addressTypeSubQuery->toSql()}) as a"))
      ->mergeBindings($addressTypeSubQuery)
      ->selectRaw("a.addressable_id,
        MAX(ifnull(a.account_address, NULL)) AS account_address,
        MAX(ifnull(a.billing_address, NULL)) AS billing_address,
        MAX(ifnull(a.shipping_address, NULL)) AS shipping_address")
      ->groupBy(['a.addressable_id']);

    return DB::table('providers as p')
      ->selectRaw("
        p.id,
        p.account_number,
        p.name,
        p.phone,
        p.fax,
        a.account_address,
        a.billing_address,
        a.shipping_address,
        GROUP_CONCAT( CONCAT(c.name, ', ', c.email, ', ', c.phone) SEPARATOR '\r\n') as contact
      ")
      ->leftJoin('contacts as c', function ($join) {
        $join->on('p.id', '=', 'c.contactable_id')
          ->where('c.contactable_type', 'like', '%Provider');
      })
      ->leftJoinSub($providerAddresses, 'a', function ($join) {
        $join->on('p.id', '=', 'a.addressable_id');
      })
      ->whereNull('p.deleted_at');
  }

  public static function searchFields()
  {
    return [
      'p.account_number' => 'Account Number',
      'p.name' => 'Name',
      'p.phone' => 'Phone Number',
      'p.fax' => 'Fax Number',
      'c.name' => 'Contact Name',
      'c.email' => 'Contact Email',
      'c.phone' => 'Contact Phone',
      'a.account_address' => 'Account Address',
      'a.billing_address' => 'Billing Address',
      'a.shipping_address' => 'Shipping Address',
    ];
  }

  public static function conditions($term = '')
  {
    return [
      'like' => [
        'label' => 'Contains',
        'operator' => 'LIKE',
        'value' => "%{$term}%"
      ],
      'not_like' => [
        'label' => 'Does Not Contain',
        'operator' => 'NOT LIKE',
        'value' => "%{$term}%"
      ],
      'starts_with' => [
        'label' => 'Begins With',
        'operator' => 'LIKE',
        'value' => "{$term}%"
      ],
      'ends_with' => [
        'label' => 'Ends With',
        'operator' => 'LIKE',
        'value' => "%{$term}"
      ],
      'equal' => [
        'label' => 'Is Equal To',
        'operator' => '=',
        'value' => $term
      ],
      'not_equal' => [
        'label' => 'Is Not Equal To',
        'operator' => '<>',
        'value' => $term
      ],
      'less_than' => [
        'label' => 'Is Less Than',
        'operator' => '<',
        'value' => $term
      ],
      'greater_than' => [
        'label' => 'Is Greater Than',
        'operator' => '>',
        'value' => $term
      ],
      'less_than_equal' => [
        'label' => 'Is Less Than or Equal To',
        'operator' => '<=',
        'value' => $term
      ],
      'greater_than_equal' => [
        'label' => 'Is Greater Than or Equal To',
        'operator' => '>=',
        'value' => $term
      ],
      'between' => [
        'label' => 'Is Between',
        'operator' => 'BETWEEN',
        'value' => $term
      ],
      'not_between' => [
        'label' => 'Is Not Between',
        'operator' => 'NOT BETWEEN',
        'value' => $term
      ],
      'empty' => [
        'label' => 'Is Empty',
        'operator' => 'IS NULL',
        'value' => $term
      ],
      'not_empty' => [
        'label' => 'Is Not Empty',
        'operator' => 'IS NOT NULL',
        'value' => $term
      ]
    ];
  }
}
