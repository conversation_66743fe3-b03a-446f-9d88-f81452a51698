<?php

namespace Modules\General\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\General\Events\PurchaseOrderReceived;
use Modules\General\Listeners\RestockQuantity;
use Modules\Inventory\Listeners\UploadMedia;
use Modules\Inventory\Events\ItemSaved;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        PurchaseOrderReceived::class => [
          RestockQuantity::class
        ]
    ];
}
