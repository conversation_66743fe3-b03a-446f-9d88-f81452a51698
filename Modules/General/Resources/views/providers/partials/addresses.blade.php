<div class="card">
    <div class="card-body">
        <div class="card-widgets">
            <a data-toggle="collapse" href="#cardCollpase1" role="button" aria-expanded="true" aria-controls="cardCollpase1" class=""><i class="mdi mdi-minus"></i></a>
        </div>
        <h5 class="card-title mb-0 text-info">Addresses</h5>

        <div id="cardCollpase1" class="pt-3 collapse show" style="">
            @foreach($vendor->addresses as $address)
                <div class="border-left border-info mb-3 shadow-sm px-3 py-2" style="border-radius: 3px;">
                    <h5 class="text-muted">{{ ucfirst($address->type) }}</h5>
                    <address>
                        @if(! empty($address->name))
                            <span class="d-block font-weight-bold">{{ $address->name }}</span>
                        @endif
                        @if(! empty($address->unit))
                            {{ $address->unit }},
                        @endif
                        {{ $address->city }}, {{ $address->state }}, {{ $address->zip }}
                    </address>
                    <div class="text-right">
                        <a class="btn btn-sm btn-link"
                           href="{{ route('general.vendors.addresses.edit', ['vendor' => $vendor, 'address' => $address]) }}">
                            <i class="mdi mdi-pencil"></i> Edit
                        </a>
                        <a class="btn btn-sm btn-link text-danger delete-vendor-detail-btn"
                           href="#"
                           data-toggle="modal"
                           data-target="#deleteItem"
                           data-url="{{ route('general.vendors.addresses.destroy', ['vendor' => $vendor, 'address' => $address]) }}">
                            <i class="mdi mdi-delete"></i> Delete
                        </a>
                    </div>
                </div>
            @endforeach
            @can('update customer')
                <div class="text-right">
                    <a href="{{ route('general.vendors.addresses.create', $vendor) }}"
                       class="btn btn-outline-info btn-sm">
                        <i class="mdi mdi-plus"></i>
                        Add Address
                    </a>
                </div>
            @endcan
        </div>
    </div>
</div>
