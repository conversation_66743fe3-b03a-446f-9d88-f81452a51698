<div class="card border-top border-info">
    <div class="card-body">
        <div class="row align-items-center mb-3">
            <div class="col-sm-7">
                <h4 class="header-title mb-0">Referrals</h4>
            </div>
            <div class="col-sm-5 text-right">
                @can('update customer')
                    <a class="btn btn-sm btn-outline-info"
                       href="{{ route('users.customers.referrals.create', $customer) }}">
                        <i class="mdi mdi-plus"></i>
                        Add Referral
                    </a>
                @endcan
            </div>
        </div>

        @if($customer->referrals->count())
            <div class="table-responsive">
                <table class="table table-hover table-centered mb-0">
                    <thead>
                    <tr>
                        <th>Name</th>
                        <th class="text-right">Discount</th>
                        <th>Items</th>
                        @can('update customer')
                            <th class="text-right">Action</th>
                        @endcan
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($customer->referrals as $referral)
                        <tr>
                            <td>
                                <a class="text-body"
                                   href="{{ route('users.customers.show', $referral->referredCustomer) }}">
                                    {{ $referral->referredCustomer->name }}
                                </a>
                            </td>
                            <td class="text-right">
                                {{ $referral->discount }}
                            </td>
                            <td>{{ $referral->formatted_items }}</td>
                            @can('update customer')
                                <td class="text-right">
                                    <a href="{{ route('users.customers.referrals.edit', ['customer' => $customer, 'referral' => $referral]) }}"
                                       class="action-icon">
                                        <i class="mdi mdi-pencil"></i>
                                    </a>

                                    <a href="#"
                                       data-target="#deleteItem"
                                       data-toggle="modal"
                                       class="action-icon delete-item-btn"
                                       data-url="{{ route('users.customers.referrals.destroy', ['customer' => $customer, 'referral' => $referral]) }}">
                                        <i class="mdi mdi-delete"></i>
                                    </a>
                                </td>
                            @endcan
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div> <!-- end table responsive-->
        @else
            <p class="text-muted">{{ $customer->name }} has not referred anyone</p>
        @endif
    </div>
</div>
