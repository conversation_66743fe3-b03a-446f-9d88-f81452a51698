<div class="form-row mb-2">
    <div class="col-sm-12">
        <label for="address-name">Name</label>
        <input type="text"
               name="name"
               id="address-name"
               class="form-control @error('name') is-invalid @enderror"
               value="{{ old('name', $address->name) }}"
               placeholder="e.g {{ $vendor->name }}">
    </div>
</div>
<div class="form-row mb-2">
    <div class="col-sm-12">
        <label for="address-type">Address Type</label>
        <select type="text"
                name="type"
                id="address-type"
                class="form-control @error('type') is-invalid @enderror"
                required>
            <option value="">Select Address Type</option>
            <option value="account" {{ $address->type === 'account' ? 'selected' : '' }}>Account</option>
            <option value="billing"{{ $address->type === 'billing' ? 'selected' : '' }}>Billing</option>
            <option value="shipping"{{ $address->type === 'shipping' ? 'selected' : '' }}>Shipping</option>
        </select>
    </div>
</div>
<div class="form-row mb-2">
    <div class="col-sm-12">
        <label for="address-unit">Address</label>
        <input type="text"
               name="unit"
               id="address-unit"
               class="form-control @error('unit') is-invalid @enderror"
               value="{{ old('unit', $address->unit) }}"
               placeholder="e.g 403 ABC Towers, Floor 3, Room 2">
    </div>
</div>
<div class="form-group mb-2">
    <label for="billing_zip"> Zip</label>
    <input type="text"
           id="billing_zip"
           name="zip"
           class="form-control zipInput"
           value="{{ old('billing_zip', $address->zip) }}">
    <small>This will populate both state and city</small>
</div>

<div class="form-group mb-2">
    <label class=""
           for="billing_city"> City</label>
    <input type="text"
           id="billing_city"
           name="city"
           class="form-control cityInput"
           value="{{ old('billing_city', $address->city) }}">
</div>

<div class="form-group mb-2">
    <label class=""
           for="billing_state"> State</label>
    <input type="text"
           id="billing_state"
           name="state"
           class="form-control stateInput"
           value="{{ old('billing_state', $address->state) }}">
</div>
