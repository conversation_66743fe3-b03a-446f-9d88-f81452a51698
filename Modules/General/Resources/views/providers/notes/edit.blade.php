@extends('layouts.master')

@section('title', 'Edit Customer Note')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.vendors.index') }}">Vendors</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.vendors.show', $vendor) }}">{{ $vendor->name }}</a></li>
                            <li class="breadcrumb-item active">Edit Note</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Note <small>| {{ $vendor->name }}</small></h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8 col-sm-10">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <form action="{{ route('general.vendors.notes.update', [
                                 'vendor' => $vendor,
                                 'note' => $note
                              ]) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')

                            @include('general::providers.notes.partials.form')

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('general.vendors.show', $vendor) }}"><i class="mdi mdi-cancel mr-1"></i> Cancel</a>
                                <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

