@extends('layouts.master')

@section('title', 'Edit Vendor')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.vendors.index') }}">Categories</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.vendors.show', $vendor) }}">{{ $vendor->name }}</a></li>
                            <li class="breadcrumb-item active">Edit Vendor</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Vendor</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-8">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Fill the form below to edit this vendor</p>
                        <form action="{{ route('general.vendors.update', $vendor) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')

                            <div class="form-row">
                                <div class="col-12 mb-3">
                                    <label for="vendor-name">Name</label>
                                    <input type="text"
                                           name="name"
                                           id="vendor-name"
                                           class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name', $vendor->name) }}">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col-12 mb-3">
                                    <label for="provider-account-number">Account Number</label>
                                    <input type="text"
                                           id="provider-account-number"
                                           class="form-control disabled"
                                           value="{{ $vendor->account_number }}" disabled>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col-md-6 mb-3">
                                    <label for="vendor-phone">Phone</label>
                                    <input type="text"
                                           name="phone"
                                           id="vendor-phone"
                                           class="form-control @error('phone') is-invalid @enderror"
                                           value="{{ old('phone', $vendor->phone) }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="vendor-fax">Fax</label>
                                    <input type="text"
                                           name="fax"
                                           id="vendor-fax"
                                           class="form-control @error('fax') is-invalid @enderror"
                                           value="{{ old('contact_person', $vendor->fax) }}">
                                </div>
                            </div>

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('general.vendors.show', $vendor) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

