@extends('layouts.master')

@section('title', 'Vendors')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item active">Vendors</li>
            </ol>
          </div>
          <h4 class="page-title">Vendors</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row mb-2">
              <div class="col-sm-8">
                @include('shared.advanced-search', ['searchFields' => $searchFields, 'searchConditions' => $searchConditions])
              </div>
              <div class="col-sm-4 text-right">
                @can('create vendor')
                  <a href="{{ route('general.vendors.create') }}" class="btn btn-danger mb-2"><i
                      class="mdi mdi-plus-circle mr-1"></i> Add Vendor</a>
                @endcan
              </div><!-- end col-->
            </div>

            <div class="table-responsive">
              <table class="table table-sm table-centered w-100 dt-responsive nowrap datatable search-autofocus">
                <thead class="thead-light">
                <tr>
                  <th>Account#</th>
                  <th class="all">Name</th>
                  <th>Phone</th>
                  <th>Fax</th>
                  <th>Account Address</th>
                  <th>Bill To Address</th>
                  <th>Shipping Address</th>
                  <th>Contacts</th>
                  @can('delete vendor')
                    <th style="min-width: 85px;">Action</th>
                  @endcan
                </tr>
                </thead>
                <tbody>
                @foreach ($vendors as $vendor)
                  <tr>
                    <td>{{ $vendor->account_number }}</td>
                    <td>
                      <div class="d-inline-block align-middle font-16">
                        <a class="text-info" href="{{ route('general.vendors.show', $vendor->id) }}">
                          {{ $vendor->name }}
                        </a>
                      </div>
                    </td>
                    <td>{{ $vendor->phone ?? '-' }}</td>
                    <td>{{ $vendor->fax ?? '-' }}</td>
                    <td class="font-13">{!! nl2br($vendor->account_address) !!}</td>
                    <td class="font-13">{!! nl2br($vendor->billing_address) !!}</td>
                    <td class="font-13">{!! nl2br($vendor->shipping_address) !!}</td>
                    <td class="font-13">{!! nl2br($vendor->contact) !!}</td>
                    @can('delete vendor')
                      <td class="table-action text-right">
                        <a href="#"
                           data-target="#deleteItem"
                           data-toggle="modal"
                           class="delete-data-item-btn text-muted"
                           data-url="{{ route('general.vendors.destroy', $vendor->id) }}">
                          <i class="mdi mdi-delete"></i>
                          Delete
                        </a>
                      </td>
                    @endcan
                  </tr>
                @endforeach
                </tbody>
              </table>
            </div>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>

  <div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content modal-filled bg-danger">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-wrong h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">This will delete the vendor from the system.</p>
            <form action="#" method="post" id="delete-item-form">
              @csrf
              @method('delete')
              <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  <!-- container -->
@endsection
@include('shared.datatables')
@push('js')
  <!-- demo app -->
  <script src="{{ asset('js/pages/demo.products.js') }}"></script>
@endpush
