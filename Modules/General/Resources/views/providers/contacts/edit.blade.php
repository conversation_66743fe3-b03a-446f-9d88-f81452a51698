@extends('layouts.master')

@section('title', 'Edit Vendor')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('general.vendors.index') }}">Vendors</a></li>
              <li class="breadcrumb-item"><a href="{{ route('general.vendors.show', $vendor) }}">{{ $vendor->name }}</a>
              </li>
              <li class="breadcrumb-item active">Edit Vendor Contact</li>
            </ol>
          </div>
          <h4 class="page-title">Edit Vendor Contact</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-lg-6 col-md-10 col-sm-12">
        <div class="card">
          <div class="card-body px-5 py-4">
            <p>Use the form below to edit this contact</p>
            <form action="{{ route('general.vendors.contacts.update', [
                            'vendor' => $vendor,
                            'contact' => $contact
                            ]) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf
              @method('put')

              <div class="form-row mb-3">
                <div class="col-12">
                  <label for="customer-name">Name</label>
                  <input type="text"
                         name="name"
                         id="customer-name"
                         class="form-control @error('name') is-invalid @enderror"
                         value="{{ old('name', $contact->name) }}"
                         placeholder="E.g name of contact of person, department etc"
                         required>
                </div>
              </div>

              <div class="form-row mb-3">
                <div class="col-md-6 col-sm-12">
                  <label for="contact-email">Email</label>
                  <input type="email"
                         name="email"
                         id="contact-email"
                         class="form-control @error('email') is-invalid @enderror"
                         value="{{ old('email', $contact->email) }}">
                </div>
                <div class="col-md-6 col-sm-12">
                  <label for="contact-phone">Phone</label>
                  <input type="text"
                         name="phone"
                         id="contact-phone"
                         class="form-control @error('phone') is-invalid @enderror"
                         value="{{ old('phone', $contact->phone) }}">
                </div>
              </div>

              <div class="form-row mb-3">
                <div class="col-sm-12">
                  <label for="contact-other">Other</label>
                  <input type="text"
                         name="other"
                         id="contact-other"
                         class="form-control @error('other') is-invalid @enderror"
                         value="{{ old('other', $contact->other) }}"
                         placeholder="e.g Skype ID, Whereby Url etc">
                </div>
              </div>

              <div class="form-group d-flex justify-content-end">
                <a class="btn btn-light mr-2" href="{{ route('general.vendors.show', $vendor) }}">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Changes</button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
@endsection

