@extends('layouts.master')

@section('title', 'Add Vendor Contact')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('general.vendors.index') }}">Vendors</a></li>
              <li class="breadcrumb-item"><a href="{{ route('general.vendors.show', $vendor) }}">{{ $vendor->name }}</a>
              </li>
              <li class="breadcrumb-item active">Add Contact</li>
            </ol>
          </div>
          <h4 class="page-title">Add Contact <small>| {{ $vendor->name }}</small></h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-lg-6 col-md-8 col-sm-10">
        <div class="card">
          <div class="card-body px-5 py-4">
            <p>Fill the form below to add a vendor contact</p>
            <form action="{{ route('general.vendors.contacts.store', $vendor) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf

              <div class="form-row mb-3">
                <div class="col-12">
                  <label for="vendor-name">Name</label>
                  <input type="text"
                         name="name"
                         id="vendor-name"
                         class="form-control @error('name') is-invalid @enderror"
                         value="{{ old('name') }}"
                         placeholder="E.g name of contact of person, department etc"
                         required>
                </div>
              </div>

              <div class="form-row mb-3">
                <div class="col-md-6 col-sm-12">
                  <label for="contact-email">Email</label>
                  <input type="email"
                         name="email"
                         id="contact-email"
                         class="form-control @error('email') is-invalid @enderror"
                         value="{{ old('email') }}">
                </div>
                <div class="col-md-6 col-sm-12">
                  <label for="contact-phone">Phone</label>
                  <input type="text"
                         name="phone"
                         id="contact-phone"
                         class="form-control @error('phone') is-invalid @enderror"
                         value="{{ old('phone') }}">
                </div>
              </div>

              <div class="form-row mb-3">
                <div class="col-sm-12">
                  <label for="contact-other">Other</label>
                  <input type="text"
                         name="other"
                         id="contact-other"
                         class="form-control @error('other') is-invalid @enderror"
                         value="{{ old('other') }}"
                         placeholder="e.g Skype ID, Whereby Url etc">
                </div>
              </div>

              <div class="form-group d-flex justify-content-end">
                <a class="btn btn-light mr-2" href="{{ route('general.vendors.show', $vendor) }}"><i
                    class="mdi mdi-cancel mr-1"></i> Cancel</a>
                <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
@endsection

