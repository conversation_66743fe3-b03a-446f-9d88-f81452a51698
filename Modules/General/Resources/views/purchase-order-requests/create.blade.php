@extends('layouts.master')

@section('title', 'Need to Order List')

@push('styles')
  <!-- third party css -->
  <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
  <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
  <!-- third party css end -->
  @livewireStyles
@endpush

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item">
                <a href="{{ url('/') }}">Dashboard</a>
              </li>
              <li class="breadcrumb-item active">Need to Order List</li>
            </ol>
          </div>
          <h4 class="page-title">Need to Order List</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-12">
        @livewire('purchase-order-requests.purchase-order-request-component', [
          'vendorList' => $vendorList,
          'itemsAlreadyOrdered' => $itemsAlreadyOrdered
        ])

        <div class="card">
          <div class="card-header">
            <div class="h4">Items Already Ordered</div>
          </div>
          <div class="card-body py-4">
            <table class="table table-sm datatable">
              <thead>
              <tr>
                <th>Date</th>
                <th>P.O.</th>
                <th>Vendor</th>
                <th class="text-right">Qty</th>
                <th>Part No.</th>
                <th>MFG No.</th>
                <th>Description</th>
              </tr>
              </thead>
              <tbody>
              @foreach($itemsAlreadyOrdered as $purchaseOrderItem)
                <tr>
                  <td>{{ $purchaseOrderItem['po_date'] }}</td>
                  <td>{{ $purchaseOrderItem['po_number'] }}</td>
                  <td>{{ $purchaseOrderItem['po_vendor'] }}</td>
                  <td class="text-right">{{ $purchaseOrderItem['po_quantity'] }}</td>
                  <td>{{ $purchaseOrderItem['po_part_number'] }}</td>
                  <td>{{ $purchaseOrderItem['po_manufacturer_number'] }}</td>
                  <td>{{ $purchaseOrderItem['po_description'] }}</td>
                </tr>
              @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div> <!-- end col-->
    </div>
    <!-- end row-->
    <datalist id="vendorList">
      @foreach($vendorList as $vendorId => $vendorName)
        <option value="{{ $vendorName }}">{{ $vendorId . ': ' . $vendorName }}</option>
      @endforeach
    </datalist>
    @include('users::customers.shared.item-list')
  </div>
  <!-- container -->
@endsection

@push('js')
  <!-- third party js -->
  <script src="{{ asset('js/vendor/jquery.dataTables.min.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.bootstrap4.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.responsive.min.js') }}"></script>
  <script src="{{ asset('js/vendor/responsive.bootstrap4.min.js') }}"></script>
  <!-- third party js ends -->

  <!-- demo app -->
  <script src="{{ asset('js/pages/demo.products.js') }}"></script>
  @livewireScripts

@endpush

