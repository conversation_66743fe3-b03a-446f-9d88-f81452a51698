@extends('layouts.master')

@section('title', 'Purchase Order: ' . $purchaseOrder->number)

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item">
                <a href="{{ url('/') }}">Dashboard</a>
              </li>
              <li class="breadcrumb-item">
                <a
                  href="{{ route('general.purchase-orders.show', $purchaseOrder) }}">
                  Purchase Orders
                </a>
              </li>
              <li class="breadcrumb-item active">#{{ $purchaseOrder->number }}</li>
            </ol>
          </div>
          <h4 class="page-title">
            Purchase Order #{{ $purchaseOrder->number }}
          </h4>
        </div>
        <div class="card">
          <div class="card-body">
            <div class="d-flex flex-row justify-content-between">
              <div>
                <div>
                  <i class="mdi mdi-calendar text-muted"></i>
                  <strong>Created On:</strong>
                  {{ $purchaseOrder->formatted_date }}
                </div>
                @if ($purchaseOrder->completed)
                <div>
                  <i class="mdi mdi-calendar text-muted"></i>
                  <strong>Completed On:</strong>
                  {{ $purchaseOrder->formatted_completed_date }}
                </div>
                @endif
              </div>
              <div>
                <div>
                  <i class="mdi mdi-account text-muted"></i>
                  <strong>Created By:</strong>
                  <a
                    class="text-info"
                    href="{{ route('users.staff.show', $purchaseOrder->createdBy)
                  }}">
                    {{ $purchaseOrder->createdBy->name }}
                  </a>
                </div>

                @if ($purchaseOrder->completed)
                  <div>
                    <i class="mdi mdi-account text-muted"></i>
                    <strong>Completed By:</strong>
                    <a
                      class="text-info"
                      href="{{ route('users.staff.show', $purchaseOrder->completedBy)
                  }}">
                      {{ $purchaseOrder->createdBy->name }}
                    </a>
                  </div>
                @endif
              </div>
              <div>
                <i class="mdi mdi-information text-muted"></i>
                <strong>Status:</strong>
                {{ strtoupper($purchaseOrder->status) }}

                @if ($purchaseOrder->pending)
                  <button
                    type="button"
                    class="btn btn-sm btn-outline-secondary d-block mt-2
                    font-weight-bold shadow"
                    data-toggle="modal"
                    data-target="#completePurchaseOrder">
                    <i class="mdi mdi-check-all"></i>
                    Mark as Completed
                  </button>
                @endif
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5>Purchase Order Items</h5>
          </div>
          <div class="card-body py-4">
            <table class="table table-sm mb-3">
              <thead>
              <tr class="bg-secondary-lighten">
                <th>Qty</th>
                <th>Vendor</th>
                <th>Part No.</th>
                <th>Description</th>
                @if ($purchaseOrder->pending)
                <th class="text-right">Action</th>
                @endif
              </tr>
              </thead>
              <tbody>
              @foreach($purchaseOrder->purchaseOrderItems as $purchaseOrderItem)
                <tr>
                  <td>{{ $purchaseOrderItem->quantity }}</td>
                  <td>{{ $purchaseOrderItem->vendor->name }}</td>
                  <td>{{ $purchaseOrderItem->item->code }}</td>
                  <td>{{ $purchaseOrderItem->item->description }}</td>
                  @if ($purchaseOrder->pending)
                  <td class="text-right">
                    <button
                      type="button"
                      class="btn btn-link btn-xs text-decoration-none text-danger p-0
                      delete-item-btn"
                      data-target="#deleteItem"
                      data-toggle="modal"
                      data-url="{{ route('general.purchase-order-items.destroy', $purchaseOrderItem) }}">
                      <i class="mdi mdi-trash-can"></i>
                    </button>
                  </td>
                  @endif
                </tr>
              @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
  <div id="completePurchaseOrder"
       class="modal fade"
       tabindex="-1"
       role="dialog"
       style="display: none;"
       aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content modal-filled bg-success">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-wrong h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">
              This will confirm the purchase order. Are you sure about this?
            </p>
            <form
              action="{{ route('general.purchase-orders.complete', $purchaseOrder)
            }}"
              method="post">
              @csrf
              @method('put')
              <button type="button" class="btn btn-secondary text-white my-2 mr-1"
                      data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-light my-2 px-3">Yes!</button>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  @if ($purchaseOrder->pending)
  <div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content modal-filled bg-danger">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-wrong h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">This will delete the item entry from the system.</p>
            <form action="#" method="post" id="delete-item-form">
              @csrf
              @method('delete')
              <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  @endif
@endsection
@push('js')
  <script src="{{ asset('js/pages/demo.products.js') }}"></script>
@endpush
