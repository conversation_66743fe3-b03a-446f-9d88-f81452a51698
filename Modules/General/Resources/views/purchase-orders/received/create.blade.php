@extends('layouts.master')

@section('title', 'Receive Purchase Order')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item">
                <a class="text-info" href="{{ url('/') }}">Dashboard</a>
              </li>
              <li class="breadcrumb-item">
                <a class="text-info" href="{{ route('general.purchase-orders.index') }}">Purchase Orders</a>
              </li>
              <li class="breadcrumb-item">
                <a class="text-info" href="{{ route('general.purchase-orders.show', $purchaseOrder) }}">#{{ $purchaseOrder->number }}</a>
              </li>
              <li class="breadcrumb-item active">Receive Purchase Order</li>
            </ol>
          </div>
          <h4 class="page-title">Receive Purchase Order</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-12">
        <div class="row">
          <div class="col-md-6"></div>
          <div class="col-md-6">
            <h4>Purchase Order <span class="d-inline-block px-3 py-1 bg-light font-weight-bold">{{ $purchaseOrder->number }}</span></h4>
            <p>
              Requisitioner: {{ $purchaseOrder->createdBy->name }} <br>
              Purchase Order Date: {{ $purchaseOrder->formatted_date }} <br>
              Ship Via: {{ $purchaseOrder->ship_via }} <br>
              Date Required: {{ $purchaseOrder->required_by }}
            </p>
          </div>
        </div>
        <div class="row mb-4">
          <div class="col-md-6">
            <strong>To:</strong> <br>
            <address>
              {!! $purchaseOrder->vendor_address !!}
            </address>
          </div>
          <div class="col-md-6">
            <strong>Ship To:</strong> <br>
            <address>
              {!! nl2br($purchaseOrder->shipping_address) !!}
            </address>
          </div>
        </div>
        <form action="{{ route('general.purchase-orders.receive.store', $purchaseOrder) }}"
              method="post" id="receive-purchase-order-form">
          @csrf
          <table class="table table-sm" id="purchase-order-items-tables">
            <thead>
            <tr>
              <th>Line</th>
              <th>Part#</th>
              <th>Description</th>
              <th class="text-right">Ordered</th>
              <th class="d-flex flex-column align-items-start">
                <a class="btn btn-sm btn-outline-secondary p-0 px-1" href="#" id="fill-received-btn">
                  <i class="mdi mdi-format-color-fill"></i>
                  Fill
                </a>
                <span>Received</span>
              </th>
              <th>Back Ordered</th>
              <th>Unit</th>
            </tr>
            </thead>
            <tbody>
            @foreach($purchaseOrder->purchaseOrderItems as $purchaseItem)
              <tr>
                <td>{{ $purchaseItem->line_number }}</td>
                <td>{{ $purchaseItem->part_number }}</td>
                <td>{{ $purchaseItem->description }}</td>
                <td class="text-right quantity-ordered hide-in-modal">{{ $purchaseItem->quantity }}</td>
                <td class="text-right hide-in-modal">
                  <input type="hidden" name="purchase_order_items[]" value="{{ $purchaseItem->id }}">
                  <input type="number"
                         name="quantity_received[]"
                         class="form-control py-0 quantity-received"
                         style="max-width: 150px; line-height: 1"
                         data-quantity="{{ $purchaseItem->quantity }}"
                         max="{{ $purchaseItem->quantity }}" required>
                </td>
                <td class="back-ordered-quantity"></td>
                <td>{{ $purchaseItem->unit }}</td>
              </tr>
            @endforeach
            </tbody>
          </table>
          <div class="form-group text-right">
            <a class="btn btn-outline-secondary" href="{{ route('general.purchase-orders.show', $purchaseOrder) }}">
              <i class="mdi mdi-arrow-left"></i> Cancel
            </a>
            <button type="button"
                    class="btn btn-primary"
                    data-toggle="modal"
                    data-target="#finishPurchaseOrderReceptionModal"
                    id="finish-receiving-purchase-order-btn">
              <i class="mdi mdi-check-all"></i>
              Finish
            </button>
          </div>
        </form>

      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->

  <div id="finishPurchaseOrderReceptionModal"
       class="modal fade"
       tabindex="-1"
       role="dialog"
       style="display: none;"
       aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-warning text-info h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">Was anything Back Ordered?</p>
            <p>Please check the Order <strong>BEFORE</strong> Proceeding!</p>
            <div class="d-flex">
              <span class="w-50 mr-2">
                <button type="submit"
                        class="btn btn-outline-info btn-block my-2"
                        form="receive-purchase-order-form">
                  <i class="mdi mdi-cancel"></i>
                  No
                </button>
              </span>
              <span class="w-50">
                <button type="button"
                        class="btn btn-info btn-block my-2 mr-2"
                        data-dismiss="modal">
                  <i class="mdi mdi-check"></i>
                  Yes
                </button>
              </span>
            </div>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  <div id="createBackOrderModal"
       class="modal fade"
       tabindex="-1"
       role="dialog"
       style="display: none;"
       aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-warning text-info h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3 mb-1">The following items will be back ordered</p>
            <p>Please review for Accuracy <strong>BEFORE</strong> Proceeding!</p>
            <div class="table-responsive">
              <table class="table table" id="back-ordered-items-list">
                <thead>
                  <tr>
                    <th>Line</th>
                    <th>Description</th>
                    <th>Back Ordered</th>
                    <th>Unit</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
            <div class="d-flex">
              <span class="w-50 mr-2">
                <button type="button"
                        class="btn btn-outline-info btn-block my-2"
                        data-dismiss="modal">
                  <i class="mdi mdi-check"></i>
                  Cancel
                </button>
              </span>
              <span class="w-50">
                <button type="submit"
                        class="btn btn-info btn-block my-2"
                        form="receive-purchase-order-form">
                  <i class="mdi mdi-cancel"></i>
                  Create Back Order
                </button>
              </span>

            </div>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
@endsection
@push('js')
  <script>
    let createsBackOrder = false;
    let fillReceivedBtn = $('#fill-received-btn');
    let quantityReceivedInputs = $('.quantity-received');
    let finishPOBtn = $('#finish-receiving-purchase-order-btn');

    fillReceivedBtn.click(function () {
      let quantitiesOrdered = $('.quantity-ordered');

      quantitiesOrdered.each(function (i, qo) {
        let qty = $(this).text();
        let row = $(this).parent();
        let qr = row.find('.quantity-received');
        qr.val(qty);
        row.find('.back-ordered-quantity').text(0);
      });

      finishPOBtn.attr('data-target', '#finishPurchaseOrderReceptionModal');

      return false;
    });

    $('body').on('change', '.quantity-received', function () {
      let qtyInput = $(this);
      let qtyBackOrdered = qtyInput.data('quantity') - (qtyInput.val() * 1);
      let qtyInputRowElement = qtyInput.parent().parent();
      qtyInputRowElement.find('.back-ordered-quantity').text(qtyBackOrdered);
      qtyInputRowElement.attr('data-back-order', qtyBackOrdered > 0);

      // $('.quantity-received').each(function (i, eachQtyInput) {
      //   let qtyBackOrdered = $(eachQtyInput).data('quantity') - ($(eachQtyInput).val() * 1);
      //
      //   if (qtyBackOrdered > 0) {
      //     createsBackOrder = true;
      //     return false;
      //   }
      // });
      let inputsToBackOrder = $.grep($('.quantity-received'),function (input) {
        return $(input).data('quantity') - ($(input).val() * 1) > 0
      });

      if (inputsToBackOrder.length > 0) {
        finishPOBtn.attr('data-target', '#createBackOrderModal');
      } else {
        finishPOBtn.attr('data-target', '#finishPurchaseOrderReceptionModal');
      }
    });

    finishPOBtn.click(function () {
      let btn = $(this);

      if (btn.data('target') === '#createBackOrderModal') {
        listBackOrderedItems();
      }
    });

    function listBackOrderedItems() {
      let rows = $('#purchase-order-items-tables tr[data-back-order="true"]').clone();
      let listTable = $('#back-ordered-items-list');
      console.log(rows, listTable);
      // Remove any existing rows
      listTable.find('tbody > tr').remove();
      rows.find('.hide-in-modal').remove();
      listTable.find('tbody').append(rows);
    }
  </script>
@endpush
