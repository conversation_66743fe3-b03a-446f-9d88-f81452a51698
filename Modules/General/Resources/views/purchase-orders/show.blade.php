@extends('layouts.master')

@section('title', 'Purchase Order: ' . $purchaseOrder->number)

@push('styles')
  @livewireStyles
@endpush

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item">
                <a href="{{ url('/') }}">Dashboard</a>
              </li>
              <li class="breadcrumb-item">
                <a href="{{ route('general.purchase-orders.index') }}">
                  Purchase Orders
                </a>
              </li>
              <li class="breadcrumb-item active">#{{ $purchaseOrder->number }}</li>
            </ol>
          </div>
          <h4 class="page-title">
            Purchase Order #{{ $purchaseOrder->number }}
          </h4>
        </div>
        <div class="text-right mb-3">
          @canany(['update purchase order', 'print purchase order', 'receive-purchase-order'])
            @can('print purchase order')
              <a class="btn btn-primary" href="{{ route('general.purchase-orders.print', $purchaseOrder) }}">
                <i class="mdi mdi-printer mr-1"></i>
                Print PO
              </a>
            @endcan
            <div class="btn-group">
              <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown"
                      aria-haspopup="true" aria-expanded="false">
                <i class="mdi mdi mdi-dots-vertical"></i> Actions
              </button>
              <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end"
                   style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(285px, 37px, 0px);">
                @can('print purchase order')
                  @if(! empty($purchaseOrder->purchase_order_id))
                  <a
                    class="dropdown-item"
                    href="{{ route('general.purchase-orders.print.received', $purchaseOrder->purchase_order_id) }}"
                    target="_blank">
                    <i class="mdi mdi-printer mr-1"></i>
                    Print Received PO
                  </a>
                  <a
                    class="dropdown-item"
                    href="{{ route('general.purchase-orders.print.back-ordered', $purchaseOrder) }}"
                    target="_blank">
                    <i class="mdi mdi-printer mr-1"></i>
                    Print BackOrdered PO
                  </a>
                  @endif
                  <a
                    class="dropdown-item"
                    href="#"
                    data-target="#emailPurchaseOrderModal"
                    data-toggle="modal">
                    <i class="mdi mdi-send mr-1"></i>
                    Email PO
                  </a>
                @endcan
                @can('receive purchase order')
                  @if($purchaseOrder->notReceived())
                  <a class="dropdown-item" href="{{ route('general.purchase-orders.receive.create', $purchaseOrder) }}">
                    <i class="mdi mdi-truck-delivery mr-1"></i>
                    Receive PO
                  </a>
                  @endif
                @endcan
                @can('update purchase order')
                  <a class="dropdown-item" href="#" data-toggle="modal" data-target="#completePurchaseOrderModal">
                    <i class="mdi mdi-check-all mr-1"></i>
                    Mark As Completed
                  </a>
                  <a class="dropdown-item" href="#" data-toggle="modal" data-target="#uploadAcknowledgementModal">
                    <i class="mdi mdi-file-upload mr-1"></i>
                    Upload Acknowledgement
                  </a>
                @endcan
                @can('delete purchase order')
                  <div class="dropdown-divider"></div>
                  <a class="dropdown-item text-dark" href="#" data-toggle="modal" data-target="#deletePurchaseOrderModal">
                    <i class="mdi mdi-delete mr-1"></i>
                    Delete PO
                  </a>
                @endcan
              </div>
            </div>
          @endcanany
        </div>
        <div class="card">
          <div class="card-body">
            @livewire('purchase-orders.show-purchase-order-component', ['purchaseOrder' => $purchaseOrder, 'isReceived' => $purchaseOrder->received])
          </div>
        </div>
        <div class="card">
          <div class="card-body">
            Acknowledgement File:
            @if($purchaseOrder->acknowledgement_file)
              <form class="d-inline-block mr-1"
              action="{{ route('general.purchase-orders.acknowledgement.show', ['purchaseOrder' => $purchaseOrder, 'type' => 'download']) }}">
                <button class="btn btn-link">
                  <i class="mdi mdi-file-download"></i> Download File
                </button>
              </form>
              <form class="d-inline-block"
                    action="{{ route('general.purchase-orders.acknowledgement.show', [
                      'purchaseOrder' => $purchaseOrder,
                      'type' => 'file']) }}">
                <button class="btn btn-link">
                  <i class="mdi mdi-eye-outline"></i> View File
                </button>
              </form>
              <button
                class="btn btn-link text-danger"
                data-target="#deleteAcknowledgementFileModal"
                data-toggle="modal">
                <i class="mdi mdi-delete"></i> Delete File
              </button>
            @else
              <span class="text-muted">
              No file uploaded.
              <a href="#"
                 data-target="#uploadAcknowledgementModal"
                 data-toggle="modal">
                Upload Acknowledgement
              </a>
            </span>
            @endif
          </div>
        </div>
      </div>
    </div>
    <!-- end page title -->
  </div>

  @include('users::customers.shared.item-list', ['itemList' => $itemListOptions])

  <div id="uploadAcknowledgementModal"
       class="modal fade"
       tabindex="-1"
       role="dialog"
       style="display: none;"
       aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-upload text-info h1"></i>
            <h4 class="mt-2">File Upload!</h4>
            <p class="mt-3">Choose the acknowledgement file to upload for this Purchase Order.</p>
            <form action="{{ route('general.purchase-orders.acknowledgement.store', $purchaseOrder) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf
              <div class="form-group">
                <input type="file" class="form-control" name="acknowledgement_file" accept=".pdf,.docx,.doc,.jpg,.jpeg,.png">
              </div>
              <div class="d-flex">
                <span class="w-50 pr-2">
                  <button
                    type="button"
                    class="btn btn-outline-info btn-block my-2 mr-2"
                    data-dismiss="modal">Cancel
                  </button>
                </span>
                <span
                  class="w-50">
                  <button
                    type="submit"
                    class="btn btn-info btn-block my-2">
                    <i class="mdi mdi-file-upload-outline"></i> Upload
                  </button>
                </span>
              </div>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  <div id="completePurchaseOrderModal"
       class="modal fade"
       tabindex="-1"
       role="dialog"
       style="display: none;"
       aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-warning text-info h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">You are about to mark this purchase order as <strong>completed</strong>. Are you sure about
              this?</p>
            <form action="{{ route('general.purchase-orders.complete', $purchaseOrder) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf
              @method('put')
              <div class="d-flex">
                <span class="w-50 pr-2">
                  <button
                    type="button"
                    class="btn btn-outline-info btn-block my-2 mr-2"
                    data-dismiss="modal">No
                  </button>
                </span>
                <span class="w-50">
                  <button
                    type="submit"
                    class="btn btn-info btn-block my-2">
                    <i class="mdi mdi-check"></i> Yes
                  </button>
                </span>
              </div>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  <div id="deletePurchaseOrderModal"
       class="modal fade"
       tabindex="-1"
       role="dialog"
       style="display: none;"
       aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content bg-danger text-white">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-warning h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">You are about to delete this purchase order. Are you sure about this?</p>
            <form action="{{ route('general.purchase-orders.destroy', $purchaseOrder) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf
              @method('delete')
              <div class="d-flex">
                <span class="w-50 pr-2">
                  <button type="button"
                          class="btn btn-outline-light btn-block my-2 mr-2"
                          data-dismiss="modal">No</button>
                </span>
                <span class="w-50">
                  <button type="submit"
                          class="btn btn-light btn-block my-2">
                    <i class="mdi mdi-check"></i> Yes</button>
                </span>
              </div>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>

  <div id="deleteAcknowledgementFileModal"
       class="modal fade"
       tabindex="-1"
       role="dialog"
       style="display: none;"
       aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content bg-danger text-white">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-warning h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">You are about to delete the acknowledgement file. You cannot undo this action. Are you sure about this?</p>
            <form action="{{ route('general.purchase-orders.acknowledgement.destroy', $purchaseOrder) }}"
                  method="post">
              @csrf
              @method('delete')
              <div class="d-flex">
                <span class="w-50 pr-2">
                  <button type="button"
                          class="btn btn-outline-light btn-block my-2 mr-2"
                          data-dismiss="modal">No</button>
                </span>
                <span class="w-50">
                  <button type="submit"
                          class="btn btn-light btn-block my-2">
                    <i class="mdi mdi-check"></i> Yes</button>
                </span>
              </div>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>

  <div
    id="emailPurchaseOrderModal"
    class="modal fade"
    tabindex="-1"
    role="dialog"
    style="display: none;"
    aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-body p-4">
          <div class="d-flex justify-content-center align-items-center">
            <i class="mdi mdi-alert h1 mr-2"></i>
            <h4 class="mt-2">Confirm Action!</h4>
          </div>
          <div class="mt-3">
            <p class="text-muted">This will send <strong>Purchase Order# {{ $purchaseOrder->number }}</strong> to the vendor's email(s) below. Are you sure about this?</p>
            <form action="{{ route('general.purchase-orders.send', $purchaseOrder) }}" method="post">
              @csrf
              <div class="form-group">
                <label for="invoice-emails">Recipients <small class="text-muted">- Separate multiple emails with a comma.</small></label>
                <input
                  class="form-control"
                  type="text"
                  name="recipients"
                  id="invoice-emails"
                  value="{{ old('recipients', optional($purchaseOrder->vendor)->invoice_recipients) }}"
                  required>
              </div>
              <div class="form-group">
                <label>Message <small>- customize the message to send along with the purchase order.</small></label>
                <textarea
                  class="form-control"
                  name="body"
                  rows="5"
                  required>{{ settings('global.emails.purchase_orders.message', '') }}</textarea>
                <small>This text will be replaced: <strong>$vendor</strong> by Vendor Name, <strong>$purchaseOrder</strong> by Purchase Order Number</small>
              </div>
              <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-light my-2 mr-2 px-3" data-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary my-2 px-3">Yes, Send Email <i class="mdi mdi-send ml-2"></i></button>
              </div>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  <!-- container -->
@endsection

@push('js')
  @livewireScripts
@endpush
