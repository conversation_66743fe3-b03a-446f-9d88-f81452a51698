<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ config('app.name') }} Purchase Order #{{ $purchaseOrder->number }}</title>
  @include('shared.print.default-print-styles')
</head>
<body>
<div class="mb-sm" style="overflow: auto;">
  <div class="mb-sm text-center">
    <h2>Back Order PO</h2>
  </div>
  <div class="mb-sm">
    @include('shared.print.pacific_logo_address')
    <div class="text-center" style="width: 49%; display: inline-block; float: right;">
      <h3 style="margin: 0 0 8px; color: #888;">PURCHASE ORDER</h3>

      <table class="table text-center border-0">
        <tr class="text-dark">
          <td class="text-right border-0">Purchase Order Number:</td>
          <td class="text-left border-0">{{ $purchaseOrder->number }}</td>
        </tr>
        <tr>
          <td class="text-right border-0">Purchase Order Date:</td>
          <td class="text-left border-0">{{ $purchaseOrder->formatted_date }}</td>
        </tr>
        <tr>
          <td class="text-right border-0">Ship Via:</td>
          <td class="text-left border-0">{{ $purchaseOrder->ship_via }}</td>
        </tr>
        <tr>
          <td class="text-right border-0">Date Required:</td>
          <td class="text-left border-0">{{ $purchaseOrder->required_by }}</td>
        </tr>
      </table>
    </div>
    <div style="clear: both;"></div>
  </div>
  <div style="">
    <table class="table border-0">
      <tr>
        <td class="border-0" style="width: 49%"><strong>To:</strong></td>
        <td class="border-0"><strong>Ship To:</strong></td>
      </tr>
      <tr>
        <td class="border-0">
          <div style="">{!! nl2br($purchaseOrder->vendor_address) !!}</div>
          <br>
        </td>
        <td class="border-0">
          <div style="">{!! nl2br($purchaseOrder->shipping_address) !!}</div>
          <br>
        </td>
      </tr>
    </table>
    <table class="table border-0">
      <tr>
        <td class="border-0" style="border-top: 1px dotted; width: 49%;">
          <table>
            <tr>
              <td class="text-right border-0">Phone:</td>
              <td class="border-0">{{ optional($purchaseOrder->vendor)->phone }}</td>
            </tr>
            <tr>
              <td class="text-right border-0">Fax:</td>
              <td class="border-0">{{ optional($purchaseOrder->vendor)->fax }}</td>
            </tr>
          </table>
        </td>
        <td class="border-0" style="border-top: 1px dotted">
          <table>
            <tr>
              <td class="text-right border-0">Requisitioner:</td>
              <td class="border-0">{{ $purchaseOrder->createdBy->name }}</td>
            </tr>
            <tr>
              <td class="text-right border-0">Phone:</td>
              <td class="border-0">(*************</td>
            </tr>
            <tr>
              <td class="text-right border-0">Fax:</td>
              <td class="border-0">(*************</td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div> <!-- end col-->
</div>

<table class="table mb-sm border-0">
  <thead>
    <tr class="text-dark table-header" style="font-weight: bold">
      <th class="p-1 text-right border-dotted">Line</th>
      <th class="p-1 border-dotted">Part#</th>
      <th class="p-1 border-dotted">Description</th>
      <th class="p-1 text-right border-dotted">Qty</th>
      <th class="p-1 text-right border-dotted">Sh</th>
      <th class="p-1 text-right border-dotted">BO</th>
      <th class="p-1 border-dotted">Unit</th>
      <th class="p-1 text-right border-dotted">Price</th>
      <th class="p-1 text-right border-dotted">Amount</th>
    </tr>
  </thead>

  @foreach($purchaseOrder->purchaseOrderItems as $item)
  <tr>
    <td class="text-right border-0">{{ $item->line_number }}</td>
    <td class="border-0">{{ $item->manufacturer_part_number }}</td>
    <td class="border-0">{{ $item->description }}</td>
    <td class="text-right border-0">{{ $item->quantity }}</td>
    <td class="text-right border-0"></td>
    <td class="text-right border-0"></td>
    <td class="border-0">{{ $item->unit }}</td>
    <td class="text-right border-0">{{ number_format($item->price, 4) }}</td>
    <td class="text-right border-0">{{ number_format($item->actual_amount, 2) }}</td>
  </tr>
  @endforeach
  <tr>
    <td colspan="8"
        class="border-0"
        style="border-top: 1px dotted; border-bottom: 1px dotted">
      Code: {{ $purchaseOrder->freight_code }}
    </td>
    <td class="text-right border-0"
        style="border-top: 1px dotted; border-bottom: 1px dotted">
      <strong>Total: ${{ number_format($purchaseOrder->purchaseOrderItems->sum('actual_amount'), 2) }}</strong>
    </td>
  </tr>
</table>

<table class="border mb-sm" style="border: none; width: 100%;">
  <tr>
    <td style="border: none; padding: 4px;"
        class="text-center"
        colspan="8">
      <p class="text-center">****** Please Acknowledge Receipt of this Purchase Order ******</p>
      <p>*** PLEASE SHIP VIA - {{ strtoupper($purchaseOrder->ship_via) }} ***</p>
    </td>
  </tr>
</table>

<script type="text/php">
    $text = "Page {PAGE_NUM} (PO# {{ $purchaseOrder->number }})";
    $size = 10;
    $font = $fontMetrics->getFont("Helvetica");
    $width = $fontMetrics->get_text_width($text, $font, $size);
    $x = $pdf->get_width() / 2 - 50;
    $y = $pdf->get_height() - 30;
    $pdf->page_text($x, $y, $text, $font, $size, array(0,0,0));
</script>
</body>
</html>
