<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ config('app.name') }} Purchase Order #{{ $purchaseOrder->number }}</title>
  <style>
    .table {
      border: 1px solid #333;
      width: 100%;
      padding: 0;
      border-spacing: 0;
    }
    .table-header {
      background-color: #999;
      font-size: 13px;
      text-transform: uppercase;
    }
    .table-borderless {
      border: none;
    }
    .border-dotted {
      border-style: dotted;
    }
    td, th {
      border: 1px solid #333;
      padding: 4px;
    }
    .mb-sm {
      margin-bottom: 14px;
    }
    .mb-1 {
      margin-bottom: 4px;
    }
    .text-center {
      text-align: center;
    }
    .text-right {
      text-align: right;
    }
    .text-left {
      text-align: left;
    }
  </style>
</head>
<body>
<div class="mb-sm" style="overflow: auto;">
  <div class="mb-sm">
    @include('shared.print.pacific_logo_address')
    <div class="text-center" style="width: 49%; display: inline-block; float: right;">
      <h3 style="margin: 0 0 8px; color: #888;">PURCHASE ORDER</h3>

      <table class="table text-center table-borderless">
        <tr class="text-dark">
          <td class="text-right table-borderless">Purchase Order Number:</td>
          <td class="text-left table-borderless">{{ $purchaseOrder->number }}</td>
        </tr>
        <tr>
          <td class="text-right table-borderless">Purchase Order Date:</td>
          <td class="text-left table-borderless">{{ $purchaseOrder->formatted_date }}</td>
        </tr>
        <tr>
          <td class="text-right table-borderless">Ship Via:</td>
          <td class="text-left table-borderless">{{ $purchaseOrder->ship_via }}</td>
        </tr>
        <tr>
          <td class="text-right table-borderless">Date Required:</td>
          <td class="text-left table-borderless">{{ $purchaseOrder->required_by }}</td>
        </tr>
      </table>
    </div>
    <div style="clear: both;"></div>
  </div>
  <div style="">
    <table class="table table-borderless">
      <tr>
        <td class="table-borderless" style="width: 49%"><strong>To:</strong></td>
        <td class="table-borderless"><strong>Ship To:</strong></td>
      </tr>
      <tr>
        <td class="table-borderless">
          <div style="">{!! nl2br($purchaseOrder->vendor_address) !!}</div>
          <br>
        </td>
        <td class="table-borderless">
          <div style="">{!! nl2br($purchaseOrder->shipping_address) !!}</div>
          <br>
        </td>
      </tr>
    </table>
    <table class="table table-borderless">
      <tr>
        <td class="table-borderless" style="border-top: 1px dotted; width: 49%;">
          <table>
            <tr>
              <td class="text-right table-borderless">Phone:</td>
              <td class="table-borderless">{{ optional($purchaseOrder->vendor)->phone }}</td>
            </tr>
            <tr>
              <td class="text-right table-borderless">Fax:</td>
              <td class="table-borderless">{{ optional($purchaseOrder->vendor)->fax }}</td>
            </tr>
          </table>
        </td>
        <td class="table-borderless" style="border-top: 1px dotted">
          <table>
            <tr>
              <td class="text-right table-borderless">Requisitioner:</td>
              <td class="table-borderless">{{ $purchaseOrder->createdBy->name }}</td>
            </tr>
            <tr>
              <td class="text-right table-borderless">Phone:</td>
              <td class="table-borderless">(*************</td>
            </tr>
            <tr>
              <td class="text-right table-borderless">Fax:</td>
              <td class="table-borderless">(*************</td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div> <!-- end col-->
</div>

<table class="table mb-sm table-borderless">
  <tr class="text-dark table-header" style="font-weight: bold">
    <td class="p-1 text-right border-dotted">Line</td>
    <td class="p-1 border-dotted">Description</td>
    <td class="p-1 text-right border-dotted">Qty</td>
    <td class="p-1 text-right border-dotted">Sh</td>
    <td class="p-1 text-right border-dotted">BO</td>
    <td class="p-1 border-dotted">Unit</td>
    <td class="p-1 text-right border-dotted">Price</td>
    <td class="p-1 text-right border-dotted">Amount</td>
  </tr>
  @foreach($purchaseOrder->purchaseOrderItems as $item)
  <tr>
    <td class="text-right table-borderless">{{ $item->line_number }}</td>
    <td class="table-borderless">{{ $item->description }}</td>
    <td class="text-right table-borderless">{{ $item->quantity }}</td>
    <td class="text-right table-borderless">{{ $item->formatted_quantity_received }}</td>
    <td class="text-right table-borderless">{{ $item->quantity_back_ordered }}</td>
    <td class="table-borderless">{{ $item->unit }}</td>
    <td class="text-right table-borderless">{{ number_format($item->price, 4) }}</td>
    <td class="text-right table-borderless">{{ number_format($item->amount, 2) }}</td>
  </tr>
  @endforeach
  <tr>
    <td colspan="7"
        class="table-borderless"
        style="border-top: 1px dotted; border-bottom: 1px dotted">
      Code: {{ $purchaseOrder->freight_code }}
    </td>
    <td class="text-right table-borderless"
        style="border-top: 1px dotted; border-bottom: 1px dotted">
      <strong>Total: ${{ number_format($purchaseOrder->purchaseOrderItems->sum('amount'), 2) }}</strong>
    </td>
  </tr>
</table>

<table class="border mb-sm" style="border: none; width: 100%;">
  <tr>
    <td style="border: none; padding: 4px;"
        class="text-center"
        colspan="8">
      <p class="text-center">****** Please Acknowledge Receipt of this Purchase Order ******</p>
      <p>*** PLEASE SHIP VIA - {{ strtoupper($purchaseOrder->ship_via) }} ***</p>
    </td>
  </tr>
</table>

<script type="text/php">
    $text = "Page {PAGE_NUM}";
    $size = 10;
    $font = $fontMetrics->getFont("Verdana");
    $width = $fontMetrics->get_text_width($text, $font, $size);
    $x = ($pdf->get_width() - $width) + 2;
    $y = 38;
    $pdf->page_text($x, $y, $text, $font, $size, array(0,0,0));
</script>
</body>
</html>
