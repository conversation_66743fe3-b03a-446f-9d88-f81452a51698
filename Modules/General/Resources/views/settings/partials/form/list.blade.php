@foreach(data_get($setting, 'value', []) as $optionKey => $option)
    <div class="d-flex align-items-center">
        <input
            type="text"
            class="form-control mb-1"
            name="{{ data_get($setting, 'input.name') }}[]"
            id="shipping_modes_{{ $optionKey }}"
            value="{{ $option }}">
        <button type="button" class="btn btn-sm text-danger remove-input-option" data-target="#deleteItem" data-toggle="modal"><i class="mdi mdi-delete"></i></button>
    </div>

@endforeach
<input
    type="text"
    class="form-control"
    name="{{ data_get($setting, 'input.name') }}[]"
    id="{{ $settingKey }}"
    placeholder="Add option">
