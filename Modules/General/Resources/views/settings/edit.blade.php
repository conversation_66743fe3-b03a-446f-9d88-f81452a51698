@extends('layouts.master')

@section('title', 'Edit Category')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.categories.index') }}">Categories</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.categories.show', $category) }}">{{ $category->name }}</a></li>
                            <li class="breadcrumb-item active">Edit Category</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Category</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-8">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Fill the form below to edit this category</p>
                        <form action="{{ route('general.categories.update', $category) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')
                            <div class="form-row mb-3">
                                <div class="col-12">
                                    <label for="category-name">Name</label>
                                    <input type="text"
                                           name="name"
                                           id="category-name"
                                           class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name', $category->name) }}">
                                </div>
                            </div>
                            <div class="form-row mb-3">
                                <div class="col-6">
                                    <label for="sub-category">Parent Category</label>
                                    <select class="form-control select2 @error('category_id') is-invalid @enderror"
                                            name="category_id"
                                            data-toggle="select2"
                                            id="sub-category">
                                        <option value="">Select Parent Category</option>
                                        @foreach($categoryList as $key => $categoryItem)
                                            <option value="{{ $key }}" {{ old('category_id', optional($parentCategory)->id) == $key ? 'selected' : '' }}>{{ $categoryItem }}</option>
                                        @endforeach
                                    </select>
                                    <small>Leave blank for parent categories.</small>
                                </div>
                                <div class="col-6">
                                    <label for="category-type">Category Type</label>
                                    <select class="form-control @error('type') is-invalid @enderror"
                                            name="type"
                                            id="category-type"
                                            required>
                                        <option value="">Select Category Type</option>
                                        @foreach($categoryTypeList as $key => $type)
                                            <option value="{{ $key }}" {{ old('type', $category->type) == $key ? 'selected' : '' }}>{{ $type }}</option>
                                        @endforeach
                                    </select>
                                    <small>Will automatically match parent category type</small>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label for="category-description">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror"
                                          name="description"
                                          id="category-description"
                                          rows="5">{{ old('description', $category->description) }}</textarea>
                            </div>

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('general.categories.show', $category) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

