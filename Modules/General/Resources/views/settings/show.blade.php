@extends('layouts.master')

@section('title', $category->name)

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.categories.index') }}">Categories</a></li>
                            <li class="breadcrumb-item active">Category Details</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Category Details</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-8">
                <div class="card">
                    <div class="card-body">
                        @canany(['update category', 'delete category'])
                        <div class="card-widgets">
                            <div class="btn-group">
                                <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="mdi mdi mdi-dots-vertical"></i> Actions
                                </button>
                                <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(285px, 37px, 0px);">
                                    @can('update category')
                                    <a class="dropdown-item" href="{{ route('general.categories.edit', $category) }}">Edit Category</a>
                                    @endcan
                                    @can('delete category')
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="#" data-toggle="modal" data-target="#deleteCategory">Delete Category</a>
                                    @endcan
                                </div>
                            </div>
                        </div>
                        @endcanany
                        <div class="row">
                            <div class="col-12">
                                <!-- Category title -->
                                <h3 class="mt-0">{{ $category->name }} <span class="text-muted">- {{ ucfirst($category->type) }}</span> </h3>
                                <p class="mb-4">Added Date: {{ $category->created_at->toDateString() }}</p>

                                <!-- Category description -->
                                <div class="mb-4">
                                    <h6 class="font-14 text-muted">Category Parent:</h6>
                                    <p>
                                        @if($parentCategory)
                                            <a href="{{ route('general.categories.show', $parentCategory) }}">
                                                <strong>{{ $parentCategory->name }}</strong>
                                            </a>
                                        @else
                                        -
                                        @endif
                                    </p>
                                </div>

                                <!-- Category description -->
                                <div class="mb-4">
                                    <h6 class="font-14">Description:</h6>
                                    <p>{!! $category->description !!}</p>
                                </div>

                                <h6 class="font-14">Sub Categories ({{ $category->children->count()  }})</h6>
                                <div class="d-flex mb-4">
                                    @forelse($category->children as $subCategory)
                                        <a class="d-inline-block mr-2 badge badge-secondary-lighten px-3 py-1 font-13" href="{{ route('general.categories.show', $subCategory) }}">{{ $subCategory->name }}</a>
                                    @empty
                                        <p>No sub-categories</p>
                                    @endforelse
                                </div>

                            </div> <!-- end col -->
                        </div> <!-- end row-->
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

{{--        @if($category->products->count())--}}
{{--        <div class="row justify-content-center">--}}
{{--            <div class="col-8">--}}
{{--                <div class="card">--}}
{{--                    <div class="card-body">--}}
{{--                        <table class="table table-striped datatable">--}}
{{--                            <thead>--}}
{{--                                <tr>--}}
{{--                                    <th>Name</th>--}}
{{--                                    <th>Price</th>--}}
{{--                                    <th>Quantity</th>--}}
{{--                                </tr>--}}
{{--                            </thead>--}}
{{--                            <tbody>--}}
{{--                                @foreach($category->products as $product)--}}
{{--                                    <tr>--}}
{{--                                        <td>{{ $product->name }}</td>--}}
{{--                                        <td>{{ $product->price }}</td>--}}
{{--                                        <td>{{ $product->quantity }}</td>--}}
{{--                                    </tr>--}}
{{--                                @endforeach--}}
{{--                            </tbody>--}}
{{--                        </table>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--        @endif--}}

{{--        @if($category->parts->count())--}}
{{--            <div class="row justify-content-center">--}}
{{--                <div class="col-8">--}}
{{--                    <div class="card">--}}
{{--                        <div class="card-body">--}}
{{--                            <table class="table table-striped datatable">--}}
{{--                                <thead>--}}
{{--                                <tr>--}}
{{--                                    <th>Name</th>--}}
{{--                                    <th>Price</th>--}}
{{--                                    <th>Quantity</th>--}}
{{--                                </tr>--}}
{{--                                </thead>--}}
{{--                                <tbody>--}}
{{--                                @foreach($category->parts as $part)--}}
{{--                                    <tr>--}}
{{--                                        <td>{{ $part->name }}</td>--}}
{{--                                        <td>{{ $part->price }}</td>--}}
{{--                                        <td>{{ $part->quantity }}</td>--}}
{{--                                    </tr>--}}
{{--                                @endforeach--}}
{{--                                </tbody>--}}
{{--                            </table>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        @endif--}}
    </div>
    <!-- container -->
    <div id="deleteCategory" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content modal-filled bg-danger">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="dripicons-wrong h1"></i>
                        <h4 class="mt-2">Confirm Action!</h4>
                        <p class="mt-3">This will delete the category with all its sub categories.</p>
                        <form action="{{ route('general.categories.destroy', $category) }}" method="post">
                            @csrf
                            @method('delete')
                            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
                        </form>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
@endsection

@push('js')
    <!-- demo app -->
    <script src="{{ asset('js/pages/demo.products.js') }}"></script>
@endpush
