<?php

namespace Modules\General\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\General\Entities\PurchaseOrder;

class PurchaseOrderReceived
{
    use SerializesModels, Dispatchable;

    public PurchaseOrder $purchaseOrder;

    /**
     * Create a new event instance.
     * @param PurchaseOrder $purchaseOrder
     * @return void
     */
    public function __construct(PurchaseOrder $purchaseOrder)
    {
        $this->purchaseOrder = $purchaseOrder;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
