<?php

namespace Modules\Inventory\Http\Controllers\Levels;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Inventory\Actions\IncreaseLevelPriceAction;
use Modules\Inventory\Entities\Level;

class PriceController extends Controller
{
  /**
   * Show the form for creating a new resource.
   * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
   */
  public function create(Level $level)
  {
    $level->load('items');

    return view('inventory::levels.prices.create', compact('level'));
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @param IncreaseLevelPriceAction $levelPriceAction
   * @param Level $level
   * @return \Illuminate\Http\RedirectResponse
   */
  public function store(Request $request, IncreaseLevelPriceAction $levelPriceAction, Level $level)
  {
    $request->validate([
      'item_id' => 'required|array',
      'item_id.*' => 'required|exists:items,id',
      'percentage' => 'required|numeric|min:0'
    ]);

    $levelPriceAction->execute($level, $request->all());

    return redirect()
      ->route('inventory.levels.show', $level)
      ->with('success', 'Level special prices updated successfully');
  }
}
