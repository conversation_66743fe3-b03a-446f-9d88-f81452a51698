<?php

namespace Modules\Inventory\Http\Controllers\Api;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Services\ItemSearchService;
use Modules\Inventory\Services\PriceCalculator;
use Modules\Users\Entities\Customer;
use Yajra\DataTables\DataTables;

class ItemController extends Controller
{
  /**
   * @var PriceCalculator
   */
  protected $priceCalculator;

  public function __construct(PriceCalculator $priceCalculator)
  {
    $this->priceCalculator = $priceCalculator;
  }

  public function index(ItemSearchService $itemSearchService)
  {
    return DataTables::of($itemSearchService->searchQuery())->make();
  }

  public function show(Customer $customer)
  {
    $item = Item::query()->where('code', request('code'))->first();

    if (empty($item)) {
      return response()->json([
        'message' => 'Item not found'
      ], 404);
    }

    $attributes = $this->getItemAttributes($item);
    // Update price for the customer
    $attributes['list_price'] = $this->priceCalculator->itemPrice($customer, $item);

    return $attributes;
  }

  protected function getItemAttributes(Model $item): array
  {
    return Arr::only($item->toArray(), [
      'id',
      'cost',
      'code',
      'number',
      'description',
      'quantity',
      'quantity_frozen',
      'formatted_cost_date',
      'build_description',
      'weight',
      'commission_type',
    ]);
  }
}
