<?php

namespace Modules\Inventory\Http\Controllers\Api;

use App\Services\ImportInventoryService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Inventory\Services\ImportInventory;
use Modules\Inventory\Services\ImportInventoryVendor;

/**
 * Class ImportInventoryController
 * @package Modules\Inventory\Http\Controllers\Api
 * @codeCoverageIgnore
 */
class ImportInventoryController extends Controller
{
  protected $importService;

  public function __construct(ImportInventoryService $importService)
  {
    $this->importService = $importService;
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function store(Request $request)
  {
    try {
      $this->importService->importInventory($request->all());

      return response()
        ->json(
          [
            'message' => 'Parts imported successfully',
            'status' => true
          ],
          Response::HTTP_CREATED
        );
    } catch (\Exception $e) {
      \Log::debug($e->getTraceAsString());
      return response()
        ->json(
          ['error' => $e->getMessage()],
          Response::HTTP_INTERNAL_SERVER_ERROR
        );
    }
  }

  public function view($num)
  {
    return view('source.inventory' . $num);
  }
}
