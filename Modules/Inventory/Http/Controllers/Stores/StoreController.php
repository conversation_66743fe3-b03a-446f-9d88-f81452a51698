<?php

namespace Modules\Inventory\Http\Controllers\Stores;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Modules\Inventory\Entities\Store;
use Modules\Inventory\Entities\Warehouse;

/**
 * Class StoreController
 * @package Modules\Inventory\Http\Controllers\Stores
 * @codeCoverageIgnore
 */
class StoreController extends Controller
{
    use AuthorizesRequests;

    public function __construct()
    {
        $this->authorizeResource(Store::class, 'store');
    }

    /**
     * Display a listing of the resource.
     * @return Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function index()
    {
        $this->authorize('view store');

        $stores = Cache::remember('stores', now()->secondsUntilEndOfDay(), function () {
            return Store::with('sections')
                ->orderBy('name')
                ->get();
        });

        return view('inventory::stores.index', compact('stores'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        $warehouseList = Warehouse::orderBy('name')->pluck('name', 'id');

        return view('inventory::stores.create', compact('warehouseList'));
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|array|min:1',
            'name.*' => 'string|max:255',
            'warehouse_id' => 'required|array|min:1',
            'warehouse_id.*' => 'exists:warehouses,id'
        ]);

        collect($request->name)->map(function ($name, $key) {
            return [
                'name' => $name,
                'warehouse_id' => request('warehouse_id')[$key]
            ];
        })->each(function ($record) {
            Store::create($record);
        });

        return redirect()->route('inventory.stores.index');
    }

    /**
     * Show the specified resource.
     * @param Store $store
     * @return Response
     */
    public function show(Store $store)
    {
        return view('inventory::stores.show', compact('store'));
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Response
     */
    public function edit(Store $store)
    {
        $warehouseList = Warehouse::orderBy('name')->pluck('name', 'id');

        return view('inventory::stores.edit', compact('store', 'warehouseList'));
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, Store $store)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'warehouse_id' => 'required|exists:warehouses,id'
        ]);

        $store->update($request->all());

        return redirect()
                ->route('inventory.stores.show', $store)
                ->with('success', 'Store updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy(Store $store)
    {
        try {
            $store->delete();

            return back()->with('success', 'Store deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error deleting store: ' . $e->getMessage());
        }
    }
}
