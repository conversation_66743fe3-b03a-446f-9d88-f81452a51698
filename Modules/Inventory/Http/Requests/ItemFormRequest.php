<?php

namespace Modules\Inventory\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ItemFormRequest extends FormRequest
{
  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules(): array
  {
    $rules = [
      'name' => 'required|string|max:255',
      'category_id' => 'required|exists:categories,id',
      'quantity' => 'required|numeric|min:0',
      'description' => 'nullable|string|max:5000',
      'quantity_frozen' => 'nullable|boolean',
      'featured_image' => 'nullable|mimes:jpeg,jpg,png',
      // Size and Measurements
      'case_quantity' => 'nullable|min:0',
      'stocking_quantity' => 'nullable|min:0',
      'width' => 'nullable|string|min:0',
      'height' => 'nullable|required_with:width|string|min:0',
      'weight' => 'nullable|string|min:0',
      'depth' => 'nullable|string|min:0',
      'dimension' => 'nullable|string|max:25',
      // Labelling
      'label' => 'nullable|string|max:10',
      'label_size' => 'nullable|required_with:label|string|max:10',
      'print_labels' => 'nullable|required_with:label|string|max:255',
      // Selling Particulars
      'salable' => 'nullable|boolean',
      'taxable' => 'nullable|boolean',
      'sell_name' => 'nullable|string|max:255',
      'selling_instructions' => 'nullable|string|max:5000',
      // Other Particulars
      'ordering_group' => 'nullable|string|max:50',
      'purchase_instructions' => 'nullable|string|max:5000',
      'receiving_instructions' => 'nullable|string|max:5000'
    ];

    if ($this->user()->can(['update item pricing'])) {
      $rules = array_merge($rules, [
        'misc_cost' => 'required|numeric',
        'price' => 'required|numeric',
        'multiplier' => 'required|numeric|min:0',
        'freight_cost' => 'required|numeric',
      ]);
    }

    return $rules;
  }

  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize(): bool
  {
    return true;
  }

  public function prepareForValidation()
  {
    $dataToMerge = [];

    if ($this->user()->can('update item pricing')) {
      $dataToMerge['multiplier'] = $this->multiplier;
    }

    $dataToMerge['taxable'] = $this->taxable ?? '0';
    $dataToMerge['salable'] = $this->salable ?? '0';

    $this->merge($dataToMerge);
  }
}
