<?php

namespace Modules\Inventory\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\User;

class ProductPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any product.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->can('view product');
    }

    /**
     * Determine whether the user can view the product.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function view(User $user)
    {
        return $user->can('view product');
    }

    /**
     * Determine whether the user can create products.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->can('create product');
    }

    /**
     * Determine whether the user can update the product.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function update(User $user)
    {
        return $user->can('update product');
    }

    /**
     * Determine whether the user can delete the product.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function delete(User $user)
    {
        return $user->can('delete product');
    }

    /**
     * Determine whether the user can restore the product.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function restore(User $user)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the product.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function forceDelete(User $user)
    {
        return $user->isAdmin();
    }
}
