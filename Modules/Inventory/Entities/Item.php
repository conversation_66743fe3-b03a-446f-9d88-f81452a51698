<?php

namespace Modules\Inventory\Entities;

use App\Models\ActivityLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Modules\General\Entities\Category;
use Modules\General\Entities\PurchaseOrder;
use Modules\Inventory\Events\ItemSaved;
use Modules\Inventory\Traits\PresentsMedia;
use Modules\Orders\Entities\ItemSale;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\CustomerItem;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Item extends Model implements HasMedia, Auditable
{
  use InteractsWithMedia,
    PresentsMedia,
    \OwenIt\Auditing\Auditable,
    SoftDeletes;

  const WSN_CODE = 'WSN';

  protected $appends = [
    'formatted_cost_date',
    'markup',
    'vendor_part_number',
  ];

  public static $bomFields = [
    'id',
    'quantity',
    'code',
    'description',
    'price',
    'freight_cost',
    'cost',
    'weight',
  ];
  /**
   * The attributes that are not mass assignable.
   *
   * @var array
   */
  protected $guarded = [];

  /**
   * The attributes that should be hidden for arrays.
   *
   * @var array
   */
  protected $hidden = [
    //
  ];

  protected $dates = [
    'cost_date'
  ];

  /**
   * The attributes that should be cast to native types.
   *
   * @var array
   */
  protected $casts = [
    'options' => 'array',
    'salable' => 'boolean',
    'taxable' => 'boolean',
    'quantity_frozen' => 'boolean',
  ];

  protected $dispatchesEvents = [
    'saved' => ItemSaved::class
  ];

  public static function booted()
  {
    self::creating(function (Item $item) {
      $item->cost_date = now()->toDateString();
      $item->number = Item::max('number') + 1;
    });
  }

  /**
   * Collections for this model
   */
  public function registerMediaCollections(): void
  {
    $this->addMediaCollection('default')
      ->useFallbackUrl('/images/no-image.jpeg')
      ->singleFile();

    $this->addMediaCollection('attachments');
  }

  /**
   * @param Media|null $media
   * @throws \Spatie\Image\Exceptions\InvalidManipulation
   */
  public function registerMediaConversions(Media $media = null): void
  {
    $this->addMediaConversion('featured')
      ->fit(Manipulations::FIT_CONTAIN, 800, 600)
      ->performOnCollections('default')
      ->nonQueued();

    $this->addMediaConversion('thumb')
      ->width(150)
      ->height(150)
      ->sharpen(10)
      ->performOnCollections('default')
      ->nonQueued();
  }

  /**
   * Get the Provider for the Item.
   * @DEPRECATED: Use vendors instead
   */
  public function providers()
  {
    return $this->belongsToMany(Provider::class)
      ->withPivot(['cost', 'name']);
  }

  /**
   * Get the vendors of the item
   *
   * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
   */
  public function vendors()
  {
    return $this->belongsToMany(Provider::class)
      ->withPivot(['cost', 'name']);
  }

  public function vendor()
  {
    return $this->vendors()->first();
  }

  public function inventoryImport()
  {
    return $this->hasOne(InventoryImport::class);
  }

  /**
   * Get all order activity logs
   *
   * @return \Illuminate\Database\Eloquent\Relations\MorphMany
   */
  public function logs()
  {
    return $this->morphMany(ActivityLog::class, 'loggable');
  }

  public function sections()
  {
    return $this->belongsToMany(Section::class)
      ->withPivot(['quantity'])
      ->withTimestamps();
  }

  public function specialPriceCustomers()
  {
    return $this->belongsToMany(Customer::class, 'customer_item', 'item_id', 'customer_id')
      ->withPivot(['fixed_price', 'discount_price'])
      ->withTimestamps();
  }

  public function sales(): \Illuminate\Database\Eloquent\Relations\HasMany
  {
    return $this->hasMany(ItemSale::class);
  }

  public function getLocationAttribute()
  {
    return optional($this->sections->first())->name;
  }

  public function orderLines(): \Illuminate\Database\Eloquent\Relations\HasMany
  {
    return $this->hasMany(OrderLine::class);
  }

  /**
   * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
   */
  public function purchaseOrders()
  {
    return $this->belongsToMany(PurchaseOrder::class)->withPivot([
      'quantity',
      'description',
      'manufacturer_part_number'
    ]);
  }

  /**
   * Get all the notes for this part
   *
   * @return \Illuminate\Database\Eloquent\Relations\MorphMany
   */
  public function notes()
  {
    return $this->morphMany(Note::class, 'notable');
  }


  /**
   * Get the Category for the Item.
   */
  public function category()
  {
    return $this->belongsTo(Category::class)->withDefault([
      'name' => 'Uncategorized',
      'id' => Category::UNCATEGORIZED_ID
    ]);
  }

  /**
   * Get the bill of materials for this item
   */
  public function bom()
  {
    return $this->belongsToMany(
      Item::class,
      'item_part',
      'part_id',
      'item_id'
    )
      ->withPivot(['quantity'])
      ->withTimestamps();
  }

  /**
   * Get the products to which this item as listed in the BOM
   */
  public function bomProducts()
  {
    return $this->belongsToMany(
      Item::class,
      'item_part',
      'item_id',
      'part_id'
    )
      ->withPivot(['quantity'])
      ->withTimestamps();
  }

  public function customers()
  {
    return $this->belongsToMany(Customer::class, 'customer_item')
      ->using(CustomerItem::class)
      ->withPivot(['fixed_price', 'discount_price'])
      ->withTimestamps();
  }

  public function levels()
  {
    return $this->belongsToMany(Level::class)
      ->withPivot(['fixed_price', 'discount'])
      ->withTimestamps();
  }

  public function inStock(): bool
  {
    return $this->quantity > 0;
  }

  public function hasMeasurement(): bool
  {
    return !empty($this->width) ||
      !empty($this->weight) ||
      !empty($this->dimension) ||
      !empty($this->depth);
  }

  public function getOption($key, $default = null)
  {
    return Arr::get($this->options, $key, $default);
  }

  public function requiresBuilding(): bool
  {
    return ! empty($this->build_description);
  }

  public function getBuildNotesAttribute()
  {
    return $this->notes->firstWhere('type', 'default');
  }

  public function getSellNameAttribute()
  {
    return empty($this->sell_name) ? $this->name : $this->sell_name;
  }

  public function getLocationNamesAttribute()
  {
    return implode(',', $this->sections->pluck('name')->all());
  }

  public function onBackOrder()
  {
    $qtyAvailable = $this->pivot->quantity_available;

    return !is_null($qtyAvailable) && $this->pivot->quantity >= $qtyAvailable;
  }

  /**
   * Get the back order quantity
   *
   * @return int
   */
  public function getBackOrderQuantityAttribute()
  {
    if ($this->pivot->quantity_available === 0) {
      return $this->pivot->quantity;
    }

    return $this->pivot->quantity - $this->pivot->quantity_available;
  }

  public function currentVendor()
  {
    return $this->vendors->first();
  }

  public function getVendorPartNumberAttribute()
  {
    $vendor = $this->vendors->first();

    if ($vendor) {
      return $vendor->pivot->name;
    }

    return null;
  }

  public function getVendorPartIdAttribute()
  {
    $vendor = $this->vendors->first();

    if ($vendor) {
      return $vendor->id;
    }

    return null;
  }

  public function getVendorNameAttribute()
  {
    $vendor = $this->vendors->first();

    if ($vendor) {
      return $vendor->name;
    }

    return null;
  }

  public function getAvailableQuantityAttribute()
  {
    return $this->pivot->quantity_available ?? $this->pivot->quantity;
  }

  public function getTotalAttribute()
  {
    return $this->available_quantity * $this->pivot->price;
  }

  public function getMarkupAttribute()
  {
    if (empty($this->price)) {
      return 0.000;
    }

    return round($this->price / $this->cost, 3);
  }

  public function getListPriceAttribute()
  {
    return round($this->multiplier * $this->cost, 2);
  }

  public function getManufacturerPartNumberAttribute()
  {
    return $this->getOption('manufacturer_part_number');
  }

  public function getBomAmountAttribute()
  {
    return $this->pivot->quantity * $this->cost;
  }

  public function getBomDateAttribute()
  {
    return optional($this->pivot->created_at)->format('m/d/y');
  }

  public function getBomWeightAttribute()
  {
    return $this->pivot->quantity * $this->weight;
  }

  public function getTotalBomCostAttribute()
  {
    return $this->bom->reduce(function ($carry, $item) {
      return $carry + $item->bom_amount;
    }, 0);
  }

  public function getTotalBomWeightAttribute()
  {
    return $this->bom->reduce(function ($carry, $item) {
      return $carry + $item->bom_weight;
    }, 0);
  }

  public function getFormattedCostDateAttribute()
  {
    if (! empty($this->cost_date)) {
      return $this->cost_date->format('m/d/y');
    }

    return now()->format('m/d/y');
  }
}
