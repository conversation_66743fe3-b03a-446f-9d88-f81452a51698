<?php

namespace Modules\Inventory\Entities;

use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\General\Entities\Provider as Vendor;
use Modules\Orders\Entities\Commission;
use OwenIt\Auditing\Contracts\Auditable;

class Provider extends Vendor
{
    use SoftDeletes;

    protected static $logFillable = true;

    protected static $logOnlyDirty = true;

    protected static $submitEmptyLogs = false;

    public function commissions()
    {
        return $this->morphMany(Commission::class, 'commissionable');
    }

    public function getDescriptionForEvent(string $eventName): string
    {
        $eventName = ucfirst($eventName);

        return "{$eventName} the service provider named {$this->name}";
    }
    /**
     * Get the Items provided by the Vendor
     */
    public function items()
    {
        return $this->belongsToMany(Item::class)->withPivot(['cost', 'name']);
    }

    public function scopeVendor($query)
    {
        return $query->whereType('vendor')->orderBy('name');
    }
}
