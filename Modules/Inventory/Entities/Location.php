<?php

namespace Modules\Inventory\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use OwenIt\Auditing\Contracts\Auditable;

class Location extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = ['locatable_id', 'locatable_type', 'section_id', 'quantity'];

    protected $table = 'locatables';

    /**
     * Get the owning locatable model
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function locatable()
    {
        return $this->morphTo();
    }

    public function section()
    {
        return $this->belongsTo(Section::class);
    }

    public function getTypeAttribute()
    {
        $arr = explode('\\', $this->locatable_type);
        return array_pop($arr);
    }

    public function getTypePluralAttribute()
    {
        return Str::plural(strtolower($this->type));
    }
}
