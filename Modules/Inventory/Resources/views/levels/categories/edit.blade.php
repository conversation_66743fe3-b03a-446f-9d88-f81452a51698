@extends('layouts.master')

@section('title', 'Edit ' . $category->name . ' details')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('orderModule.levels.index') }}">Levels</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('orderModule.levels.show', $level) }}">{{ $level->name }}</a></li>
                            <li class="breadcrumb-item active">Edit {{ $category->name }}</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit {{ $category->name }} <small>| {{ $level->name }}</small></h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8 col-sm-10">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Fill the form below to update {{ $category->name }} for {{ $level->name }}</p>
                        <form action="{{ route('orderModule.levels.categories.update', [$level, $category]) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')
                            <div class="form-row mb-3">
                                <div class="col-12">
                                    <label for="category-name">Name</label>
                                    <input type="text"
                                           name="name"
                                           id="category-name"
                                           class="form-control"
                                           value="{{ $category->name }}" disabled>
                                </div>
                            </div>
                            <div class="form-row mb-3">
                                <div class="col-md-6">
                                    <label for="category-fixed-price">Fixed Price</label>
                                    <input type="text"
                                           name="fixed_price"
                                           id="category-fixed-price"
                                           class="form-control"
                                           value="{{ old('fixed_price', $priceLevelCategory->fixed_price) }}">
                                </div>
                                <div class="col-md-6">
                                    <label for="category-discount">Discount</label>
                                    <input type="text"
                                           name="discount"
                                           id="category-discount"
                                           class="form-control"
                                           value="{{ $priceLevelCategory->discount }}">
                                </div>
                            </div>

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('orderModule.levels.show', $level) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

