@extends('layouts.master')

@section('title', 'Add Level')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('inventory.levels.index') }}">Levels</a></li>
                            <li class="breadcrumb-item active">Add Level</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Add Level</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-8">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Fill the form below to add a level</p>
                        <form action="{{ route('inventory.levels.store') }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf

                            <div class="form-group mb-2">
                                <label for="level-name">Name</label>
                                <input type="text"
                                       name="name"
                                       id="level-name"
                                       class="form-control level-name-input"
                                       value="{{ old('name') }}">
                            </div>
                            <div class="form-group mb-2">
                                <label class="d-block mb-1" for="level-type">Level</label>
                                <label class="mr-2 d-inline-block">
                                    <input class="level-type-input" type="radio" name="type" value="system" {{ old('type') === 'system' ? 'selected' : '' }}> System
                                </label>
                                <label>
                                    <input class="level-type-input" type="radio" name="type" value="custom" {{ old('type') === 'custom' ? 'selected' : '' }}> Custom
                                </label>
                            </div>

                            <div class="form-group text-right">
                                <button type="button" class="btn btn-light my-2 mr-1" data-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection
