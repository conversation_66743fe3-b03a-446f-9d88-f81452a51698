@extends('layouts.master')

@section('title', 'Edit Store')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('inventory.stores.index') }}">Stores</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('inventory.stores.show', $store) }}">{{ $store->name }}</a></li>
                            <li class="breadcrumb-item active">Edit Store</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Store</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-sm-12 col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Fill the form below to edit this store</p>
                        <form action="{{ route('inventory.stores.update', $store) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')
                            <div class="form-group mb-2">
                                <label for="store-name">Name</label>
                                <input type="text"
                                       name="name"
                                       id="store-name"
                                       class="form-control @error('name') is-invalid @enderror"
                                       value="{{ old('name', $store->name) }}">
                            </div>

                            <div class="form-group mb-3">
                                <label for="warehouse-id">Warehouse</label>
                                <select class="form-control" name="warehouse_id" id="warehouse-id">
                                    @foreach($warehouseList as $warehouseId => $warehouseName)
                                        <option value="{{ $warehouseId }}"
                                            {{ old('warehouse_id', $warehouseId) === $store->warehouse_id ? 'selected' : '' }}>
                                            {{ $warehouseName }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('inventory.stores.show', ['type' => $store->type, 'store' => $store]) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

