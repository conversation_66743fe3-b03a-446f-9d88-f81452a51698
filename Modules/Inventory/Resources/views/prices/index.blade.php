@extends('layouts.master')

@section('title', 'Levels')

@prepend('styles')
    <!-- third party css -->
    <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
    <!-- third party css end -->
@endprepend

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">Special Price Items</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Special Price Items</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-8">

                            </div>
                            <div class="col-md-4 text-right">
                                <a class="btn btn-danger"
                                   href="{{ route('inventory.prices.create') }}">
                                    <i class="mdi mdi-plus"></i>
                                    Add Special Priced Item
                                </a>
                            </div>
                        </div>


                        <div class="table-responsive">
                            <table class="table table-centered w-100 dt-responsive nowrap datatable">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Level</th>
                                        <th>Category</th>
                                        <th class="all">Item</th>
                                        <th>Type</th>
                                        <th>Fixed Price</th>
                                        <th>Discount</th>
                                        <th style="min-width: 85px;">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                @foreach ($prices as $price)
                                    <tr>
                                        <td>{{ $price->level->name }}</td>
                                        <td>{{ $price->category->name }}</td>
                                        <td><a href="{{ $price->url }}">{{ $price->priceable->name }}</a></td>
                                        <td>{{ $price->type }}</td>
                                        <td>{{ $price->fixed_price }}</td>
                                        <td>{{ $price->discount }}</td>
                                        <td class="table-action text-right">
                                            <a href="{{ route('inventory.prices.edit', $price) }}"
                                               class="action-icon">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>

                                            <a href="#"
                                               data-target="#deleteItem"
                                               data-toggle="modal"
                                               class="action-icon delete-item-btn"
                                               data-url="{{ route('inventory.prices.destroy', $price) }}">
                                                <i class="mdi mdi-delete"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col -->
        </div>
        <!-- end row -->
    </div>

    <div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content modal-filled bg-danger">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="dripicons-wrong h1"></i>
                        <h4 class="mt-2">Confirm Action!</h4>
                        <p class="mt-3">This will delete this price item from the system.</p>
                        <form action="#" method="post" id="delete-item-form">
                            @csrf
                            @method('delete')
                            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
                        </form>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- container -->
@endsection

@push('js')
    <!-- third party js -->
    <script src="{{ asset('js/vendor/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/vendor/dataTables.bootstrap4.js') }}"></script>
    <script src="{{ asset('js/vendor/dataTables.responsive.min.js') }}"></script>
    <script src="{{ asset('js/vendor/responsive.bootstrap4.min.js') }}"></script>
    <!-- third party js ends -->

    <!-- demo app -->
    <script src="{{ asset('js/pages/demo.products.js') }}"></script>
@endpush
