<fieldset class="mb-4">
    <legend class="text-muted">Store Locations</legend>
    <small class="mb-2 d-block">You can add multiple locations by using the <code>+ Add</code> button.</small>
    <div class="form-row duplicate">
        <div class="col-md-3 form-group">
            <label>Store</label>
            <select class="form-control select2 @error('store_id') is-invalid @enderror"
                    data-toggle="select2"
                    name="store_id[]">
                <option value="" selected>Select Store</option>
                @foreach($storeList as $key => $store)
                    <option value="{{ $key }}">{{ $store }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Location</label>
                <input type="text" class="form-control" name="store_name[]">
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label>Quantity</label>
                <input type="number" class="form-control" name="store_quantity[]" value="0">
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label for="store-level">Level</label>
                <select class="form-control @error('store_level') is-invalid @enderror"
                        name="store_level[]"
                        id="store-level">
                    <option value="" selected>Select Level</option>
                    <option value="downstairs" {{ old('store_level') == 'downstairs' ? 'selected' : '' }}>Downstairs</option>
                    <option value="upstairs" {{ old('store_level') == 'upstairs' ? 'selected' : '' }}>Upstairs</option>
                </select>
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label class="d-block">&nbsp;</label>
                <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none"><i class="mdi mdi-close"></i> Remove </button>
                <button type="button" class="btn btn-link duplicate-btn"><i class="mdi mdi-plus"></i> Add</button>
            </div>
        </div>
    </div>
</fieldset>

