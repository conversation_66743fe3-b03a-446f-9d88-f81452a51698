<fieldset class="mb-4">
    <legend class="text-muted">Add Items to {{ $section->name }}</legend>
    <small class="mb-2 d-block">You can add multiple locations by using the <code>+ Add</code> button.</small>
    <div class="form-row duplicate">
        <div class="col-md-6 form-group">
            <label>Item</label>
            <select class="form-control select2 @error('item_id') is-invalid @enderror"
                    data-toggle="select2"
                    name="item_id[]">
                <option value="" selected>Select Item</option>
                @foreach($itemList as $itemId => $itemName)
                    <option value="{{ $itemId }}">{{ $itemName }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>Quantity</label>
                <input type="number" class="form-control" name="quantity[]" value="0">
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label class="d-block">&nbsp;</label>
                <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none"><i class="mdi mdi-close"></i> Remove </button>
                <button type="button" class="btn btn-link duplicate-btn"><i class="mdi mdi-plus"></i> Add</button>
            </div>
        </div>
    </div>
</fieldset>

