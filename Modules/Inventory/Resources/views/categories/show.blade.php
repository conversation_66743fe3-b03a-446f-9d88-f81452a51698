@extends('layouts.master')

@section('title', $category->name)

@push('styles')
@livewireStyles
@endpush

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('inventory.categories.index', $category->type) }}">Categories</a></li>
                            <li class="breadcrumb-item active">Category Details</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Category Details</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-body">
                        @canany(['update category', 'delete category'])
                        <div class="card-widgets">
                            <div class="btn-group">
                                <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="mdi mdi mdi-dots-vertical"></i> Actions
                                </button>
                                <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(285px, 37px, 0px);">
                                    @can('update category')
                                    <a class="dropdown-item" href="{{ route('inventory.categories.edit', ['type' => $category->type, 'category' => $category]) }}">Edit Category</a>
                                    @endcan
                                    @can('delete category')
                                        <div class="dropdown-divider"></div>
                                        @if($category->canBeDeleted($category->type))
                                            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#deleteCategory">Delete Category</a>
                                        @else
                                            <a class="dropdown-item" href="#" onclick="alert('This category cannot be deleted with {{ $types = \Str::plural($category->type) }} attached to it. Reassign those {{ $types }} to other categories and try again.')">Delete Category</a>
                                        @endif
                                    @endcan
                                </div>
                            </div>
                        </div>
                        @endcanany
                        <div class="row">
                            <div class="col-12">
                                <!-- Category title -->
                                <h3 class="mt-0">{{ $category->name }} <span class="text-muted">- {{ ucfirst($category->type) }}</span> </h3>
                                <p class="mb-4">Added Date: {{ $category->created_at->toDateString() }}</p>

                                <!-- Category description -->
                                <div class="mb-4">
                                    <h6 class="font-14 text-muted">Category Parent:</h6>
                                    <p>
                                        @if($parentCategory)
                                            <a href="{{ route('inventory.categories.show', ['type' => $parentCategory->type, 'category' => $parentCategory]) }}">
                                                <strong>{{ $parentCategory->name }}</strong>
                                            </a>
                                        @else
                                        -
                                        @endif
                                    </p>
                                </div>

                                <!-- Category description -->
                                <div class="mb-4">
                                    <h6 class="font-14">Description:</h6>
                                    <p>{!! $category->description !!}</p>
                                </div>

                                <h6 class="font-14">Sub Categories ({{ $category->children->count()  }})</h6>
                                <div class="d-flex mb-4">
                                    @forelse($category->children as $subCategory)
                                        <a class="d-inline-block mr-2 badge badge-secondary-lighten px-3 py-1 font-13" href="{{ route('inventory.categories.show', ['type' => $subCategory->type, 'category' => $subCategory]) }}">{{ $subCategory->name }}</a>
                                    @empty
                                        <p>No sub-categories</p>
                                    @endforelse
                                </div>

                            </div> <!-- end col -->
                        </div> <!-- end row-->
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->
        @can('view item')
            @livewire('inventory.category-items', ['category' => $category])
        @endcan
    </div>
    <!-- container -->
    <div id="deleteCategory" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content modal-filled bg-danger">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="dripicons-wrong h1"></i>
                        <h4 class="mt-2">Confirm Action!</h4>
                        <p class="mt-3">This will delete the category with all its sub categories.</p>
                        <form action="{{ route('inventory.categories.destroy', ['type' => $category->type, 'category' => $category]) }}" method="post">
                            @csrf
                            @method('delete')
                            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
                        </form>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
@endsection

@push('js')
  @livewireScripts
@endpush
