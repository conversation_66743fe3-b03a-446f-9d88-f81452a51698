<div class="card border-top border-info">
  <div class="card-body">
    <div class="card-widgets">
      <a data-toggle="collapse" href="#cardCollpase1" role="button" aria-expanded="false"
         aria-controls="cardCollpase1" class="collapsed"><i class="mdi mdi-minus"></i></a>
    </div>
    <h5 class="card-title text-info collapsed" data-toggle="collapse" href="#cardCollpase1" role="button"
        aria-expanded="false" aria-controls="cardCollpase1">More Item Details</h5>

    <div id="cardCollpase1" class="collapse pt-3">
      <div class="row">
        <div class="col-md-6">
          <p>
            <strong>Case Quantity:</strong> <span class="badge p-1 font-13 badge-info-lighten">{{ $item->case_quantity }}</span>
          </p>
          <p>
            <strong>Stocking Quantity:</strong> <span class="badge p-1 font-13 badge-info-lighten">{{ $item->stocking_quantity }}</span>
          </p>
          <p>
            <strong>Quantity Frozen:</strong> <span class="badge p-1 font-13 badge-info-lighten">{{ $item->quantity_frozen ? 'Yes' : 'No' }}</span>
          </p>
          <h5 class="text-muted font-weight-normal">Size and Measurements</h5>
          <div class="d-flex mb-3">
            <span class="mr-2"><strong>Width:</strong>
              <span class="badge p-1 font-13 badge-info-lighten">
                {{ number_format($item->width, 2) }}<span class="font-italic">in</span>
              </span>
            </span>
            <span class="mr-2"><strong>Height:</strong>
              <span class="badge badge-info-lighten p-1 font-13">
                {{ number_format($item->height, 2) }}<span class="font-italic">in</span>
              </span>
            </span>
            <span class="mr-2"><strong>Depth:</strong>
              <span class="badge badge-info-lighten p-1 font-13">
                {{ number_format($item->depth, 2) }}<span class="font-italic">in</span>
              </span>
            </span>
            <span class="mr-2"><strong>Weight:</strong>
              <span class="badge badge-info-lighten p-1 font-13">
                {{ number_format($item->weight, 3) }}<span class="font-italic">lb</span>
              </span>
            </span>
          </div>

          <h5 class="text-muted font-weight-normal ">Labelling</h5>
          <div class="d-flex mb-3">
            <span class="mr-3">
              <strong>Needs Label:</strong> <span class="badge badge-info-lighten p-1 font-13">{{ $item->label ?? '~' }}</span>
            </span>
            <span class="mr-3">
              <strong>Label Size:</strong> <span class="badge badge-info-lighten p-1 font-13">{{ $item->label_size ?? '~' }}</span>
            </span>
            <span>
              <strong>Print Labels:</strong> <span class="badge badge-info-lighten p-1 font-13">{{ $item->print_labels ?? '~' }}</span>
            </span>
          </div>

          <h5 class="text-muted font-weight-normal">Selling</h5>
          <p><strong>Sell Name:</strong> {{ $item->sell_name ?? $item->name }}</p>
          <div class="d-flex mb-2">
            <span class="mr-2">
              <strong>Salable:</strong> <span class="badge badge-info-lighten p-1 font-13">{{ $item->salable ? 'Yes' : 'No' }}</span>
            </span>
            <span>
              <strong>Taxable:</strong> <span class="badge badge-info-lighten p-1 font-13">{{ $item->taxable ? 'Yes' : 'No' }}</span>
            </span>
          </div>
          <p class="mb-3"><strong>Selling Instructions:</strong> {{ $item->selling_instructions }}</p>
          <h5 class="text-muted font-weight-light">Other Details</h5>
          <p><strong>Ordering Group:</strong> {{ $item->ordering_group }}</p>
          <p><strong>Receiving Instructions:</strong> {{ $item->receiving_instructions }}</p>
          <p><strong>Purchase Instructions:</strong> {{ $item->purchase_instructions }}</p>
        </div>
      </div>
    </div>
  </div>
</div>
