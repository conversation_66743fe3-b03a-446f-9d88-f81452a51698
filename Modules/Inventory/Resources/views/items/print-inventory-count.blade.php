<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ config('app.name') }}</title>
  @include('shared.print.default-print-styles')
  <style>
    body, .report-container {
      font-family: 'Helvetica', serif;
      font-size: 17px;
      line-height: 1.2;
    }
    .print-page-title {
      font-size: 25px;
    }
  </style>
</head>
<body>

<table class="table">
  <thead class="thead-light">
    <tr>
      <td colspan="4" class="border-0" style="border-bottom: 3px solid">
        <h1 style="margin-bottom: 0">Inventory Price List</h1>
      </td>
      <td class="border-0 text-right" style="border-bottom: 3px solid">
        {{ now()->format('F d, Y') }}
      </td>
    </tr>
    <tr class="">
      <th class="text-right border-0" style="width: 60px;">Qty</th>
      <th class="p-1 border-0 text-left">Code</th>
      <th class="p-1 border-0 text-left">Our Part#</th>
      <th class="p-1 border-0 text-left">Vendor Part#</th>
      <th class="p-1 border-0 text-left">Description</th>
    </tr>
  </thead>
  <tbody>
  @php $currentCategory = optional($inventoryItemGroups->first())->category_name; @endphp
  @foreach($inventoryItemGroups as $inventoryImport)

    @if($loop->first || $currentCategory !== $inventoryImport->category_name)
      @php $currentCategory = $inventoryImport->category_name @endphp
      <tr>
        <td colspan="4" class="border-0" style="border-bottom: 2px dotted"></td>
        <td class="border-0 font-weight-bold" style="border-bottom: 2px dotted">{{ $currentCategory }}</td>
      </tr>
    @endif
    <tr>
      <td class="border-0" style="border-bottom: 2px solid;"></td>
      <td class="border-0" style="border-bottom: 2px dotted;">{{ $inventoryImport->item_number }}</td>
      <td class="border-0" style="border-bottom: 2px dotted;">{{ $inventoryImport->item_code }}</td>
      <td class="border-0" style="border-bottom: 2px dotted;">{{ $inventoryImport->item_manufacturer_part_number }}</td>
      <td class="border-0" style="border-bottom: 2px dotted;">{{ $inventoryImport->item_description }}</td>
    </tr>
  @endforeach
  </tbody>
</table>
</body>
</html>
