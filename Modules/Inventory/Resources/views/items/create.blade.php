@extends('layouts.master')

@section('title', 'Add Item')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('inventory.items.index') }}">Items</a></li>
              <li class="breadcrumb-item active">Add Item</li>
            </ol>
          </div>
          <h4 class="page-title">Add Item</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-10">
        <div class="card">
          <div class="card-body px-5 py-4">
            <p>Fill the form below to add an item to inventory</p>
            <form action="{{ route('inventory.items.store') }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf

              @include('inventory::items.partials.form', ['item' => new \Modules\Inventory\Entities\Item])

              <div class="form-group d-flex justify-content-end">
                <a class="btn btn-light mr-2" href="{{ route('inventory.items.index') }}">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Changes</button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
@endsection

@include('inventory::shared.forms.duplicate')
