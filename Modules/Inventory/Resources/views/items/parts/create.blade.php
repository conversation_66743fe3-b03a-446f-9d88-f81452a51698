@extends('layouts.master')

@section('title', 'Add to BOM List')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('inventory.items.show', $item) }}">{{ $item->code }}</a>
              </li>
              <li class="breadcrumb-item active">Add to BOM List</li>
            </ol>
          </div>
          <h4 class="page-title">Add to BOM List</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-sm-12">
        <div class="card">
          <div class="card-body py-4">
            <form action="{{ route('inventory.items.parts.store', $item) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf

              <p>Fill the form below to add items to the BOM list. You can assign multiple records by clicking the <code>+ Add</code> button.</p>

              <fieldset>
                <div class="form-row duplicate">
                  <div class="col-md-2">
                    <label>Qty</label>
                    <input
                      class="form-control quantity-input"
                      type="number"
                      name="quantity[]"
                      step="0.0001"
                      value="1.0"
                      min="0">
                  </div>
                  <div class="col-md-4">
                    <label>Part</label>
                    <select
                      name="item_id[]"
                      class="form-control select2 bom-list-selector"
                      data-toggle="select2">
                      <option value="">Select Item</option>
                      @foreach($parts as $itemId => $item)
                        <option value="{{ $item->id }}"
                                data-price="{{ number_format($item->price, 2, '.', '') }}"
                                data-cost="{{ number_format($item->cost, 2, '.', '') }}">
                          {{ $item->code . ' - ' . $item->description }}
                        </option>
                      @endforeach
                    </select>
                  </div>
                  <div class="col-md-2">
                    <label>Price</label>
                    <input class="form-control bom-list-item-price"
                           type="number"
                           step="0.01"
                           value="0.00"
                           min="0"
                           disabled>
                  </div>
                  <div class="col-md-2">
                    <label>Our Cost</label>
                    <input class="form-control bom-list-item-cost"
                           type="number"
                           step="0.01"
                           value="0.00"
                           min="0"
                           disabled>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label class="d-block">&nbsp;</label>
                      <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none">
                        <i class="mdi mdi-close"></i> Remove
                      </button>
                      <button type="button" class="btn btn-link duplicate-btn" data-id="duplicate0"><i
                          class="mdi mdi-plus"></i> Add
                      </button>
                    </div>
                  </div>
                </div>
              </fieldset>

              <div class="form-group mt-3 d-flex justify-content-end">
                <a class="btn btn-light mr-2" href="{{ route('inventory.items.show', $item->id) }}"><i
                    class="mdi mdi-cancel mr-1"></i> Cancel</a>
                <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
@endsection
@include('inventory::shared.forms.duplicate')
@push('js')
  <script type="text/javascript">
    $(document).ready(function () {
      $('body').on('change', '.bom-list-selector', function () {
        let selectedOption = $('option:selected', this);
        let selectParentWrapper = $(this).parent().parent();

        selectParentWrapper.find('.bom-list-item-price').val(selectedOption.attr('data-price'));
        selectParentWrapper.find('.bom-list-item-cost').val(selectedOption.attr('data-cost'));
      })
    });
  </script>
@endpush
