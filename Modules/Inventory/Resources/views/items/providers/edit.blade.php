@extends('layouts.master')

@section('title', 'Edit Vendor')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.vendors.index') }}">Categories</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('general.vendors.show', $provider) }}">{{ $provider->name }}</a></li>
                            <li class="breadcrumb-item active">Edit Vendor</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Vendor</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <form action="{{ route('inventory.parts.providers.update', ['part' => $part, 'provider' => $provider]) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            <fieldset class="mb-4">
                                <div class="form-row">
                                    <div class="col-md-3 form-group">
                                        <label>Vendor</label>
                                        <select class="form-control select2"
                                                data-toggle="select2"
                                                name="provider_id"
                                                required>
                                            <option value="" selected>Select Vendor</option>
                                            @foreach($providerList as $key => $providerName)
                                                <option
                                                    value="{{ old('provider_id', $key) }}"
                                                    {{ old('provider_id', $key) === $provider->id ? 'selected' : '' }}>
                                                    {{ $providerName }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <label>Part Name <small> - as known by Vendor.</small></label>
                                            <input type="text" class="form-control" name="name" value="{{ old('name', $provider->pivot->name) }}">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Cost</label>
                                            <input type="number" class="form-control" name="cost" value="{{ old('cost', $provider->pivot->cost) }}" required>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('inventory.parts.show', $part) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

