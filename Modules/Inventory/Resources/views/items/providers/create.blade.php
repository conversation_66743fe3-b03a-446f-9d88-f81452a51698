@extends('layouts.master')

@section('title', 'Add Vendor to ' . $item->name)

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('general.vendors.index') }}">Vendors</a></li>
              <li class="breadcrumb-item active">Add Vendor</li>
            </ol>
          </div>
          <h4 class="page-title">Add Vendor</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-12">
        <div class="card">
          <div class="card-body px-5 py-4">
            <form action="{{ route('inventory.items.vendors.store', $item) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf
              <fieldset class="mb-2">

                <small class="mb-2 d-block">You can add multiple vendors by using the <code>+ Add</code> button.</small>
                <div class="form-row duplicate">
                  <div class="col-md-3 form-group">
                    <label>Vendor</label>
                    <select class="form-control select2 @error('provider_id') is-invalid @enderror"
                            data-toggle="select2"
                            name="provider_id[]"
                            required>
                      <option value="" selected>Select Vendor</option>
                      @foreach($providerList as $key => $providerName)
                        <option value="{{ $key }}">{{ $providerName }}</option>
                      @endforeach
                    </select>
                  </div>
                  <div class="col-md-5">
                    <div class="form-group">
                      <label>Part # <small> - as known by Vendor.</small></label>
                      <input type="text" class="form-control" name="name[]">
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label>Cost</label>
                      <input type="number" step="0.0001" class="form-control" name="cost[]" value="0" required>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label class="d-block">&nbsp;</label>
                      <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none">
                        <i class="mdi mdi-close"></i> Remove
                      </button>
                      <button type="button" class="btn btn-outline-secondary duplicate-btn"><i class="mdi mdi-plus"></i>
                        Add
                      </button>
                    </div>
                  </div>
                </div>
              </fieldset>
              <div class="d-flex align-items-center">
                <label class="form-check-label mr-2" for="update-item-price-input">Update Main Item Price?</label>
                <input
                  type="checkbox"
                  class="form-check form-check-inline"
                  name="update_item_price" {{ (bool) old('update_item_price') ? 'checked' : '' }}
                  id="update-item-price-input"
                  value="1">
              </div>
              <div class="form-group d-flex justify-content-end">
                <a class="btn btn-light mr-2" href="{{ route('inventory.items.show', $item) }}">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Changes</button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
@endsection

@include('inventory::shared.forms.duplicate')
