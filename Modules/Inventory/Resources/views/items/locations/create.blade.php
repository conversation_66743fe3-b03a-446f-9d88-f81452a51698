@extends('layouts.master')

@section('title', 'Item')

@section('content')
<!-- Start Content-->
<div class="container-fluid">

    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('inventory.parts.index') }}">Parts</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('inventory.parts.show', $part) }}">{{ $part->name }}</a></li>
                        <li class="breadcrumb-item active">Add Location</li>
                    </ol>
                </div>
                <h4 class="page-title">Add Part Location</h4>
            </div>
        </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card">
                <div class="card-body px-5 py-4">
                    <form action="{{ route('inventory.parts.locations.store', $part) }}"
                          method="post"
                          enctype="multipart/form-data">
                        @csrf
                        @include('inventory::shared.forms.create-store-location')

                        <div class="form-group d-flex justify-content-end">
                            <a class="btn btn-light mr-2" href="{{ route('items', $part) }}">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div> <!-- end col-->
    </div>
    <!-- end row-->

</div>
<!-- container -->
@endsection

@include('inventory::shared.forms.duplicate')
