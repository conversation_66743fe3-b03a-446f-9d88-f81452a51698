<?php

namespace Modules\Inventory\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\Inventory\Entities\Item;

class UpdateItemMultiplier implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

  /**
   * Create a new job instance.
   *
   * @return void
   */
  public function __construct()
  {
    //
  }

  /**
   * Execute the job.
   *
   * @return void
   */
  public function handle()
  {
    // FYI: This is already included in the main import
    foreach (Item::cursor() as $item) {
      $multiplier = $item->getOption('list_price_special');

      if ($multiplier == 0.00) {
        $multiplier = 2.5;
      }

      $item->update([
        'multiplier' => $multiplier
      ]);
    }
  }
}
