<?php


namespace Modules\Inventory\Actions;


use Illuminate\Support\Facades\DB;
use Modules\Inventory\Entities\Item;

class SwapItemAction
{
  protected UpdateItemPricingAction $updateItemPricingAction;
  protected UpdateItemProductPricingAction $updateItemProductPricingAction;

  public function __construct(
    UpdateItemPricingAction $updateItemPricingAction,
    UpdateItemProductPricingAction $updateItemProductPricingAction
  )
  {
    $this->updateItemPricingAction = $updateItemPricingAction;
    $this->updateItemProductPricingAction = $updateItemProductPricingAction;
  }

  public function execute(Item $item, array $details)
  {
    DB::table('item_part')
      ->whereItemId($item->id)
      ->update([
        'item_id' => $details['item_id'],
      ]);

    $swappedItem = Item::find($details['item_id']);

    $this->updateItemProductPricingAction->execute($swappedItem);
  }
}
