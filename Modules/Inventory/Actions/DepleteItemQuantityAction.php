<?php


namespace Modules\Inventory\Actions;


use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Modules\Inventory\Entities\InventoryImport;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Services\ItemService;

class DepleteItemQuantityAction
{
  /**
   * @param Item $item
   * @param $quantity
   * @return boolean
   */
  public function execute(Item $item, $quantity): bool
  {
    $item->decrement('quantity', $quantity);

    // TODO: Why does $item->bom->isEmpty() return bad method call exception here yet it works say in a controller method
    if (is_null($item->bom->first())) {
      return true;
    }

    $item->bom->each(function (Item $bomItem) use ($quantity) {
      $this->execute($bomItem, $bomItem->pivot->quantity * $quantity);
    });

    return true;
  }
}
