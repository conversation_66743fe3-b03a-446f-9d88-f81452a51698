<?php


namespace Modules\Inventory\Actions;


use Modules\Inventory\Entities\Level;

class IncreaseLevelPriceAction
{
  public function execute(Level $level, $data): array
  {
    $updatedItemPrices = $level->items()
      ->whereIn('item_id', $data['item_id'])
      ->get()
      ->mapWithKeys(function ($item) use ($data) {
        $fixedPrice = $item->pivot->fixed_price;
        $newFixedPrice = money_add(
          $fixedPrice,
          money_multiply(
            $fixedPrice,
            $data['percentage'] / 100
          )
        );

        return [
          $item->id => [
            'fixed_price' => round($newFixedPrice)
          ]
        ];
      })->all();

    return $level->items()->syncWithoutDetaching($updatedItemPrices);
  }
}
