<?php


namespace Modules\Inventory\Actions;


use Illuminate\Support\Arr;
use Modules\Inventory\Entities\Item;

class UpdateItemProductPricingAction
{
  protected UpdateItemPricingAction $updateItemPricingAction;

  public function __construct(UpdateItemPricingAction $updateItemPricingAction)
  {
    $this->updateItemPricingAction = $updateItemPricingAction;
  }

  public function execute(Item $item)
  {
    $item->bomProducts->each(function (Item $bomProduct) {

      $this->updateItemPricingAction->execute($bomProduct);

      $bomProduct->bomProducts->each(function (Item $bomItem) {
        $this->updateItemPricingAction->execute($bomItem);
      });
    });
  }
}
