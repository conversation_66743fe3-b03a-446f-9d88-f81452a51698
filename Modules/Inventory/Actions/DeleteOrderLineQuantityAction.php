<?php


namespace Modules\Inventory\Actions;


use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Modules\Inventory\Entities\InventoryImport;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Services\ItemService;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;

class DeleteOrderLineQuantityAction
{
  /**
   * @param Item $item
   * @param $oldQuantity
   * @param $orderNumber
   *
   * @return boolean
   */
  public function execute(Item $item, $oldQuantity, Order $order): bool
  {
    $item->increment('quantity', $oldQuantity);
    $route = route('orderModule.orders.show', $order);
    log_activity($item, "changed quantity from ({$oldQuantity}) to (0) on <a href='{$route}'>Order# {$order->full_number}</a>");

    if (is_null($item->bom->first())) {
      return true;
    }

    $item->bom->each(function (Item $bomItem) use ($oldQuantity, $order) {
      $this->execute($bomItem, round($bomItem->pivot->quantity * $oldQuantity, 2), $order);
    });

    return true;
  }
}
