<?php


namespace Modules\Inventory\Actions;


use Modules\Inventory\Entities\Item;

class AddBillOfMaterialsItemAction
{
  protected UpdateItemPricingAction $updateItemPricingAction;
  protected UpdateItemProductPricingAction $updateItemProductPricingAction;

  public function __construct(
    UpdateItemPricingAction $updateItemPricingAction,
    UpdateItemProductPricingAction $updateItemProductPricingAction
  )
  {
    $this->updateItemPricingAction = $updateItemPricingAction;
    $this->updateItemProductPricingAction = $updateItemProductPricingAction;
  }

  public function execute(Item $item, array $bomDetails)
  {
    $bomRecords = collect($bomDetails['item_id'])
      ->mapWithKeys(function ($itemId, $key) use ($bomDetails) {
        if (is_null($itemId)) {
          return [null];
        }

        return [
          $itemId => [
            'quantity' => $bomDetails['quantity'][$key]
          ]
        ];
      })
      ->reject(function ($itemDetails) {
        return is_null($itemDetails);
      })->all();

    $item->bom()->syncWithoutDetaching($bomRecords);
    $item = $item->fresh();
    
    $this->updateItemPricingAction->execute($item);
    $this->updateItemProductPricingAction->execute($item);
  }
}
