<?php


namespace Modules\Inventory\Actions;


use Modules\Inventory\Entities\Item;

class DeleteBillOfMaterialsItemAction
{
  protected UpdateItemPricingAction $updateItemPricingAction;
  protected UpdateItemProductPricingAction $updateItemProductPricingAction;

  public function __construct(
    UpdateItemPricingAction $updateItemPricingAction,
    UpdateItemProductPricingAction $updateItemProductPricingAction
  )
  {
    $this->updateItemPricingAction = $updateItemPricingAction;
    $this->updateItemProductPricingAction = $updateItemProductPricingAction;
  }

  public function execute(Item $item, Item $part)
  {
    $item->bom()->detach($part->id);
    $item = $item->fresh();

    $this->updateItemPricingAction->execute($item);
    $this->updateItemProductPricingAction->execute($item);
  }
}
