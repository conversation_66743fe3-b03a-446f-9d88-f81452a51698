<?php


namespace Modules\Inventory\Services;

use App\Actions\GetSearchConditionsAction;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class ItemSearchService
{
  const FIELD_ALL = 'all';
  const OPERATOR_BETWEEN = 'BETWEEN';
  const OPERATOR_NOT_BETWEEN = 'NOT BETWEEN';
  const OPERATOR_IS_NULL = 'IS NULL';
  const OPERATOR_IS_NOT_NULL = 'IS NOT NULL';

  protected bool $outOfStock = false;

  static protected array $hideFromSearch = [
    'id',
    'category_id',
    'commission_number',
    'deleted_at',
    'model',
    'modifications',
    'modified',
    'modified_at',
    'ordering_group',
  ];

  static protected array $relatedSearchFields = [
    'categories.name' => 'Category Name',
    'item_provider.name' => 'Vendor Part Number',
  ];

  public function searchQuery(): Builder
  {
    $query = $this->defaultQuery();

    if (request()->filled('fields')) {
      $this->getFilters()
        ->each(function ($condition, $field) use (&$query) {
          $query = $this->buildQuery($query, $condition, $field);
        });
    }

    return $query->latest('items.created_at');
  }

  public function outOfStock() :self
  {
    $this->outOfStock = true;

    return $this;
  }

  public function find()
  {
    $request = request();

    if (! $request->filled('fields')) {
      return $this->getItems();
    }

    return $this->searchQuery()->orderBy('items.code')
      ->get();
  }

  protected function buildQuery(Builder $query, $condition, $field = null)
  {
    $value = $condition['value'];
    $operator = $condition['operator'];

    if ($operator === self::OPERATOR_BETWEEN) {
      $pieces = explode(' ', $value);
      return $query->whereBetween($field, [$pieces[0], $pieces[2]]);
    }

    if ($operator === self::OPERATOR_NOT_BETWEEN) {
      $pieces = explode(' ', $value);
      return $query->whereNotBetween($field, [$pieces[0], $pieces[2]]);
    }

    if ($operator === self::OPERATOR_IS_NULL) {
      return $query->whereNull($field);
    }

    if ($operator === self::OPERATOR_IS_NOT_NULL) {
      return $query->whereNotNull($field);
    }

    return $query->where($field, $operator, $value);

  }

  protected function searchAnyFields($query, $condition)
  {
    return $query->where(function ($query) use ($condition) {
      $query->where('customers.account_number', $condition['operator'], $condition['value'])
        ->orWhere('customers.code', $condition['operator'], $condition['value'])
        ->orWhere('customers.fax', $condition['operator'], $condition['value'])
        ->orWhere('customers.payment_terms', $condition['operator'], $condition['value'])
        ->orWhere('customers.tax_exemption', $condition['operator'], $condition['value'])
        ->orWhere('customers.options', $condition['operator'], $condition['value'])
        ->orWhere('users.name', $condition['operator'], $condition['value'])
        ->orWhere('users.phone', $condition['operator'], $condition['value'])
        ->orWhere('users.email', $condition['operator'], $condition['value'])
        ->orWhere('contacts.name', $condition['operator'], $condition['value'])
        ->orWhere('contacts.email', $condition['operator'], $condition['value'])
        ->orWhere('contacts.phone', $condition['operator'], $condition['value'])
        ->orWhere('addresses.name', $condition['operator'], $condition['value'])
        ->orWhere('addresses.unit', $condition['operator'], $condition['value'])
        ->orWhere('addresses.state', $condition['operator'], $condition['value'])
        ->orWhere('addresses.city', $condition['operator'], $condition['value'])
        ->orWhere('addresses.zip', $condition['operator'], $condition['value']);
    });
  }

  protected function getFilters(): \Illuminate\Support\Collection
  {
    $conditionAction = app(GetSearchConditionsAction::class);

    return collect(request('fields'))
      ->mapWithKeys(function ($field, $key) use ($conditionAction) {
        return [
          $field => Arr::get(
            $conditionAction->execute(request('terms')[$key]),
            request('conditions')[$key],
            []
          )
        ];
      });
  }

  protected function getItems()
  {
    return Cache::remember('items', 36000, function () {
      return DB::table('items')
        ->selectRaw("
        items.id,
        items.code,
        item_provider.name as vendor_part_number,
        items.description,
        categories.name as category_name,
        items.cost,
        items.list_price
      ")
        ->leftJoin('categories', 'categories.id', '=', 'items.category_id')
        ->leftJoin('item_provider', 'item_provider.item_id', '=', 'items.id')
        ->leftJoin('providers', 'item_provider.provider_id', '=', 'providers.id')
        ->orderBy('items.code')
        ->get();
    });
  }

  protected function defaultQuery(): Builder
  {
    $query = DB::table('items')
      ->selectRaw("
        items.id,
        items.code,
        providers.name as vendor_name,
        item_provider.name as vendor_part_number,
        items.description,
        categories.name as category_name,
        items.quantity,
        items.cost,
        items.list_price
      ")
      ->leftJoin('categories', 'categories.id', '=', 'items.category_id')
      ->leftJoin('item_provider', 'item_provider.item_id', '=', 'items.id')
      ->leftJoin('providers', 'item_provider.provider_id', '=', 'providers.id')
      ->whereNull('items.deleted_at');

    if ($this->outOfStock) {
      $query = $query->where('items.quantity', '<=', 0)->where('quantity_frozen', 0);
    }

    return $query;
  }

  public static function searchFields(): array
  {
    return collect(Schema::getColumnListing('items'))
      ->reject(function ($column) {
        return in_array($column, self::$hideFromSearch);
      })
      ->mapWithKeys(function ($column) {
        return [
          'items.' . $column => Str::title(str_replace('_', ' ', $column))
        ];
      })
      ->merge(self::$relatedSearchFields)
      ->sort()
      ->all();
  }

  public function conditions($term = ''): array
  {
    return app(GetSearchConditionsAction::class)->execute($term);
  }
}
