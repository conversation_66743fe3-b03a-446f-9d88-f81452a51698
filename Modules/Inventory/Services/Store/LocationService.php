<?php


namespace Modules\Inventory\Services\Store;


use Illuminate\Http\Request;
use Modules\Inventory\Entities\Store;

class LocationService
{
    public function getLocationsFromRequest(Request $request)
    {
        return collect($request->store_id)->map(function ($storeId, $key) use ($request) {
            return [
                'store_id' => $storeId,
                'quantity' => $request->store_quantity[$key],
                'level' => $request->store_level[$key],
                'name' => $request->store_name[$key]
            ];
        })->all();
    }

    public function storeList()
    {
        return Store::orderBy('name')->pluck('name', 'id')->all();
    }
}
