<?php


namespace Modules\Inventory\Services\Parts;


use Illuminate\Http\Request;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Entities\Provider;

class ProviderService
{
  public function getProvidersFromRequest(Request $request): \Illuminate\Support\Collection
  {
    return collect($request->provider_id)->mapWithKeys(function ($providerId, $key) use ($request) {
      return [
        $providerId => [
          'name' => $request->name[$key],
          'cost' => $request->cost[$key],
        ]
      ];
    });
  }

  public function providerList(Item $part)
  {
    // Get list of vendors not yet attached to $part
    return Provider::vendor()->get()->filter(function ($provider) use ($part) {
      return !$part->providers->contains($provider);
    })->pluck('name', 'id')->all();
  }

  public function allProvidersList()
  {
    return Provider::vendor()->pluck('name', 'id');
  }
}
