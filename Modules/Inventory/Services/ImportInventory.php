<?php


namespace Modules\Inventory\Services;

use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Modules\General\Entities\Category;
use Modules\Inventory\Entities\Level;
use Modules\Inventory\Entities\Note;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Entities\Provider;

class ImportInventory
{
  /**
   * Import items (parts and products) into the inventory
   *
   * @param array $sourceData
   */
  public function importInventory(array $sourceData)
  {
    // We don't need audit records here.
    Item::disableAuditing();
    Note::disableAuditing();
    Category::disableAuditing();
    // Extract categories from the item raw data and sync with existing categories
    //$this->syncItemCategories($sourceData);
    // We need to clean the raw data so we don't have to deal with bad such as empty rows, or rows without part#
    //$cleanedItems = $this->cleanItems($sourceData);
    // Now we create the items
    //$this->createItems($cleanedItems);
    // Now that we have imported the items and extracted their categories
    // Sync the categories onto the available levels.
    $this->syncLevelCategories();
    // Import is complete, so we can re-enable auditing
    Item::enableAuditing();
    Note::enableAuditing();
    Category::enableAuditing();
  }

  public function importItemPriceLevels(array $sourceData)
  {
    collect($sourceData)->each(function ($levelPrice) {

      $item = Item::whereCode($levelPrice['SOFTENERS'])->first();

      if (empty($item)) {
        return;
      }

      $levelPrices = collect(Arr::except($levelPrice, 'SOFTENERS'))
        ->mapWithKeys(function ($price, $levelId) {
          return [
            $levelId => ['fixed_price' => $price]
          ];
        })->all();

      $item->levels()->sync($levelPrices);
    });
  }


  /**
   * Clean items to remove duplicates and items without part number
   *
   * @param array $sourceData
   * @return Collection
   */
  protected function cleanItems(array $sourceData): Collection
  {
    return collect($sourceData)
      ->unique('OurPart#')
      ->reject(function ($row) {
        return empty($row['OurPart#']);
      });
  }

  /**
   * Create new part categories or update existing ones
   *
   * @param $source
   * @return Collection
   */
  public function syncItemCategories($source)
  {
    return collect($source)
      ->unique('Category')
      ->reject(function ($row) {
        /**
         * Unique method above will leave at least one blank category field
         * We also don't want to bother with a category without a part number
         * Lastly, we are rejecting categories name any alpha-numerals i.e only punctuation characters,
         * we assume that was some typing mistake.
         */
        return empty($row['Category']) || empty($row['OurPart#']) || ctype_punct($row['Category']);
      })
      ->mapWithKeys(function ($row, $key) {
        return [
          $key => [
            'Category' => $row['Category']
          ]
        ];
      })
      ->each(function ($row) {
        Category::updateOrCreate(
          [
            'name' => $row['Category'],
          ],
          [
            'type' => 'item'
          ]
        );
      });
  }

  public function syncLevelCategories()
  {
    $categoryIds = Category::item()->pluck('id')->all();
    Level::all()
      ->each(function ($level) use ($categoryIds) {
        $level->categories()->syncWithoutDetaching($categoryIds);
      });
  }

  public function findItem($id)
  {
    return Item::find($id);
  }

  /**
   * @param array $rows
   */
  protected function createItems(Collection $rows)
  {
    $rows->each(function ($row) {
      $this->createItem($row);
    });
  }

  /**
   * @param array $row
   * @return bool
   */
  protected function createItem(array $row)
  {
    $record = $this->extractItemData($row);
    // We still update or create new item even after removing duplicates
    // because we are getting item data in chunks
    $item = Item::updateOrCreate(
      [
        'code' => $record['code']
      ],
      $record
    );
    $item->notes()->delete();
    $item->notes()->saveMany($record['notes']);

    if (!empty($record['vendor'])) {
      $item->vendors()->sync($record['vendor']);
    }

    return true;
  }

  /**
   * Extract data from the request and map to the part attributes
   * All other unknown attributes are saved in part options (json)
   *
   * @param array $row
   * @return array
   */
  protected function extractItemData(array $row): array
  {
    $type = empty($row['BOM']) ? 'part' : 'product';
    $listPriceSpecial = floatval($row['ListPriceSpecial']);
    $multiplier = $listPriceSpecial > 0.0 ? $listPriceSpecial : 2.5;
    $record = [
      'code' => $row['OurPart#'],
      'number' => $row['NumericPartNo'],
      'name' => $this->getItemName($row),
      'type' => $type,
      'price' => $this->formatNumber($row['Price']),
      'freight_cost' => $this->formatNumber($row['Freight']),
      'misc_cost' => $this->formatNumber($row['MiscCost']),
      'cost' => $this->formatNumber($row['OurCost']),
      'list_price' => $this->formatNumber($row['ListPrice']),
      'multiplier' => $this->formatNumber($multiplier, 2),
      'unit' => $row['Unit'],
      'price_code' => $row['PriceCode'],
      'salable' => strtolower($row['SALESITEM']) === 'yes',
      'quantity' => (int)$row['InvQty'],
      'category_id' => $this->getCategoryId($row['Category']),
      'description' => $row['Description'],
      'build_description' => $row['BuildDescription'],
      'purchase_instructions' => $row['PurchaseDescription'],
      'receiving_instructions' => $row['ReceivingInstructions'],
      'rebuild_list' => $row['RebuildList'],
      'weight' => $this->formatNumber($row['Weight'], 3),
      'height' => $this->formatNumber($row['HEIGHT']),
      'width' => $this->formatNumber($row['WIDTH']),
      'depth' => $this->formatNumber($row['DEPTH']),
      'label' => $row['LABEL'],
      'created_at' => Carbon::parse($row['Date'])->toDateTimeString(),
      //'cost_date' => Carbon::parse($row['Date'])->toDateString(),
      'options' => [
        'approved_by' => $row['ApprovedBy'],
        'bom' => $row['BOM'],
        'bom_cost' => $row['BOMCost'],
        'bom_list' => $row['BOMList'],
        'calc_price' => $row['CalcPrice'],
        'case_quantity' => $row['CaseQty'],
        'customer_codes' => $row['CustomerCodes'],
        'expanded_bom' => $row['EXPANDEDBOM'],
        'favorite' => (bool)$row['Favorite'],
        'list_price' => $row['ListPrice'],
        'list_price_special' => $row['ListPriceSpecial'],
        'manufacturer_part_number' => $row['MfgPart#'],
        'new_list_date' => $row['NEWLISTDATE'],
        'program_as' => $row['ProgramAs'],
        'purchase_unit' => $row['PurchaseUnit'],
        'real_value' => $row['REALVALUE'],
        'retail' => $row['Retail'],
        'soft_list' => $row['SOFTLIST'],
        'sync_time' => $row['SYNCTIME'],
        'sort' => $row['Sort'],
        'special_instruction' => $row['SpecialInstructions'],
        'special_price' => $row['SpecialPrice'],
        'specs_description' => $row['SpecsDescription'],
        'stock_quantity' => $row['StockQty'],
        'temp' => $row['Temp'],
        'test' => $row['Test'],
        'true_cost' => $row['TrueCost'],
        'options' => $row['Options']
      ],
      'notes' => [
        [
          'type' => 'general',
          'body' => $row['GeneralNotes'],
        ],
        [
          'type' => 'default',
          'body' => $row['Notes']
        ],
        [
          'type' => 'product',
          'body' => $row['ProductNotes']
        ]
      ]
    ];

    // Remove empty notes
    $record['notes'] = $this->getNotes($record['notes']);

    if (empty($row['Vendor'])) {
      // This item has no vendor information so let's just return the known item details
      return $record;
    }

    $vendor = Provider::whereAccountNumber($row['Vendor'])->first();

    if (empty($vendor)) {
      // This item has an unknown vendor. So let's just keep those details
      $record['options']['price'] = $row['Price'];
      $record['options']['vendor'] = $row['Vendor'];

      return $record;
    }

    // The item's vendor is known so let's extract the details
    $record['vendor'] = [
      $vendor->id => [
        'cost' => $row['Price'],
        'name' => $row['MfgPart#']
      ]
    ];

    return $record;
  }

  protected function getItemName(array $row)
  {
    return !empty($row['Description']) ? $row['Description'] : $row['OurPart#'];
  }

  protected function getCategoryId($rawCategory)
  {
    $category = Category::type('item')->where('name', $rawCategory)->first();

    return optional($category)->id;
  }

  protected function getNotes(array $rawNotes)
  {
    return collect($rawNotes)
      ->reject(function ($note) {
        return empty($note['body']);
      })
      ->map(function ($note) {
        return new Note([
          'body' => $note['body'],
          'type' => $note['type']
        ]);
      })
      ->all();
  }

  /**
   * TODO: Make this a helper method so we can use in multiple places
   *
   * @param $num
   * @param int $decimals
   * @return float
   */
  public function formatNumber($num, $decimals = 2)
  {
    return floatval(number_format($num, $decimals, '.', ''));
  }
}
