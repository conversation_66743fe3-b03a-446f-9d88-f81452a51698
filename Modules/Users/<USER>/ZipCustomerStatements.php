<?php

namespace Modules\Users\Jobs;

use App\Events\BatchCustomerStatementsGenerated;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ZipCustomerStatements implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

  /**
   * Execute the job.
   *
   * @return void
   */
  public function handle()
  {
    $zipFileName = storage_path('app/statements/zips/') . now()->toDateString() . '-batch-customer-statements.zip';

    $zip = new \ZipArchive();
    $zip->open($zipFileName, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

    $files = new \RecursiveIteratorIterator(
      new \RecursiveDirectoryIterator(
        storage_path('app/statements/customers')
      )
    );

    foreach ($files as $name => $file) {
      if (!$file->isDir()) {
        $filePath = $file->getRealPath();

        $zip->addFile($filePath, basename($filePath));
      }
    }

    $zip->close();

    File::cleanDirectory(storage_path('app/statements/customers'));

    event(
      new BatchCustomerStatementsGenerated(
        Str::after($zipFileName, 'statements/zips/')
      )
    );
  }
}
