<?php

namespace Modules\Users\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Users\Entities\Department;
use Modules\Users\Entities\User;

class DepartmentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any department.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->can('view department');
    }

    /**
     * Determine whether the user can view the department.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     * @throws \Exception
     */
    public function view(User $user)
    {
        return $user->can('view department');
    }

    /**
     * Determine whether the user can create departments.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->can('create department');
    }

    /**
     * Determine whether the user can update the department.
     *
     * @param \Modules\Users\Entities\User $user
     * @param Department $department
     * @return mixed
     */
    public function update(User $user)
    {
        return $user->can('update department');
    }

    /**
     * Determine whether the user can delete the department.
     *
     * @param \Modules\Users\Entities\User $user
     * @param Department $department
     * @return mixed
     */
    public function delete(User $user, Department $department)
    {
        return $user->can('delete department');
    }

    /**
     * Determine whether the user can restore the department.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function restore(User $user)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the department.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function forceDelete(User $user)
    {
        return $user->isAdmin();
    }
}
