<?php

namespace Modules\Users\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Modules\Users\Entities\Preference;

class PreferenceController extends Controller
{
    use AuthorizesRequests;

    public function __construct()
    {
        $this->authorizeResource(Preference::class, 'preference');
    }

    /**
     * Display a listing of the resource.
     * @return Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function index()
    {
        $this->authorize('viewAny', Preference::class);

        $preferences = Cache::remember('preferences', now()->secondsUntilEndOfDay(), function () {
            return Preference::orderBy('name')
                ->get();
        });

        return view('users::preferences.index', compact('preferences'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('users::preferences.create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name.*' => 'required|string|max:255'
        ]);

        collect($request->name)
            ->each(function ($name) {
                Preference::create(['name' => $name]);
            });

        return redirect()
            ->route('users.preferences.index')
            ->with('success', 'Preferences created successfully');
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show(Preference $preference)
    {
        return view('users::preferences.show', compact('preference'));
    }

    /**
     * Show the form for editing the specified resource.
     * @param Preference $preference
     * @return Response
     */
    public function edit(Preference $preference)
    {
        return view('users::preferences.edit', compact('preference'));
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param Preference $preference
     * @return Response
     */
    public function update(Request $request, Preference $preference)
    {
        $request->validate([
            'name' => 'required|string|max:255'
        ]);

        $preference->update($request->all());

        return redirect()
                ->route('users.preferences.index')
                ->with('success', 'Preference updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     * @param Preference $preference
     * @return Response
     */
    public function destroy(Preference $preference)
    {
        try {
            $preference->delete();

            return redirect()
                ->route('users.preferences.index')
                ->with('success', 'Preference deleted successfully');
        } catch (\Exception $e) {
            return back()
                    ->with('error', 'Could not be deleted. Message: ' . $e->getMessage());
        }
    }
}
