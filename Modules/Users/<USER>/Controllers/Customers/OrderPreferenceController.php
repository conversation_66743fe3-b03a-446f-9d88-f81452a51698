<?php

namespace Modules\Users\Http\Controllers\Customers;

use App\Services\ListService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Modules\Orders\Entities\Order;
use Modules\Users\Entities\Preference;

class OrderPreferenceController extends Controller
{
    use AuthorizesRequests;

    protected $lists;

    public function __construct(ListService $lists)
    {
        $this->lists = $lists;
    }

    /**
     * Show the form for creating a new resource.
     * @param Order $order
     * @return Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function create(Order $order)
    {
        $this->authorize('update order', Order::class);

        $ids = Arr::pluck($order->customer->preferences, 'id');
        $preferenceList = $this->lists->preferenceListWithout($ids);

        return view('users::customers.orders.create-preferences', compact('order','preferenceList'));
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @param Order $order
     * @return Response
     */
    public function store(Request $request, Order $order)
    {
        $request->validate([
            'preference_id' => 'nullable|array|min:1',
            'preference_id.*' => 'exists:preferences,id',
            'preference_value' => 'nullable|required_with:preference_id|array|min:1',
            'preference_value.*' => 'nullable|string|max:400'
        ]);

        $preferenceList = $this->lists->preferenceList();
        $preferences = collect($request->preference_id)
            ->reject(function ($id, $key) use ($request) {
                return empty($request->preference_value[$key]);
            })
            ->map(function ($id, $key) use ($request, $preferenceList) {
                return [
                    'id' => $id,
                    'name' => Arr::get($preferenceList, $id),
                    'value' => Arr::get($request->preference_value, $key)
                ];
            })->all();

        $options = $order->options;
        $options['preferences'] = $preferences;
        $order->options = $options;
        $order->save();

        return redirect()
            ->route('orderModule.orders.show', $order)
            ->with('success', 'Customer preferences updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     * @param Order $order
     * @param $preferenceId
     * @return Response
     */
    public function destroy(Order $order, $preferenceId)
    {
        $options = $order->options;
        $options['preferences'] = collect($order->preferences)
            ->reject(function ($preference) use ($preferenceId) {
                return $preference['id'] === $preferenceId;
            })
            ->all();
        $order->options = $options;
        $order->save();

        return back()
            ->with('success', 'Order preference removed successfully');
    }
}
