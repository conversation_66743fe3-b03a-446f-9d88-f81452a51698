<?php

namespace Modules\Users\Http\Controllers\Customers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Users\Jobs\EmailCustomerStatements;

class SendStatementBatchEmails extends Controller
{
  public function __invoke(Request $request)
  {
    $request->validate([
      'body' => 'required|string|max:1000'
    ]);

    dispatch(new EmailCustomerStatements($request->body));

    return back()
      ->withSuccess('Batch email activated, emails are being sent in the background.');
  }
}
