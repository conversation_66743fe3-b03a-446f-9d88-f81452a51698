<?php

namespace Modules\Users\Http\Controllers\Customers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Users\Jobs\GenerateCustomerStatements;

class BatchPrintCustomerStatements extends Controller
{
  public function __invoke(Request $request)
  {
    $request->validate([
      'statement_type' => 'required|string',
    ]);

    dispatch(new GenerateCustomerStatements($request->statement_type));
    $message = 'Batch print initiated successfully';

    if ($request->isJson()) {
      return response()->json([
        'message' => $message
      ], 200);
    }

    return back()
      ->with('success', $message);
  }
}
