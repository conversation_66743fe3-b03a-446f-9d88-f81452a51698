<?php

namespace Modules\Users\Http\Controllers\Customers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Users\Entities\Address;
use Modules\Users\Entities\Customer;

class AddressController extends Controller
{

    /**
     * Show the form for creating a new resource.
     * @param Customer $customer
     * @return Response
     */
    public function create(Customer $customer)
    {
        $address = new Address();

        return view('users::customers.addresses.create', compact('customer', 'address'));
    }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @param Customer $customer
   * @return Response
   */
    public function store(Request $request, Customer $customer)
    {
        $attributes = $request->validate([
            'state' => 'required|string|max:10',
            'city' => 'required|string|max:50',
            'zip' => 'nullable|string|max:25',
            'unit' => 'nullable|string|max:255',
            'type' => 'nullable|string|max:25',
            'name' => 'nullable|string|max:255'
        ]);

        $address = $customer->addresses()->create($attributes);
        $customer->{$address->type . '_address'} = $address->full_address;
        $customer->save();

        return redirect()->route('users.customers.show', $customer)->with('success', 'Address added successfully');
    }

    /**
     * Show the specified resource.
     * @param Customer $customer
     * @param Address $address
     * @return Response
     */
    public function show(Customer $customer, Address $address)
    {
        return view('users::customers.addresses.show', compact('customer', 'address'));
    }

    /**
     * Show the form for editing the specified resource.
     * @param Customer $customer
     * @param Address $address
     * @return Response
     */
    public function edit(Customer $customer, Address $address)
    {
        return view('users::customers.addresses.edit', compact('customer', 'address'));
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param Customer $customer
     * @param Address $address
     * @return Response
     */
    public function update(Request $request, Customer $customer, Address $address)
    {
        $request->validate([
            'state' => 'required|string|max:10',
            'city' => 'required|string|max:50',
            'zip' => 'nullable|string|max:25',
            'unit' => 'nullable|string|max:255',
            'type' => 'required|string|max:25'
        ]);

        $address->update($request->all());

        if (! $address->forShipping() || ($address->forShipping() && $address->active)) {
            $customer->{$address->type . '_address'} = $address->full_address;
            $customer->save();
        }


        return redirect()
                ->route('users.customers.show', $customer)
                ->with('success', 'Address updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     * @param Customer $customer
     * @param Address $address
     * @return Response
     */
    public function destroy(Customer $customer, Address $address)
    {
        try {
            $address->delete();

            return back()
                    ->with('success', 'Address deleted successfully');
        } catch (\Exception $e) {
            return back()
                    ->with('error', 'Could not delete address. Message returned: ' . $e->getMessage());
        }
    }
}
