<?php

namespace Modules\Users\Http\Controllers\Customers;

use App\Services\ListService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\General\Entities\Category;
use Modules\Users\Entities\Customer;

class CategoryDiscountController extends Controller
{
  protected $lists;

  public function __construct(ListService $lists)
  {
    $this->lists = $lists;
  }

  /**
   * Show the form for creating a new resource.
   * @return Response
   */
  public function create(Customer $customer)
  {
    $categoryList = $this->lists
      ->itemCategoryList()
      ->reject(function ($category) use ($customer) {
        return $customer->categoryDiscounts->contains('name', $category);
      });

    return view('users::customers.pricing.create-category-discount', compact('categoryList', 'customer'));
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @param Customer $customer
   * @return Response
   */
  public function store(Request $request, Customer $customer)
  {
    $request->validate([
      'category_id' => 'required|array',
      'category_id.*' => 'required|exists:categories,id',
      'discount' => 'required|array',
      'discount.*' => 'required|numeric|min:0'
    ]);

    $customer->categoryDiscounts()->attach($this->getCategoryDiscounts());

    return redirect()
      ->route('users.customers.show', $customer)
      ->with('success', 'Category discounts added successfully');
  }

  /**
   * Show the form for editing the specified resource.
   * @param int $id
   * @return Response
   */
  public function edit($id)
  {
    return view('users::customers.pricing.edit-category-discount');
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param int $id
   * @return Response
   */
  public function update(Request $request, $id)
  {
    //
  }

  /**
   * Remove the specified resource from storage.
   * @param Customer $customer
   * @param Category $category
   * @return Response
   */
  public function destroy(Customer $customer, Category $category)
  {
    $customer->categoryDiscounts()->detach($category->id);

    return back()
      ->with('success', 'Removed category discount successfully');
  }

  protected function getCategoryDiscounts()
  {
    return collect(request('category_id'))
      ->mapWithKeys(function ($categoryId, $key) {
        return [
          $categoryId => [
            'discount' => request('discount')[$key]
          ]
        ];
      });
  }

}
