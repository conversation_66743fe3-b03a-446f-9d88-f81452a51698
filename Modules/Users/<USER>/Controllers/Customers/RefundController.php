<?php

namespace Modules\Users\Http\Controllers\Customers;

use App\Services\ListService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Str;
use Modules\Orders\Actions\Payments\RefundPaymentAction;
use Modules\Orders\Actions\ReversePaymentAction;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Payment;
use Modules\Users\Entities\Refund;
use Modules\Orders\Http\Requests\StoreRefund;
use Modules\Users\Entities\Customer;

class RefundController extends Controller
{
  protected $lists;

  public function __construct(ListService $listService)
  {
    $this->lists = $listService;
  }

  public function index()
  {
    return view('users::refunds.index', [
      'refunds' => Payment::where('refund', true)->latest()->get()
    ]);
  }

  /**
   * Show the form for creating a new resource.
   * @param Customer $customer
   * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
   */
  public function create(Customer $customer)
  {
    return view('users::refunds.create', [
      'customer' => $customer,
      'nextNumber' => (new Payment)->nextPaymentNumber(),
      'cardBrandList' => $this->lists->cardBrandList(),
      'refundableInvoices' => $customer->invoicesWithNegativeBalance
        ->map(function (Order $invoice) {
          return [
            'id' => $invoice->id,
            'description' => 'Invoice# ' . $invoice->number . ' ($' . abs($invoice->balance) . ')',
            'amount' => $invoice->balance
          ];
        })->all(),
      'paymentMethodList' => [
        'cash' => 'Cash',
        'check' => 'Check',
        'credit_card' => 'Credit Card',
        'eft' => 'EFT'
      ],
    ]);
  }

  /**
   * Store a newly created resource in storage.
   *
   * @param StoreRefund $request
   * @param Customer $customer
   * @return Response
   */
  public function store(StoreRefund $request, RefundPaymentAction $refundPaymentAction, Customer $customer)
  {
    $refundPaymentAction->execute($customer, $request->all());

    return redirect()->route('users.customers.show', $customer)
      ->withSuccess('Refund recorded successfully');
  }

  /**
   * Show the form for editing the specified resource.
   * @param Customer $customer
   * @param Payment $payment
   * @return Response
   */
  public function edit(Customer $customer, Payment $payment)
  {
    $nextPaymentNumber = $payment->number;
    $paymentMethodList = $this->lists->paymentMethodList();

    return view('users::customers.payments.edit',
      compact(
        'payment',
        'customer',
        'nextPaymentNumber',
        'paymentMethodList'
      ));
  }

  /**
   * Update the specified resource in storage.
   *
   * @param Customer $customer
   * @param Payment $payment
   * @return Response
   */
  public function update(Customer $customer, Payment $payment)
  {
    $payment->update([
      'delete_requested' => true,
      'delete_requested_by' => auth()->id()
    ]);

    return redirect()
      ->back()
      ->with('success', 'Payment saved successfully');
  }

  /**
   * Remove the specified resource from storage.
   * @param Refund $refund
   * @return Response
   */
  public function destroy(ReversePaymentAction $reversePaymentAction, Payment $refund)
  {

    try {
      $reversePaymentAction->execute($refund);

      return redirect()
        ->route('users.refunds.index')
        ->with('success', 'Refund deleted successfully');

    } catch (\Exception $e) {
      \Log::debug($e->getMessage());

      return back()
        ->with('error', 'Could not delete refund.');
    }
  }
}
