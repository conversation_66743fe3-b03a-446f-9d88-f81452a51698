<?php

namespace Modules\Users\Http\Controllers\Customers;

use App\Services\ListService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Inventory\Entities\Product;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\Referral;
use Modules\Users\Entities\User;

class ReferralController extends Controller
{
  /**
   * @var ListService
   */
  protected ListService $lists;

  public function __construct(ListService $lists)
  {
    $this->lists = $lists;
  }

  /**
   * Show the form for creating a new resource.
   * @param Customer $customer
   * @return Response
   */
  public function create(Customer $customer)
  {
    $customerList = $this->lists->refereeList($customer);

    return view('users::customers.referrals.create', compact('customer', 'customerList'));
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @param Customer $customer
   * @return \Illuminate\Http\RedirectResponse
   */
  public function store(Request $request, Customer $customer)
  {
    $request->validate([
      'new_customer_id' => 'required|exists:customers,user_id',
      'discount' => 'nullable|required_without:fixed|numeric'
    ]);

    $referral = $customer->referrals()->create($request->all());

    if ($request->filled('fixed')) {
      $referral->items()->attach($request->items);
    }

    return redirect()
      ->route('users.customers.show', $customer)
      ->with('success', 'Referral created successfully');
  }

  /**
   * Show the form for editing the specified resource.
   * @param Customer $customer
   * @param Referral $referral
   * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|Response|\Illuminate\View\View
   */
  public function edit(Customer $customer, Referral $referral)
  {
    return view(
      'users::customers.referrals.edit',
      compact('customer', 'referral')
    );
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param Customer $customer
   * @param Referral $referral
   * @return Response
   */
  public function update(Request $request, Customer $customer, Referral $referral)
  {
    $request->validate([
      'discount' => 'nullable|required_without:fixed|numeric',
    ]);

    $referral->update($request->only(['discount']));

    if ($request->filled('fixed')) {
      $referral->items()->sync($request->items);
    }

    return redirect()
      ->route('users.customers.show', $customer)
      ->with('success', 'Referral updated successfully');
  }

  /**
   * Remove the specified resource from storage.
   * @param Customer $customer
   * @param Referral $referral
   * @return \Illuminate\Http\RedirectResponse
   */
  public function destroy(Customer $customer, Referral $referral)
  {
    try {
      $referral->delete();

      return redirect()
        ->route('users.customers.show', $customer)
        ->with('success', 'Referral updated successfully');
    } catch (\Exception $e) {
      return back()->with('error', 'Could not delete referral. Error: ' . $e->getMessage());
    }
  }
}
