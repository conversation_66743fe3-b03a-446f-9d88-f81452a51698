<?php

namespace Modules\Users\Http\Controllers\Customers;

use App\Services\ListService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Str;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Entities\Product;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\CustomerItem;
use Modules\Users\Http\Requests\CreateSpecialPriceRequest;

class SpecialPriceController extends Controller
{

  protected $lists;

  public function __construct(ListService $lists)
  {
    $this->lists = $lists;
  }

  /**
   * Show the form for creating a new resource.
   * @param Customer $customer
   * @return Response
   */
  public function create(Customer $customer)
  {
    $itemList = $this->lists->itemPriceList();

    return view('users::customers.pricing.create', compact('customer', 'itemList'));
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @return Response
   */
  public function store(CreateSpecialPriceRequest $request, Customer $customer)
  {
    $customer->specialPriceItems()
      ->syncWithoutDetaching($this->getItems());

    return redirect()
      ->route('users.customers.show', $customer)
      ->with('success', 'Special prices added successfully.');
  }

  /**
   * Show the form for editing the specified resource.
   * @param Customer $customer
   * @param CustomerItem $pricing
   * @return Response
   */
  public function edit(Customer $customer, Item $item)
  {
    $customerItem = CustomerItem::whereCustomerId($customer->user_id)
      ->whereItemId($item->id)
      ->first();

    return view('users::customers.pricing.edit', compact('customer', 'item', 'customerItem'));
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param int $id
   * @return Response
   */
  public function update(Request $request, Customer $customer, Item $item)
  {
    $request->validate([
      'fixed_price' => 'nullable|required_without:discount_price',
      'discount_price' => 'nullable|required_without:fixed_price'
    ]);

    $customer->specialPriceItems()
      ->updateExistingPivot($item->id, [
        'fixed_price' => $request->fixed_price,
        'discount_price' => $request->discount_price
      ]);

    return redirect()
      ->route('users.customers.show', $customer)
      ->with('success', 'Customer special pricing updated successfully');
  }

  /**
   * Remove the specified resource from storage.
   * @param int $id
   * @return Response
   */
  public function destroy(Customer $customer, Item $item)
  {
    try {
      $customer->specialPriceItems()->detach($item->id);

      return back()->with('success', 'Special price has been deleted successfully.');
    } catch (\Exception $e) {
      return back()->with('error', 'Could not delete selected special price');
    }
  }

  protected function getItems(): array
  {
    return collect(array_filter(request('item_id')))
      ->mapWithKeys(function ($id, $key) {
        return [
          $id => [
            'fixed_price' => floatval(request('item_fixed_price')[$key]),
            'discount_price' => floatval(request('item_discount_price')[$key])
          ]
        ];
      })
      ->filter(function ($item) {
        return !empty($item['fixed_price']) || !empty($item['discount_price']);
      })
      ->all();
  }
}
