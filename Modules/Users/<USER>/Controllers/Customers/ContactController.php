<?php

namespace Modules\Users\Http\Controllers\Customers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Users\Entities\Contact;
use Modules\Users\Entities\Customer;
use Modules\Users\Http\Requests\CreateContactRequest;

class ContactController extends Controller
{
    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create(Customer $customer)
    {
        return view('users::customers.contacts.create', compact('customer'));
    }

    /**
     * Store a newly created resource in storage.
     * @param CreateContactRequest $request
     * @param Customer $customer
     * @return Response
     */
    public function store(CreateContactRequest $request, Customer $customer)
    {
        $customer->contacts()->create($request->all());

        return redirect()
                ->route('users.customers.show', $customer)
                ->with('success', 'Customer contact added successfully');
    }

    /**
     * Show the form for editing the specified resource.
     * @param Customer $customer
     * @param Contact $contact
     * @return Response
     */
    public function edit(Customer $customer, Contact $contact)
    {
        return view('users::customers.contacts.edit', compact('customer', 'contact'));
    }

    /**
     * Update the specified resource in storage.
     * @param CreateContactRequest $request
     * @param Customer $customer
     * @param Contact $contact
     * @return Response
     */
    public function update(CreateContactRequest $request, Customer $customer, Contact $contact)
    {
        $contact->update($request->all());

        return redirect()
            ->route('users.customers.show', $customer)
            ->with('success', 'Customer contact updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     * @param Customer $customer
     * @param Contact $contact
     * @return Response
     */
    public function destroy(Customer $customer, Contact $contact)
    {
        try {
            $contact->delete();

            return back()->withSuccess('Contact deleted successfully.');
        } catch (\Exception $exception) {
            return back()->withError('Could not delete contact.');
        }
    }
}
