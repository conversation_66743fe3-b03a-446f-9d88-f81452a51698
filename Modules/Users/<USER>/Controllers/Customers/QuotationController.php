<?php

namespace Modules\Users\Http\Controllers\Customers;

use App\Services\ListService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Services\PriceCalculator;
use Modules\Orders\Actions\Quotations\CreateQuotationAction;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Quotation;
use Modules\Orders\Services\OrderService;
use Modules\Users\Entities\Customer;
use Modules\Users\Http\Requests\Customers\CreateQuotationRequest;

class QuotationController extends Controller
{
  use AuthorizesRequests;

  protected $lists;
  protected $priceCalculator;
  protected $orderService;

  public function __construct(ListService $lists, PriceCalculator $priceCalculator, OrderService $orderService)
  {
    $this->authorizeResource(Order::class, 'quotation');
    $this->lists = $lists;
    $this->priceCalculator = $priceCalculator;
    $this->orderService = $orderService;
  }

  /**
   * Display a listing of the resource.
   * @param Customer $customer
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index(Customer $customer)
  {
    $this->authorize('view customer', Customer::class);

    $quotations = $customer->quotations;

    return view(
      'users::customers.quotations.index',
      compact('quotations', 'customer')
    );
  }

  /**
   * Show the form for creating a new resource.
   * @param Customer $customer
   * @return Response
   */
  public function create(Customer $customer)
  {
    $quotation = new Quotation();
    $nextQuotationNumber = 'Q' .
      request()->user()->staff->initials . $quotation->nextNumber();
    $itemList = $this->lists->itemCodeDescriptionList();
    $staffList = $this->lists->staffList();
    $defaultShippingAddress = $customer->defaultShippingAddress();

    if ($customer->requiresSerialNumber()) {
      $serialNumberPart = Item::whereCode(Item::WSN_CODE)->first();
    } else {
      $serialNumberPart = null;
    }

    return view(
      'users::customers.quotations.create',
      compact(
        'quotation',
        'customer',
        'itemList',
        'staffList',
        'nextQuotationNumber',
        'defaultShippingAddress',
        'serialNumberPart'
      )
    );
  }

  /**
   * Store a newly created resource in storage.
   * @param CreateQuotationRequest $request
   * @param CreateQuotationAction $action
   * @param Customer $customer
   * @return \Illuminate\Http\RedirectResponse
   */
  public function store(CreateQuotationRequest $request, CreateQuotationAction $action, Customer $customer)
  {
    return redirect()
      ->route('orderModule.quotations.show', $action->execute($customer))
      ->with('success', 'Customer quotation created successfully');
  }

  /**
   * Show the specified resource.
   * @param Customer $customer
   * @param Order $quotation
   * @return Response
   */
  public function show(Customer $customer, Order $quotation)
  {
    $preferenceList = $this->lists->preferenceList();

    $preferences = collect($quotation->preferences)
      ->map(function ($orderPreference) use ($preferenceList) {
        // TODO: Keep reference to the name of the preference when creating the order other than having to do this
        $orderPreference['name'] = Arr::get($preferenceList, $orderPreference['id']);

        return $orderPreference;
      });

    return view('orders::quotations.show', compact('quotation', 'customer', 'preferences'));
  }

  /**
   * Remove the specified resource from storage.
   * @param Customer $customer
   * @param Order $order
   * @return Response
   */
  public function destroy(Customer $customer, Order $order)
  {
    try {
      $order->delete();

      return redirect()
        ->route('users.customers.orders.index', $customer)
        ->with('success', 'Customer order deleted successfully');
    } catch (\Exception $e) {
      Log::debug($e->getMessage());

      return back()->with('error', 'There was a problem deleting this order');
    }
  }
}
