<?php

namespace Modules\Users\Http\Controllers\Customers;

use App\Services\ListService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Users\Actions\Customers\CreateCustomerAction;
use Modules\Users\Actions\Customers\DeleteCustomerAction;
use Modules\Users\Actions\Customers\UpdateCustomerAction;
use Modules\Users\Entities\Customer;
use Modules\Users\Http\Requests\CreateCustomerRequest;
use Modules\Users\Http\Requests\UpdateCustomerRequest;
use Modules\Users\Services\CustomerSearchService;

class CustomerController extends Controller
{
  use AuthorizesRequests;

  protected ListService $lists;

  public function __construct(ListService $lists)
  {
    $this->lists = $lists;
    $this->authorizeResource(Customer::class, 'customer');
  }

  /**
   * Display a listing of the resource.
   * @param CustomerSearchService $customerSearch
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index(CustomerSearchService $customerSearch)
  {
    $this->authorize('viewAny', Customer::class);

    return view('users::customers.index', [
      'searchFields' => $customerSearch->searchFields(),
      'searchConditions' => $customerSearch->conditions(),
      'searchParams' => request()->only(['fields', 'conditions', 'terms', 'has_special_price_items'])
    ]);
  }

  /**
   * Show the form for creating a new resource.
   * @return Response
   */
  public function create()
  {
    return view('users::customers.create', [
      'priceLevels' => $this->lists->levelList(),
      'preferenceList' => $this->lists->customerPreferenceList(),
      'customerTypeList' => $this->lists->customerTypeList(),
      'customerPaymentTermsList' => $this->lists->customerPaymentTermsList(),
      'customer' => new Customer(),
      'commissionTypeNames' => $this->lists->commissionTypeNames()
    ]);
  }

  /**
   * Store a newly created resource in storage.
   * @param CreateCustomerRequest $request
   * @param CreateCustomerAction $action
   * @return Response
   */
  public function store(CreateCustomerRequest $request, CreateCustomerAction $action)
  {
    $action->execute();

    return redirect()
      ->route('users.customers.index')
      ->with('success', 'Customer created successfully');
  }

  /**
   * Show the specified resource.
   * @param Customer $customer
   * @return Response
   */
  public function show(Customer $customer)
  {
//    $customer
//      ->loadCount('items', 'orders')
//      ->loadSum('orders', 'total_amount');

    return view('users::customers.show', [
      'customer' => $customer,
      //'customerReceivables' => $customer->pendingReceivables,
      //'totalAmountPaid' => 0//number_format($customer->receivables->where('type', 'receivable')->sum('amount_paid'), 2)
    ]);
  }

  /**
   * Show the form for editing the specified resource.
   * @param Customer $customer
   * @return Response
   */
  public function edit(Customer $customer)
  {
    $priceLevels = $this->lists->levelList();
    $preferenceList = $this->lists->customerPreferenceList($customer);
    $customerTypeList = $this->lists->customerTypeList();
    $customerAddresses = $customer->addresses;
    $accountAddress = $customerAddresses->where('type', 'account')->first();
    $billingAddress = $customerAddresses->where('type', 'billing')->first();
    $shippingAddress = $customerAddresses->where('type', 'shipping')->first();
    $commissionTypeNames = $this->lists->commissionTypeNames();
    $customerPaymentTermsList = $this->lists->customerPaymentTermsList();
    $showOtherPaymentTermsInput = false;

    if (! in_array($customer->payment_terms, $customerPaymentTermsList)) {
      $showOtherPaymentTermsInput = true;
    }

    return view('users::customers.edit', compact(
      'customer',
      'priceLevels',
      'preferenceList',
      'customerTypeList',
      'accountAddress',
      'billingAddress',
      'shippingAddress',
      'commissionTypeNames',
      'customerPaymentTermsList',
      'showOtherPaymentTermsInput'
    ));
  }

  /**
   * Update the specified resource in storage.
   * @param UpdateCustomerRequest $request
   * @param UpdateCustomerAction $updateCustomerAction
   * @param Customer $customer
   * @return \Illuminate\Http\RedirectResponse
   */
  public function update(UpdateCustomerRequest $request, UpdateCustomerAction $updateCustomerAction, Customer $customer)
  {
    try {
      return redirect()
        ->route('users.customers.show', $updateCustomerAction->execute($customer, $request))
        ->with('success', 'Customer updated successfully');
    } catch (\Exception $exception) {
      return back()->with('error', $exception->getMessage());
    }
  }

  /**
   * Remove the specified resource from storage.
   * @param Customer $customer
   * @return Response
   */
  public function destroy(DeleteCustomerAction $action, Customer $customer)
  {
    try {

      $action->execute($customer);

      return redirect()
        ->route('users.customers.index')
        ->with('success', 'Customer deleted successfully');
    } catch (\Exception $e) {
      return back()
        ->with('error', 'Could not delete the customer. Message: ' . $e->getMessage());
    }
  }
}
