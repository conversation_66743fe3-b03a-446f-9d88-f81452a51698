<?php

namespace Modules\Users\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Modules\Users\Entities\Staff;

class LockUserScreen extends Controller
{
  public function __invoke(Request $request)
  {
    $request->user()->update(['screen_locked' => true]);
    
    return response()->json([], Response::HTTP_ACCEPTED);
  }
}
