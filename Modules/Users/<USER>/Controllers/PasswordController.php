<?php

namespace Modules\Users\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Hash;
use Modules\Users\Entities\User;

class PasswordController extends Controller
{
  public function edit(User $user = null)
  {
    return view('users::passwords.edit', [
      'user' => $user ?? request()->user()
    ]);
  }

  public function update(Request $request, User $user)
  {
    $request->validate([
      'current_password' => 'nullable|string',
      'password' => 'required|string|confirmed',
    ]);

    if (
      $user->id === auth()->id() &&
      !Hash::check($request->current_password, $request->user()->password)
    ) {
      return back()->with('error', 'Credentials do no match');
    }

    $user->update([
      'password' => Hash::make($request->password)
    ]);

    // Todo: Send user a notification about the change of their password

    return redirect()->route('users.staff.show', $user)
      ->with('success', 'Password updated successfully');
  }
}
