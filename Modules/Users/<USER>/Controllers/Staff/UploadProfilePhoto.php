<?php

namespace Modules\Users\Http\Controllers\Staff;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Modules\Users\Entities\Staff;

class UploadProfilePhoto extends Controller
{
  public function __invoke(Request $request, Staff $staff)
  {
    $request->validate([
      'profile_photo' => 'required|mimes:jpeg,jpg,png',
    ]);

    $filename = 'profile_photo.' . $request->file('profile_photo')->getClientOriginalExtension();

    $staff->user->addMediaFromRequest('profile_photo')
      ->usingFileName($filename)
      ->toMediaCollection();

    Cache::forget('staff');

    return back()
      ->with('success', 'Profile photo changed successfully');
  }
}
