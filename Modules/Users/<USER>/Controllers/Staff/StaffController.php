<?php

namespace Modules\Users\Http\Controllers\Staff;

use App\Services\ListService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Modules\Users\Emails\Staff\StaffAccountCreated;
use Modules\Users\Entities\Staff;
use Modules\Users\Entities\User;

class StaffController extends Controller
{
  use AuthorizesRequests;

  protected $lists;

  public function __construct(ListService $lists)
  {
    $this->authorizeResource(Staff::class, 'staff');
    $this->lists = $lists;
  }

  /**
   * Display a listing of the resource.
   * @param bool $active
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index($active = true)
  {
    $active = (bool)request()->active;

    $this->authorize('viewAny', Staff::class);

    return view('users::staff.index', [
      'staffMembers' => User::staffMember()
        ->has('staff')
        ->with('staff.department')
        ->whereActive($active)
        ->get()
    ]);
  }

  /**
   * Show the form for creating a new resource.
   * @return Response
   */
  public function create()
  {
    $roleList = $this->lists->roleList();
    $departmentList = $this->lists->departmentList();
    $itemList = $this->lists->itemList();

    return view('users::staff.create', compact('roleList', 'departmentList', 'itemList'));
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @return Response
   */
  public function store(Request $request)
  {
    $request->validate([
      'name' => 'required|string|max:255',
      'email' => 'required|email|unique:users,email',
      'initials' => 'required|string|between:1,2',
      'department_id' => 'nullable|exists:departments,id',
      'roles' => 'nullable|array',
      'commission_percentage' => 'nullable|numeric',
      'commission_items' => 'nullable|required_with:commission_percentage|array'
    ]);

    $record = $request->all();
    $record['password'] = Hash::make('pacific');
    $record['type'] = User::STAFF;

    if ($request->has('commission_percentage')) {
      $options = [];
      $options['commission'] = [
        'percentage' => $request->commission_percentage,
        'items' => $request->commission_items
      ];
      $record['options'] = $options;
    }
    // Create the staff member
    $user = User::create($record);
    $user->staff()->create($record);

    if ($request->filled('roles')) {
      $user->assignRole($request->roles);
    }

    Mail::to($user)->send(new StaffAccountCreated($user->staff));

    Cache::forget('staff');

    return redirect()
      ->route('users.staff.index', ['active' => true])
      ->with('success', 'Staff member created successfully');
  }

  /**
   * Show the specified resource.
   * @param Staff $staff
   * @return Response
   */
  public function show(Staff $staff)
  {
    $activities = $staff->user->activities()->latest()->take(5)->get();
    $orders = $staff->orders()->with('items')->get()->take(5);

    return view('users::staff.show', compact('staff', 'activities', 'orders'));
  }

  /**
   * Show the form for editing the specified resource.
   * @param Staff $staff
   * @return Response
   */
  public function edit(Staff $staff)
  {
    $roleList = $this->lists->roleList();
    $departmentList = $this->lists->departmentList();
    $itemList = $this->lists->itemList();
    $userRoleIds = $staff->user->roles()->orderBy('name')->pluck('id');

    return view(
      'users::staff.edit',
      compact('staff', 'roleList', 'userRoleIds', 'departmentList', 'itemList')
    );
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param Staff $staff
   * @return Response
   */
  public function update(Request $request, Staff $staff)
  {
    $request->validate([
      'name' => 'required|string|max:255',
      'email' => 'required|email',
      'initials' => 'required|string|between:1,2',
      'department_id' => 'nullable|exists:departments,id',
      'roles' => 'nullable|array',
      'commission_percentage' => 'nullable|numeric',
      'commission_items' => 'nullable|array'
    ]);

    $record = $request->all();

    if ($request->has('commission_percentage') && $request->commission_percentage != 0) {
      $options = $staff->user->options;
      $options['commission'] = [
        'percentage' => $request->commission_percentage,
        'items' => $request->commission_items
      ];
      $record['options'] = $options;
    }
    // Update the staff member
    $staff->user->update($record);
    $staff->update($record); // The fillable property on Staff specifies which fields can be assigned

    if ($request->filled('roles')) {
      $staff->user->syncRoles($request->roles);
    } else {
      $staff->user->roles()->detach();
    }

    Cache::forget('staff');

    return redirect()
      ->route('users.staff.show', $staff)
      ->with('success', 'Staff member updated successfully');
  }

  /**
   * Remove the specified resource from storage.
   * @param Staff $staff
   * @return Response
   */
  public function destroy(Staff $staff)
  {
    try {
      $staff->user->active = false;
      $staff->user->save();

      return redirect()
        ->route('users.staff.index')
        ->with('success', 'Staff member deactivated successfully');
    } catch (\Exception $e) {
      return back()
        ->with('error', 'Could not deactivate the staff member. Message: ' . $e->getMessage());
    }
  }
}
