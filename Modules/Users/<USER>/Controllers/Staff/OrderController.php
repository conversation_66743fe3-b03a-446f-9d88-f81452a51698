<?php

namespace Modules\Users\Http\Controllers\Staff;

use App\Services\ListService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Modules\Inventory\Services\PriceCalculator;
use Modules\Orders\Entities\Order;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\Staff;
use Modules\Users\Http\Requests\Customers\CreateOrderRequest;

class OrderController extends Controller
{
  use AuthorizesRequests;

  protected $lists;
  protected $priceCalculator;

  public function __construct(ListService $lists, PriceCalculator $priceCalculator)
  {
    $this->authorizeResource(Order::class, 'order');
    $this->lists = $lists;
    $this->priceCalculator = $priceCalculator;
  }

  /**
   * Display a listing of the resource.
   * @param Customer $customer
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index(Staff $staff)
  {
    $this->authorize('viewAny', Order::class);

    $orders = $staff->orders()->with('customer.user')->get();

    return view(
      'users::staff.orders.index',
      compact('orders', 'staff')
    );
  }

  /**
   * Show the form for creating a new resource.
   * @param Customer $customer
   * @return Response
   */
  public function create(Customer $customer)
  {
    $itemList = $this->lists->itemDescriptionList();
    $shippingMethods = $this->lists->shippingMethodList();
    $preferenceList = $this->lists->preferenceList($customer);

    return view(
      'users::customers.orders.create',
      compact(
        'customer',
        'shippingMethods',
        'itemList',
        'preferenceList'
      )
    );
  }

  /**
   * Store a newly created resource in storage.
   * @param CreateOrderRequest $request
   * @param Customer $customer
   * @return \Illuminate\Http\RedirectResponse
   */
  public function store(CreateOrderRequest $request, Customer $customer)
  {
    return back()
      ->with('error', 'Could not create an order here. Go to customer profile to create an order.');
  }

  /**
   * Show the specified resource.
   * @param Customer $customer
   * @param Order $order
   * @return Response
   */
  public function show(Customer $customer, Order $order)
  {
    $preferenceList = $this->lists->preferenceList();

    $preferences = collect($order->preferences)
      ->map(function ($orderPreference) use ($preferenceList) {
        // TODO: Keep reference to the name of the preference when creating the order other than having to do this
        $orderPreference['name'] = Arr::get($preferenceList, $orderPreference['id']);

        return $orderPreference;
      });

    return view('users::customers.orders.show', compact('order', 'customer', 'preferences'));
  }

  /**
   * Show the form for editing the specified resource.
   * @param Customer $customer
   * @param Order $order
   * @return Response
   */
  public function edit(Customer $customer, Order $order)
  {
    return view('orders::customers.orders.edit', compact('customer', 'order'));
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param int $id
   * @return Response
   */
  public function update(Request $request, $id)
  {
    //
  }

  /**
   * Remove the specified resource from storage.
   * @param Order $order
   * @return Response
   */
  public function destroy(Customer $customer, Order $order)
  {
    try {
      $order->delete();

      return redirect()
        ->route('users.customers.orders.index', $customer)
        ->with('success', 'Customer order deleted successfully');
    } catch (\Exception $e) {
      Log::debug($e->getMessage());

      return back()->with('error', 'There was a problem deleting this order');
    }
  }

  protected function getOptions($request)
  {
    $billingAddress = [
      'state' => $request->billing_state,
      'city' => $request->billing_city,
      'zip' => $request->billing_zip,
      'unit' => $request->billing_unit
    ];

    $shippingAddress = [
      'state' => $request->shipping_state,
      'city' => $request->shipping_city,
      'zip' => $request->shipping_zip,
      'unit' => $request->shipping_unit
    ];
    $preferenceList = $this->lists->preferenceList();
    $preferences = collect($request->preference_id)
      ->reject(function ($id, $key) use ($request) {
        return empty($request->preference_value[$key]);
      })
      ->map(function ($id, $key) use ($request, $preferenceList) {
        return [
          'id' => $id,
          'name' => Arr::get($preferenceList, $id),
          'value' => $request->preference_value[$key]
        ];
      })->all();

    return [
      'billing' => $billingAddress,
      'shipping' => $shippingAddress,
      'shipping_method' => $request->shipping_method,
      'preferences' => $preferences,
      'statuses' => [
        [
          'name' => 'created',
          'created_at' => now()->toDateTimeString(),
          'created_by' => $request->user()->id,
          'previous' => null,
        ]
      ]
    ];
  }

  protected function getItems()
  {
    return collect(request('item_id'))
      ->mapWithKeys(function ($id, $key) {
        return [
          $id => [
            'quantity' => request('quantity')[$key],
            'price' => request('price')[$key],
            'cost' => request('cost')[$key]
          ]
        ];
      })
      ->all();
  }
}
