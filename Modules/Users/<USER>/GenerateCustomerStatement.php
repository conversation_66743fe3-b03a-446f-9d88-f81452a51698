<?php

namespace Modules\Users\Jobs;

use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\Users\Entities\Customer;
use Modules\Users\Services\StatementService;

class GenerateCustomerStatement implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

  protected $customer;
  protected $statementType;
  protected $path;

  /**
   * Create a new job instance.
   *
   * @param Customer $customer
   * @param string $statementType
   */
  public function __construct(Customer $customer, string $statementType)
  {
    $this->customer = $customer;
    $this->statementType = $statementType;
  }

  /**
   * Execute the job.
   *
   * @return void
   */
  public function handle()
  {
    $statementGenerator = new StatementService($this->customer);
    $view = $statementGenerator->pdfView($this->statementType);
    $records = $statementGenerator->records($this->statementType);
    $pdfPaper = $this->statementType !== 'payment-log' ? 'portrait' : 'landscape';

    $pdf = PDF::loadView($view, [
      'customer' => $this->customer,
      'records' => $records
    ])
      ->setPaper('a4', $pdfPaper)
      ->setOptions([
        'margin-bottom' => '0mm',
        'margin-right' => '0mm',
        'margin-top' => '0mm',
        'margin-left' => '0mm',
        'images' => true
      ]);

    $pdf->save($this->filename());
  }

  protected function filename()
  {
    return storage_path('app/statements/customers') . '/' . $this->customer->account_number . '.pdf';
  }
}
