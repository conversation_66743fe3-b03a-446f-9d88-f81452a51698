<?php

namespace Modules\Users\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateCustomerRequest extends FormRequest
{
  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'name' => 'required|string|max:255',
      'email' => 'nullable|email',
      'taxable' => 'nullable|boolean',
      'tax_exemption' => 'nullable|required_without:taxable',
      'tax_certificate' => 'nullable|required_without:taxable|file|mimes:pdf|max:10240',
      'price_level' => 'nullable|string|max:50',
      'payment_terms' => 'nullable|string|max:255',
      'account_address_zip' => 'nullable|string|max:25',
      'account_address_unit' => 'nullable|string|max:255',
      'billing_address_zip' => 'nullable|string|max:25',
      'billing_address_unit' => 'nullable|string|max:255',
      'shipping_address_zip' => 'nullable|string|max:25',
      'shipping_address_unit' => 'nullable|string|max:255',
      'contact_name' => 'nullable|array',
      'contact_email' => 'nullable|array',
      'contact_phone' => 'nullable|array',
      'contact_other' => 'nullable|array',
      'preference_id' => 'nullable|array',
      'preference_id.*' => 'nullable|exists:preferences,id',
      'preference_value' => 'nullable|array',
      'preference_value.*' => 'nullable|required_with:preference_id.*',
      'preference' => 'nullable|string|max:10000',
      'building_preference' => 'nullable|string|max:10000',
      'credit_limit' => 'nullable|numeric|min:0',
      'use_mail' => 'nullable|boolean',
      'alert_email' => 'nullable|string|max:255',
      'commission_type' => 'nullable|string|max:25'
    ];
  }

  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }
}
