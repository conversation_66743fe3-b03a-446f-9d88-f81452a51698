<?php

namespace Modules\Users\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Modules\Orders\Entities\Commission;
use Modules\Orders\Entities\Order;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Permission\Traits\HasRoles;

class Staff extends Model implements Auditable
{
  use HasRoles,
    \OwenIt\Auditing\Auditable,
    SoftDeletes;

  protected $primaryKey = 'user_id';

  protected $fillable = [
    'department_id',
    'supervisor_id',
    'user_id',
    'initials',
    'active'
  ];

  public function user()
  {
    return $this->hasOne(User::class, 'id', 'user_id');
  }

  public function department()
  {
    return $this->belongsTo(Department::class, 'department_id');
  }

  public function orders()
  {
    return $this->hasMany(Order::class, 'created_by')->latest();
  }

  public function commissions()
  {
    return $this->morphMany(Commission::class, 'commissionable');
  }

  public function getNameAttribute()
  {
    return $this->user->name;
  }

  public function getActiveAttribute()
  {
    return $this->user->active;
  }

  public function getOption($key, $default = null)
  {
    $options = $this->options ? $this->options : $this->user->options;

    return Arr::get($options, $key, $default);
  }

  public function getCommissionPercentageAttribute()
  {
    return $this->getOption('commission.percentage', 0.00);
  }

  public function getCommissionItemsAttribute()
  {
    return $this->getOption('commission.items', []) ?? [];
  }

  public function getCommissionItemListAttribute()
  {
    return implode(', ', $this->commission_items);
  }

  public function hasCommissionItem($code)
  {
    return in_array($code, $this->commission_items);
  }

  public function getProfilePhotoAttribute()
  {
    return $this->user->profile_photo;
  }

  public function getInitialsAttribute($value)
  {
    if (! empty($value)) {
      return $value;
    }

    $names = explode(' ', $this->name);

    if (count($names) === 1) {
      return substr($names[0], 0, 1);
    }

    return substr($names[0], 0, 1) . substr($names[1], 0, 1);
  }

}
