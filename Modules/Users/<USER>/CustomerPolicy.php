<?php

namespace Modules\Users\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\User;

class CustomerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any customers.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->can('view customer');
    }

    /**
     * Determine whether the user can view the customer.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function view(User $user, Customer $customer)
    {
        return $user->can('view customer') || $user->id === $customer->user_id;
    }

    /**
     * Determine whether the user can create customers.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->can('create customer');
    }

    /**
     * Determine whether the user can update the customer.
     *
     * @param \Modules\Users\Entities\User $user
     * @param Customer $customer
     * @return mixed
     */
    public function update(User $user, Customer $customer)
    {
        return $user->hasAnyPermission($user->updateCustomerPermissions) || $user->id === $customer->user_id;
    }

    /**
     * Determine whether the user can delete the customer.
     *
     * @param \Modules\Users\Entities\User $user
     * @param Customer $customer
     * @return mixed
     */
    public function delete(User $user, Customer $customer)
    {
        return $user->can('delete customer') || $user->id === $customer->user_id;
    }

    /**
     * Determine whether the user can restore the customer.
     *
     * @param \Modules\Users\Entities\User $user
     * @param Customer $customer
     * @return mixed
     */
    public function restore(User $user)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the customer.
     *
     * @param \Modules\Users\Entities\User $user
     * @param Customer $customer
     * @return mixed
     */
    public function forceDelete(User $user)
    {
        return $user->isAdmin();
    }
}
