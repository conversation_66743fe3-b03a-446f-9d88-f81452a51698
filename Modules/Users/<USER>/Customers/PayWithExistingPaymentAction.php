<?php


namespace Modules\Users\Actions\Customers;


use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Users\Entities\Customer;
use Money\Money;

class PayWithExistingPaymentAction
{
  public function execute(Customer $customer, Collection $invoicesToPay)
  {
    $existingPayment = $customer->payments()
      ->where('id', Str::after(request('payment_method'), 'payment_'))
      ->first();

    $invoiceTotal = Money::USD(str_money($invoicesToPay->sum('amount')));
    $existingPaymentBalance = Money::USD(str_money($existingPayment->balance)); // negative e.g -10099
    $existingPayment->balance = $existingPaymentBalance->add($invoiceTotal)->getAmount() / 100;
    $existingPayment->save();

    $newInvoicesToPay = $invoicesToPay->filter(function ($record, $invoiceId) use ($existingPayment) {

      $orderPayment = DB::table('order_payment')
        ->where('payment_id', $existingPayment->id)
        ->where('order_id', $invoiceId)
        ->first();

      if (empty($orderPayment)) {
        return true;
      }

      $amount = Money::USD(str_money($orderPayment->amount))
          ->add(Money::USD(str_money($record['amount'])))
          ->getAmount() / 100;

      DB::table('order_payment')
        ->where('payment_id', $existingPayment->id)
        ->where('order_id', $invoiceId)
        ->update(['amount' => $amount]);

      return false;
    })->all();

    if (! empty($newInvoicesToPay)) {
      $existingPayment->invoices()->attach($newInvoicesToPay);
    }

    return $existingPayment;
  }
}
