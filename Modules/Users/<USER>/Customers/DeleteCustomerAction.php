<?php


namespace Modules\Users\Actions\Customers;

use Modules\Users\Entities\Customer;

class DeleteCustomerAction
{
  /**
   * @param Customer $customer
   * @return boolean
   */
  public function execute(Customer $customer): bool
  {
    if (!$customer->orders->isEmpty()) {
      $customer->orders()->delete();
    }

    $customer->user()->delete();
    $customer->delete();

    return true;
  }
}
