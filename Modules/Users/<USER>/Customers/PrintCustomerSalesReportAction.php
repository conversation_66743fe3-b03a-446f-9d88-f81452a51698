<?php

namespace Modules\Users\Actions\Customers;

use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Modules\Users\Entities\Customer;

class PrintCustomerSalesReportAction
{
  protected string $view = 'users::customers.reports.sales.print';

  /**
   * @var string
   */
  public string $filename;

  public function execute(Customer $customer, array $details)
  {
    $customer->load(['user', 'invoicedOrders.createdBy.staff']);
    $invoices = $this->filteredInvoices($customer, $details);
    $startDate = Carbon::parse($details['date_from'])->format('m/d/y');
    $endDate = Carbon::parse($details['date_to'])->format('m/d/y');

    $pdf = app()->make('snappy.pdf.wrapper')
      ->setPaper('a4', 'landscape')
      ->setOptions([
        'header-right' => 'Page [page] of [topage]',
        'header-font-size' => '8'
      ])
      ->loadView($this->view, compact('customer', 'invoices', 'startDate', 'endDate'));

    return $pdf->inline('customer-sales-report-' . $customer->account_number . '.pdf');
  }

  protected function filteredInvoices(Customer $customer, array $details)
  {
    if (! Arr::has($details, ['date_from', 'date_to'])) {
      return $customer->invoicedOrders();
    }

    return $customer->invoicedOrders()
      ->whereBetween('invoiced_at', [
        $details['date_from'],
        Carbon::parse($details['date_to'])->endOfDay()
      ])
      ->get();
  }
}
