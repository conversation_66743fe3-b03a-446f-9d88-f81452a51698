<?php


namespace Modules\Users\Actions\Customers;


use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Users\Entities\Customer;
use Modules\Users\Services\CustomerSearchService;

class ExportCustomersAction
{
  protected $searchService;

  public function __construct(CustomerSearchService $service)
  {
    $this->searchService = $service;
  }

  public function execute(array $details, string $filepath = null): ?string
  {
    $results = $this->searchService->find();

    if (request()->has('search')) {
      $results = $results->filter(function ($record) {
        return ! empty(collect($record)->first(function ($value, $key) use (&$hasTerm) {
          if ($key === 'customer_contacts') {
            return false;
          }
          return Str::contains(strtolower($value), strtolower(request('search')));
        }));
      });
    }

    if  (is_null($filepath)) {
      $filepath = storage_path('app/exports/' . now()->timestamp . 'customers.csv');
    }

    $fileHandle = fopen($filepath, 'x');
    fputcsv($fileHandle, ['account_number', 'name', 'alert_email', 'email_invoices']);

    $customers = Customer::leftJoin('users', 'users.id', '=', 'customers.user_id')
      ->selectRaw('customers.account_number, users.name, customers.alert_email, customers.options->>"$.preferences[7].value" as email_invoices')
      ->whereIn('customers.account_number', $results->pluck('account_number')->all())
      ->get()
      ->map(function (Customer $customer) {
        return collect($customer->getAttributes())->transform(function ($value, $key) {
          if ($value === "null") {
            return '';
          }

          return $value;
        })->all();
      })
      ->sortBy('name')
      ->all();

    foreach($customers as $customer) {
      fputcsv($fileHandle, $customer);
    }

    fclose($fileHandle);

    return $filepath;
  }
}
