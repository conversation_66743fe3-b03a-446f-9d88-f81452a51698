<?php

namespace Modules\Users\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Users\Entities\User;

class OrderPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any order.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->can('view order');
    }

    /**
     * Determine whether the user can view the order.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function view(User $user)
    {
        return $user->can('view order');
    }

    /**
     * Determine whether the user can create orders.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->can('create order');
    }

    /**
     * Determine whether the user can update the order.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function update(User $user)
    {
        return $user->can('update order');
    }

    /**
     * Determine whether the user can delete the order.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function delete(User $user)
    {
        return $user->can('delete order');
    }

    /**
     * Determine whether the user can reorder the order.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function reorder(User $user)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the order.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function forceDelete(User $user)
    {
        return $user->isAdmin();
    }
}
