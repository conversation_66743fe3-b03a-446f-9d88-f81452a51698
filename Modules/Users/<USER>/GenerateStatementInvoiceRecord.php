<?php


namespace Modules\Users\Services;


use Modules\Orders\Entities\Order;

class GenerateStatementInvoiceRecord
{
  protected $order;

  public function __construct(Order $order)
  {
    $this->order = $order;
  }

  public function make()
  {
    $record = [
      'timestamp' => $this->order->created_at->timestamp, // Need this to sort properly
      'invoiced_at' => optional($this->order->invoiced_at)->format('m/d/y'),
      'raw_invoiced_at' => $this->order->invoiced_at,
      'created_at' => $this->order->created_at->format('m/d/y'),
      'item' => 'INV',
      'reference_number' => $this->order->number,
      'payment' => null,
      'purchase_order_number' => $this->order->purchase_order_number,
      'due_date' => $this->order->due_date,
      'invoice_total' => $this->order->total_amount,
      'invoice_balance' => $this->order->balance
    ];

    if ($this->order->pastDueDate()) {
      $record['past_due'] = $this->order->balance;
      $record['current'] = null;
    } else {
      $record['past_due'] = null;
      $record['current'] = $this->order->balance;
    }


    return $record;
  }
}
