<?php

namespace Modules\Users\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\Staff;
use Modules\Users\Entities\User;

class StaffPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any staff.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->can('view staff');
    }

    /**
     * Determine whether the user can view the staff.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     * @throws \Exception
     */
    public function view(User $user, Staff $staff)
    {
        return $user->can('view staff') || $user->id === $staff->user_id;
    }

    /**
     * Determine whether the user can create staffs.
     *
     * @param  \Modules\Users\Entities\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->can('create staff');
    }

    /**
     * Determine whether the user can update the staff.
     *
     * @param \Modules\Users\Entities\User $user
     * @param Staff $staff
     * @return mixed
     */
    public function update(User $user, Staff $staff)
    {
        return $user->can('update staff') || $user->id === $staff->user_id;
    }

    /**
     * Determine whether the user can delete the staff.
     *
     * @param \Modules\Users\Entities\User $user
     * @param Staff $staff
     * @return mixed
     */
    public function delete(User $user, Staff $staff)
    {
        return $user->can('delete staff') || $user->id === $staff->user_id;
    }

    /**
     * Determine whether the user can restore the staff.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function restore(User $user)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the staff.
     *
     * @param \Modules\Users\Entities\User $user
     * @return mixed
     */
    public function forceDelete(User $user)
    {
        return $user->isAdmin();
    }
}
