<?php

namespace Modules\Users\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class DebitTransfer extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $guarded = [];

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'user_id', 'customer_id');
    }

    public function recipient()
    {
        return $this->belongsTo(Customer::class, 'user_id', 'recipient_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(Staff::class, 'user_id', 'transferred_by');
    }
}
