<?php

namespace Modules\Users\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\File;
use Modules\Users\Entities\Customer;

class GenerateCustomerStatements //implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

  protected $statementType;

  public $tries = 1;

  /**
   * Create a new job instance.
   *
   * @param string $statementType
   */
  public function __construct(string $statementType)
  {
    $this->statementType = $statementType;
  }

  /**
   * Execute the job.
   *
   * @return void
   */
  public function handle()
  {
    Customer::selectRaw('
        distinct customers.user_id,
        customers.account_number, customers.options')
      ->whereRaw('customers.options->>"$.preferences[12].value" = "Yes" AND customers.options->>"$.preferences[7].value" = "null"')
      ->join('orders', 'orders.customer_id', '=', 'customers.user_id')
      ->where('orders.invoiced', true)
      ->where('orders.paid', false)
      ->get()
      ->each(function (Customer $customer) {
        GenerateCustomerStatement::dispatch($customer->fresh(), $this->statementType);
      });

    ZipCustomerStatements::dispatch();
  }
}
