<?php

namespace Modules\Users\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Mail;
use Modules\Users\Actions\SendStatementAction;
use Modules\Users\Emails\Statements\CustomerStatement;
use Modules\Users\Entities\Customer;

class EmailCustomerStatements implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

  protected string $body;

  /**
   * Create a new job instance.
   *
   * @return void
   */
  public function __construct(string $body = '')
  {
    $this->body = $body;
  }

  /**
   * Execute the job.
   *
   * @return void
   */
  public function handle()
  {
    Customer::selectRaw('
        distinct customers.user_id,
        customers.options
      ')
      ->whereRaw('customers.options->>"$.preferences[12].value" = "Yes" AND customers.options->>"$.preferences[7].value" <> "null"')
      ->join('orders', 'orders.customer_id', '=', 'customers.user_id')
      ->where('orders.invoiced', true)
      ->where('orders.paid', false)
      ->get()
      ->filter(function ($customer) {
        return money_greater($customer->actual_account_balance, 0);
      })
      ->each(function (Customer $customer, $index) {
        app(SendStatementAction::class)->execute($customer, $this->body);
      });
  }
}
