<?php

use Faker\Generator as Faker;
use Modules\Users\Entities\Role;

$factory->define(Role::class, function (Faker $faker) {
    return [
        'name' => $faker->randomElement(['admin', 'staff', 'customer', 'provider'])
    ];
});

$factory->state(Role::class, 'admin', [
    'name' => 'admin'
]);

$factory->state(Role::class, 'staff', [
    'name' => 'staff'
]);

$factory->state(Role::class, 'customer', [
    'name' => 'customer'
]);
