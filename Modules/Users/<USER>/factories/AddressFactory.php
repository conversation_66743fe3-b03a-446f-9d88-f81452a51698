<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Modules\Users\Entities\Address;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(Address::class, function (Faker $faker) {
  return [
    'name' => $faker->name(),
    'unit' => $faker->streetAddress(),
    'zip' => $faker->postcode(),
    'type' => Address::TYPE_ACCOUNT,
    'state' => $faker->stateAbbr(),
    'city' => $faker->city()
  ];
});

$factory->state(Address::class, 'billing', ['type' => Address::TYPE_BILLING]);
$factory->state(Address::class, 'shipping', ['type' => Address::TYPE_SHIPPING]);

