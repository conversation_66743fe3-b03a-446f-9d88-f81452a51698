<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Modules\Orders\Entities\Commission;
use Modules\Users\Entities\Customer;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(Customer::class, function (Faker $faker) {
  return [
    'account_number' => $faker->unique()->randomNumber(5, true),
    'taxable' => 1,
    'price_level' => $faker->randomElement(['2300', '2302', '2304', '2306', '2308', '2310', '2312', '2314', '2316', '2318', '2320', '2350']),
    'type' => $faker->randomElement(['Ex-Rental', 'Inactive Customer', 'Receivable', 'Rental']),
    'payment_terms' => $faker->randomElement(['Cash Customer', 'C.O.D', 'Due Upon Receipt', 'Net 1 Day', 'Net 15 Days', 'Net 30 Days', 'Net 30 Days with CC', 'Other']),
  ];
});

$factory->state(Customer::class, 'asFormRequest', function (Faker $faker) {
  return [
    'account_address_zip' => $faker->postcode(),
    'account_address_unit' => $faker->streetAddress(),
    'billing_address_zip' => $faker->postcode(),
    'billing_address_unit' => $faker->streetAddress(),
    'shipping_address_zip' => $faker->postcode(),
    'shipping_address_unit' => $faker->streetAddress(),
    'commission_type' => $faker->randomElement([Commission::TYPE_RO, Commission::TYPE_WES, Commission::TYPE_WS]),
    'contact_name' => [$faker->name()],
    'contact_email' => [$faker->safeEmail()],
    'contact_phone' => [$faker->phoneNumber()],
    'contact_other' => [$faker->word()],
  ];
});
