<?php

namespace Modules\Users\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Representative extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, SoftDeletes;

    const ROLE_PICKUP = 'pickup';
    const ROLE_ORDER = 'order';

    protected $fillable = [
        'authorized',
        'customer_id',
        'name',
        'role'
    ];

    protected $casts = [
        'authorized' => 'boolean'
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function scopeAuthorized($query)
    {
        return $query->whereAuthorized(true);
    }
}
