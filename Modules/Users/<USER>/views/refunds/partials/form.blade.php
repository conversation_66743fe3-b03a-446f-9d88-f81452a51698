<div class="form-group row">
  <div class="col-md-5">
    <label for="account-number">Cust Code</label>
    <input class="form-control" id="account-number" value="{{ $customer->account_number }}" disabled>
  </div>
  <div class="col-md-7">
    <label for="customer_id">Refund to</label>
    <input class="form-control" id="customer_id" value="{{ $customer->name }}" disabled>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-4">
    <label for="invoice-id">Invoices to Refund</label>
    <select
      class="form-control"
      type="text"
      name="invoice_id"
      id="invoice-id">
      <option value="">Select Invoice to refund</option>
      @foreach($refundableInvoices as $invoiceDetails)
        <option
          value="{{ $invoiceDetails['id'] }}"
          data-amount="{{ abs($invoiceDetails['amount']) }}">
          {{ $invoiceDetails['description'] }}
        </option>
      @endforeach
    </select>
  </div>
  <div class="col-md-4">
    <label for="payment-method">Method</label>
    <select class="form-control" type="text" name="method" id="payment-method">
      <option value="">Select Payment Method</option>
      @foreach($paymentMethodList as $key => $paymentMethod)
        <option
          value="{{ $key }}" {{ old('method', $payment->method ?? 'cash') == $key ? 'selected' : '' }}>{{ $paymentMethod }}</option>
      @endforeach
    </select>
  </div>
  <div class="col-md-4">
    <label for="refund-amount">Amount</label>
    <input type="number"
           class="form-control"
           id="refund-amount"
           name="amount"
           step="0.01"
           value="{{ old('amount', number_format($refund->amount, 2, '.', '')) }}"
           required>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-4">
    <label for="payment-check-date">Check Date</label>
    <input class="form-control @error('check_date') is-invalid @enderror"
           type="date"
           name="check_date"
           id="payment-check-date"
           value="{{ old('check_date', $refund->check_date) }}">
  </div>
  <div class="col-md-4">
    <label for="payment-check-name">Check Name</label>
    <input class="form-control @error('check_name') is-invalid @enderror"
           type="text"
           name="check_name"
           id="payment-check-name"
           value="{{ old('check_name', $refund->check_name ?? $customer->name ) }}">
  </div>
  <div class="col-md-4">
    <label for="payment-bank-id">Bank ID</label>
    <input class="form-control @error('bank_id') is-invalid @enderror"
           type="text"
           name="bank_id"
           id="payment-bank-id"
           value="{{ old('bank_id', $refund->bank_id) }}">
  </div>
</div>
<div class="form-group row">
  <div class="col-md-4">
    <label for="payment-reference">Check No. or Last 4 Digits of Credit Card</label>
    <input class="form-control @error('reference') is-invalid @enderror"
           type="text"
           name="reference"
           id="payment-reference"
           value="{{ old('reference', $refund->reference) }}">
  </div>
  <div class="col-md-4">
    <label for="payment-card-brand">Card Brand</label>
    <select class="form-control @error('card_brand') is-invalid @enderror" type="text" name="card_brand"
            id="payment-card-brand">
      <option value="">Select Card Brand</option>
      @foreach($cardBrandList as $cardBrand)
        <option
          value="{{ $cardBrand }}"
          {{ old('card_brand', $refund->card_brand) === $cardBrand ? 'selected' : '' }}>
          {{ $cardBrand }}
        </option>
      @endforeach
    </select>
  </div>

</div>
<div class="form-group mb-4">
  <label for="payment-notes">Notes</label>
  <textarea
    name="notes"
    id="payment-notes"
    rows="5"
    class="form-control">{{ old('notes', $refund->reason) }}</textarea>
</div>

@push('js')
  <script>
    (function () {
      let invoiceIdElement = $('#invoice-id');
      let invoiceAmountElement = $('#refund-amount');

      $('body').on('change', '#invoice-id', function () {
        invoiceAmountElement.val($(this).find(':selected').data('amount'));
      })
    }())
  </script>
@endpush

