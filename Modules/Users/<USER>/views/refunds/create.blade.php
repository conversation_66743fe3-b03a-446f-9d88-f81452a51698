@extends('layouts.master')

@section('title', 'Create Refund')

@section('content')
  <!-- Start Content-->
  <div class="container">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
              <li class="breadcrumb-item"><a
                  href="{{ route('users.customers.show', $customer) }}">{{ $customer->name }}</a></li>
              <li class="breadcrumb-item active">Create Refund</li>
            </ol>
          </div>
          <h4 class="page-title">Create Refund</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-sm-12">
        <div class="card">
          <div class="card-body px-4 py-3">

            <form action="{{ route('users.refunds.store', $customer) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf
              @include('users::refunds.partials.form', [
                  'refund' => (new \Modules\Users\Entities\Refund)
              ])

              <div class="form-group mt-3 d-flex justify-content-end mb-4">
                <a class="btn btn-light mr-2"
                   href="{{ route('users.customers.show', $customer) }}">
                  <i class="mdi mdi-cancel mr-1"></i> Cancel</a>
                <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->
  </div>
  <!-- container -->
@endsection
