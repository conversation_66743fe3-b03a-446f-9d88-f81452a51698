@extends('layouts.master')

@section('title', 'Edit Payment #' . $payment->number)

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>

                            <li class="breadcrumb-item active">Edit Payment #{{ $payment->number }}</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Payment</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-body px-4 py-3">

                        <form action=""
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')
                            @include('users::customers.payments.partials.form', [
                                'invoices' => $payment->invoices
                            ])

                            <div class="form-group mt-3  mb-4">
                                <div class="d-flex justify-content-end">
                                    <a class="btn btn-light mr-2"
                                       href="{{ route('users.customers.show', $customer) }}">
                                        <i class="mdi mdi-arrow-left mr-1"></i> Cancel</a>
                                    @if (! $payment->delete_requested)
                                        <button type="button" class="btn btn-danger px-3" data-toggle="modal" data-target="#deletePayment"><i class="mdi mdi-check mr-1"></i> Delete</button>
                                    @else
                                        <span class="btn btn-outline-secondary">
                                        <i class="mdi mdi-cancel"></i>
                                        Delete Requested
                                    </span>
                                    @endif
                                </div>
                                @if ($payment->delete_requested)
                                    <p class="text-right text-dark font-weight-bold mt-1">Deletion of this payment has been requested by {{ $payment->deleteRequestedBy->name }}</p>
                                @endif
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>

    <div id="deletePayment" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content modal-filled bg-danger">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="dripicons-wrong h1"></i>
                        <h4 class="mt-2">
                            Request Deletion!
                        </h4>
                        <p class="mt-3">Once approved, this will delete the payment and all related payment information from the system.</p>
                        <form action="{{ route('users.customers.payments.update', ['customer' => $customer, 'payment' => $payment]) }}" method="post">
                            @csrf
                            @method('put')
                            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-light my-2">Confirm!</button>
                        </form>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>

    <!-- container -->
@endsection
