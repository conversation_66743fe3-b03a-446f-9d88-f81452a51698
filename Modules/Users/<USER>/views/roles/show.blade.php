@extends('layouts.master')

@section('title', $role->name)

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.roles.index') }}">Roles</a></li>
                            <li class="breadcrumb-item active">Role Details</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Role Details</h4>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-7 col-md-9 col-sm-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-9">
                                <h4 class="header-title mb-3">Role: {{ $role->name }}</h4>
                            </div>
                            <div class="col-sm-3">
                                @canany(['update role', 'delete role'])
                                <div class="card-widgets">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="mdi mdi mdi-dots-vertical"></i> Actions
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(285px, 37px, 0px);">
                                            @can('update role')
                                            <a class="dropdown-item" href="{{ route('users.roles.edit', $role) }}">Edit Role</a>
                                            @endcan
                                            @can('delete role')
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#deleteRole">Delete Role</a>
                                            @endcan
                                        </div>
                                    </div>
                                </div>
                                @endcanany
                            </div>
                        </div>

                        <h5 class="text-muted">Permissions</h5>
                        <div class="d-flex flex-wrap">
                            @forelse($role->permissions as $permission)
                                <span class="badge badge-info-lighten font-13 d-inline-block mr-2 mb-2 p-1 px-2">
                                    {{ $permission->name }}
                                </span>
                            @empty
                                <p>This role has no permissions.</p>
                            @endforelse
                        </div>
                    </div> <!-- end col-->
                </div>
            </div>
        </div>
    </div>
    <!-- container -->
    <div id="deleteRole" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content modal-filled bg-danger">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="dripicons-wrong h1"></i>
                        <h4 class="mt-2">Confirm Action!</h4>
                        <p class="mt-3">This will delete the role and its permissions assignments.</p>
                        <form action="{{ route('users.roles.destroy', $role) }}" method="post">
                            @csrf
                            @method('delete')
                            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
                        </form>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
@endsection

@push('js')
    <!-- demo app -->
    <script src="{{ asset('js/pages/demo.products.js') }}"></script>
@endpush
