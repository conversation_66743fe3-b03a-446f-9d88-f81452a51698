@extends('layouts.master')

@section('title', 'Edit Role')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.roles.index') }}">Roles</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.roles.show', $role) }}">{{ $role->name }}</a></li>
                            <li class="breadcrumb-item active">Edit Role</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Role</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-lg-7 col-md-9 col-sm-12">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Fill the form below to edit this staff</p>
                        <form action="{{ route('users.roles.update', $role) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')
                            <div class="form-row mb-3">
                                <div class="col-12">
                                    <label for="role-name">Name</label>
                                    <input type="text"
                                           name="name"
                                           id="role-name"
                                           class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name', $role->name) }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="switch0" class="d-block">Attach all Permissions?</label>
                                <input type="checkbox" id="switch0" name="all_permissions" data-switch="none" value="1">
                                <label for="switch0" data-on-label="" data-off-label=""></label>
                            </div>
                            <div class="form-group">
                                <label for="role-permissions">Role Permissions</label>
                                <select class="form-control select2 select2-multiple"
                                        id="role-permissions"
                                        name="permissions[]"
                                        data-toggle="select2"
                                        multiple data-placeholder="Choose permissions...">
                                    @foreach($permissionList as $permId => $permName)
                                        <option value="{{ $permId }}" {{ in_array($permId, $rolePermissions) ? 'selected' : '' }}>{{ $permName }}</option>
                                    @endforeach
                                </select>
                                <small>No need to choose permissions when All Permissions checkbox has been checked</small>
                            </div>

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2"
                                   href="{{ route('users.roles.show', $role) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

