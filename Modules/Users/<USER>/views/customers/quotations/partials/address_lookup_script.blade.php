@push('js')

    <script>
        $('.zipInput').on('change', function () {
            let url = window.location.origin + '/api/address';
            let zipInput = $(this);
            let stateInput = zipInput.parent()
                                        .parent()
                                        .parent()
                                        .find('.stateInput')
                                        .first();
            let cityInput = zipInput.parent()
                                        .parent()
                                        .parent()
                                        .find('.cityInput')
                                        .first();
            console.log(stateInput, cityInput);
            $.ajax({
                method: 'GET',
                url: url,
                data: {
                    zip: $(this).val()
                }
            }).done(function (response) {
                stateInput.val(response.State);
                cityInput.val(response.City);

                zipInput.removeClass('is-invalid');
                zipInput.addClass('is-valid');

            }).fail(function () {
                zipInput.addClass('is-invalid');
                zipInput.removeClass('is-valid');

                stateInput.val('');
                cityInput.val('');
            });
        });
    </script>
@endpush
