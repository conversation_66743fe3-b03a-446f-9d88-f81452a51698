@extends('layouts.master')

@section('title', 'Add Items to Quotation# ' . $quotation->id)

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.show', $quotation->customer) }}">{{ $quotation->customer->name }}</a></li>
                            <li class="breadcrumb-item active">Add Items to Quotation# {{ $quotation->id }}</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Add Items to <a href="{{ route('orderModule.quotations.show', $quotation) }}">Quotation# {{ $quotation->id }}</a> for {{ $quotation->customer->name }}</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <form action="{{ route('users.quotations.items.store', $quotation) }}" method="post">
                            @csrf

                            @include('users::customers.orders.partials.items')

                            <div class="form-group d-flex justify-content-end mt-4">
                                <a class="btn btn-light mr-2" href="{{ route('orderModule.quotations.show', $quotation) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary"><i class="mdi mdi-plus"></i> Add Items</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('inventory::shared.forms.duplicate')
