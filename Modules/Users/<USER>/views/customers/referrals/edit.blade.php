@extends('layouts.master')

@section('title', 'Edit Customer Referral')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a>
              </li>
              <li class="breadcrumb-item"><a
                  href="{{ route('users.customers.show', $customer) }}">{{ $customer->name }}</a>
              </li>
              <li class="breadcrumb-item active">Edit Referral</li>
            </ol>
          </div>
          <h4 class="page-title">Edit Referral <small>| {{ $customer->name }}</small></h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-md-10 col-sm-12">
        <div class="card">
          <div class="card-body px-5 py-4">
            <p>Use the form below to edit the customer referral</p>
            <form
              action="{{ route('users.customers.referrals.update', ['customer' => $customer, 'referral' => $referral]) }}"
              method="post"
              enctype="multipart/form-data">
              @csrf
              @method('put')
              <div class="form-row mb-3">
                <div class="col-md-8">
                  <label for="customer-name">Referred Customer <small>- referred
                      by {{ $customer->name }}</small></label>
                  <select id="customer-name"
                          class="form-control"
                          disabled
                          required>
                    <option value="{{ $referral->new_customer_id }}">
                      {{ $referral->referredCustomer->name }}
                    </option>
                  </select>
                </div>
                <div class="col-md-4 col-sm-12">
                  <label for="referral-discount">Discount</label>
                  <input type="number"
                         name="discount"
                         id="referral-discount"
                         class="form-control @error('discount') is-invalid @enderror"
                         value="{{ old('discount', $referral->discount) }}"
                         step="0.01"
                         min="0">
                </div>
              </div>

              <div class="form-group d-flex justify-content-end">
                <a class="btn btn-light mr-2"
                   href="{{ route('users.customers.show', $customer) }}"><i
                    class="mdi mdi-cancel mr-1"></i> Cancel</a>
                <button type="submit" class="btn btn-primary"><i
                    class="mdi mdi-check mr-1"></i> Save Changes
                </button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
@endsection

