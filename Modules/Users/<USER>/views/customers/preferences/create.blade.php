@extends('layouts.master')

@section('title', 'Add Preferences')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.show', $customer) }}">Customer</a></li>
                            <li class="breadcrumb-item active">Add Preference</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Add Preference</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-sm-10">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Fill the form below to assign preferences to customers. You can assign multiple preferences by clicking the <code>+ Add</code> button.</p>
                        <form action="{{ route('users.customers.preferences.store', $customer) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            <fieldset>
                                <div class="form-row duplicate">
                                    <div class="col-md-4">
                                        <label>Name</label>
                                        <select
                                               name="preference_id[]"
                                               class="form-control select2"
                                               data-toggle="select2"
                                               required>
                                            @foreach($preferenceList as $prefId => $prefName)
                                                <option value="{{ $prefId }}">{{ $prefName }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label>Value</label>
                                        <input class="form-control" type="text" name="value[]" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="d-block">Print on Order?</label>
                                        <input class="switch-input" type="checkbox" id="switch0" data-switch="none" value="1" name="print_on_order">
                                        <label class="switch-label" for="switch0" data-on-label="Yes" data-off-label="No"></label>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="d-block">&nbsp;</label>
                                            <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none"><i class="mdi mdi-close"></i> Remove </button>
                                            <button type="button" class="btn btn-link duplicate-btn" data-id="duplicate0"><i class="mdi mdi-plus"></i> Add</button>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                            <div class="form-group mt-3 d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('users.customers.show', $customer) }}"><i class="mdi mdi-cancel mr-1"></i> Cancel</a>
                                <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

@include('inventory::shared.forms.duplicate')
