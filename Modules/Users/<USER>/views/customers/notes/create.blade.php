@extends('layouts.master')

@section('title', 'Add Customer Note')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.show', $customer) }}">{{ $customer->user->name }}</a></li>
                            <li class="breadcrumb-item active">Add Note</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Add Note <small>| {{ $customer->user->name }}</small></h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8 col-sm-10">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Fill the form below to add customer note</p>
                        <form action="{{ route('users.customers.notes.store', $customer) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf

                            <div class="form-row mb-3">
                                <div class="col-12">
                                    <label for="note-body">Name</label>
                                    <textarea
                                           name="body"
                                           id="note-body"
                                           class="form-control @error('body') is-invalid @enderror"
                                           required>{{ old('note') }}</textarea>
                                </div>
                            </div>

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('users.customers.show', $customer) }}"><i class="mdi mdi-cancel mr-1"></i> Cancel</a>
                                <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

