@extends('layouts.master')

@section('title', 'Edit Customer Note')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
              <li class="breadcrumb-item"><a
                  href="{{ route('users.customers.show', $customer) }}">{{ $customer->user->name }}</a></li>
              <li class="breadcrumb-item active">Edit Note</li>
            </ol>
          </div>
          <h4 class="page-title">
            Edit Note for <a href="{{ route('users.customers.show', $customer) }}">{{ $customer->name }}</a>
          </h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-lg-6 col-md-8 col-sm-10">
        <div class="card">
          <div class="card-body px-5 py-4">
            <form action="{{ route('users.customers.notes.update', ['customer' => $customer, 'note' => $note]) }}"
                  method="post"
                  enctype="multipart/form-data">
              @csrf
              @method('put')
              <div class="form-group">
                <div><span class="font-weight-bold">Note Type:</span> {{ $note->formatted_type }}</div>
              </div>
              <div class="form-row mb-3">
                <div class="col-12">
                  <label for="note-body">Note</label>
                  <textarea
                    name="body"
                    id="note-body"
                    rows="8"
                    class="form-control @error('body') is-invalid @enderror"
                    required>{{ old('note', $note->body) }}</textarea>
                </div>
              </div>
              <div class="form-group">
                <label for="note-date">Date</label>
                <input class="form-control date"
                       type="text"
                       name="created_at"
                       id="note-date"
                       data-toggle="date-picker"
                       data-single-date-picker="true"
                       value="{{ old('created_at', $note->created_at->format('m/d/Y')) }}">
              </div>

              <div class="form-group d-flex justify-content-end">
                <a class="btn btn-light mr-2" href="{{ route('users.customers.show', $customer) }}"><i
                    class="mdi mdi-cancel mr-1"></i> Cancel</a>
                <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
@endsection

