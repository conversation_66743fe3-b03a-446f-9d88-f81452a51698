@extends('layouts.master')

@section('title', 'Customers')

@prepend('styles')
  <!-- third party css -->
  <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <!-- third party css end -->
@endprepend

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('users.customers.reports.sales.index') }}">Sales Reports</a>
              </li>
              <li class="breadcrumb-item"><a
                  href="{{ route('users.customers.show', $customer) }}">{{ $customer->name }}</a></li>
              <li class="breadcrumb-item active">Sales Report</li>
            </ol>
          </div>

        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row">
      <div class="col-12">
        <article class="card">
          <div class="card-body">
            <form class="rounded mb-4 bg-light-lighten px-3 py-2">
              <div class="form-row justify-content-center">
                <div class="mr-2">
                  <label for="date_from">Start Date</label>
                  <input class="form-control" id="date_from" type="date" name="date_from"
                         value="{{ old('date_from', $firstInvoiceDate) }}">
                  <input id="print_date_from" type="hidden" name="date_from"
                         value="{{ old('date_from', $firstInvoiceDate) }}" form="sales-report-print-form">
                </div>
                <div class="mr-2">
                  <label for="date_to">End Date</label>
                  <input class="form-control" id="date_to" type="date" name="date_to"
                         value="{{ old('date_to', $lastInvoiceDate) }}">
                  <input id="print_date_to" type="hidden" name="date_to" value="{{ old('date_to', $lastInvoiceDate) }}"
                         form="sales-report-print-form">
                </div>
                <div class="">
                  <label class="d-block" for="date_from">&nbsp;</label>
                  <button class="btn btn-sm btn-primary py-1 mr-2" type="submit"><i class="mdi mdi-filter"></i> Filter
                  </button>
                  @can('print report')
                    <button class="btn btn-sm btn-secondary py-1" type="submit" form="sales-report-print-form"><i
                        class="mdi mdi-printer"></i> Print Report
                    </button>
                  @endcan
                </div>
              </div>
            </form>
            <form class="d-none"
                  action="{{ route('users.customers.reports.sales.print', $customer) }}"
                  id="sales-report-print-form"></form>
            <div class="table-responsive pb-5">
              <table class="table table-centered w-100 dt-responsive nowrap datatable search-autofocus">
                <thead class="thead-light">
                <tr class="border">
                  <th class="text-center h2 bg-transparent border" colspan="10">Sales Report
                    for {{ $customer->name }}</th>
                </tr>
                <tr class="bg-transparent border">
                  <th class="bg-transparent pl-1">Order Date</th>
                  <th class="bg-transparent pl-0">Order Number</th>
                  <th class="bg-transparent pl-0">PO#</th>
                  <th class="bg-transparent text-right pr-0">Qty</th>
                  <th class="bg-transparent pl-2">Description</th>
                  <th class="bg-transparent pl-0">Part#</th>
                  <th class="bg-transparent text-right pr-0">Price</th>
                  <th class="bg-transparent text-right pr-0">Total</th>
                  <th class="bg-transparent text-right pr-0">Final Date</th>
                  <th class="bg-transparent text-right pr-0">Invoice#</th>
                </tr>
                </thead>
                <tbody>
                @foreach ($invoices as $invoice)
                  @foreach($invoice->items as $item)
                    <tr class="border-0">
                      @if ($loop->first)
                        <td class="border-0 p-0 pl-1">{{ $invoice->created_at->format('m/d/y') }}</td>
                        <td class="border-0 p-0">{{ $invoice->full_number }}</td>
                        <td class="border-0 p-0">{{ $invoice->purchase_order_number }}</td>
                      @else
                        <td class="border-0" colspan="3"></td>
                      @endif
                      <td class="border-0 p-0 text-right">{{ $item->quantity }}</td>
                      <td class="border-0 p-0 pl-2">{{ $item->description }}</td>
                      <td class="border-0 p-0">{{ $item->code }}</td>
                      <td class="border-0 p-0 text-right">{{ number_format($item->price, 2) }}</td>
                      <td class="border-0 p-0 text-right">{{ number_format($item->price * $item->quantity, 2) }}</td>
                      @if ($loop->first)
                        <td class="border-0 p-0 text-right">{{ $invoice->invoiced_at->format('m/d/y') }}</td>
                        <td class="border-0 p-0 text-right">{{ $invoice->number }}</td>
                      @else
                        <td colspan="2" class="border-0 p-0"></td>
                      @endif
                    </tr>
                  @endforeach
                  @if($invoice->is_service)
                    <tr>
                      <td class="border-0 p-0" colspan="3"></td>
                      <td class="border-0 p-0 text-right">{{ $invoice->service_hours }}</td>
                      <td class="border-0 p-0 pl-2">Labor</td>
                      <td class="border-0 p-0"></td>
                      <td class="border-0 p-0 text-right">{{ number_format($invoice->service_rate, 2) }}</td>
                      <td class="border-0 p-0 text-right">{{ number_format($invoice->service_hours * $invoice->service_rate, 2) }}</td>
                      <td class="border-0 p-0" colspan="2"></td>
                    </tr>
                    <tr>
                      <td class="border-0 p-0" colspan="3"></td>
                      <td class="border-0 p-0 text-right">1</td>
                      <td class="border-0 p-0 pl-2">Service Call</td>
                      <td class="border-0 p-0"></td>
                      <td class="border-0 p-0 text-right">{{ number_format($invoice->service_call, 2) }}</td>
                      <td class="border-0 p-0 text-right">{{ number_format($invoice->service_call, 2) }}</td>
                      <td class="border-0 p-0" colspan="2"></td>
                    </tr>
                  @endif
                  <tr>
                    <td class="border-0 p-0" colspan="3"></td>
                    <td class="border-0 p-0 text-right">1</td>
                    <td class="border-0 p-0 pl-2">Sales Tax</td>
                    <td class="border-0 p-0"></td>
                    <td class="border-0 p-0 text-right">{{ number_format($invoice->tax, 2) }}</td>
                    <td class="border-0 p-0 text-right">{{ number_format($invoice->tax, 2) }}</td>
                    <td class="border-0 p-0" colspan="2"></td>
                  </tr>
                  <tr>
                    <td class="border-0 p-0" colspan="3"></td>
                    <td class="border-0 p-0 text-right">1</td>
                    <td class="border-0 p-0 pl-2">Ship Via: {{ $invoice->shipping_via }}</td>
                    <td class="border-0 p-0" colspan="2"></td>
                    <td class="border-0 p-0 text-right">{{ number_format($invoice->shipping_cost, 2) }}</td>
                    <td class="border-0 p-0" colspan="3"></td>
                  </tr>
                  <tr>
                    <td class="border-0 pt-1" colspan="12"></td>
                  </tr>
                  <tr class="border border-light font-weight-bold">
                    <td class="text-right pr-0 pt-1 pb-1" colspan="7">Order Total</td>
                    <td class="text-right pr-0 pt-1 pb-1">${{ number_format($invoice->total_amount, 2) }}</td>
                    <td class="pt-1 pb-1" colspan="2"></td>
                  </tr>
                  <tr>
                    <td class="border-0" colspan="12"></td>
                  </tr>
                @endforeach
                </tbody>
                <tfoot>
                <tr>
                  <td class="p-1" colspan="12"></td>
                </tr>
                <tr class="font-weight-bold font-16">
                  <td class="text-right pr-0 pt-1 pb-1" colspan="7">Report Total</td>
                  <td class="text-right p-0">${{ number_format($invoices->sum('total_amount'), 2) }}</td>
                  <td colspan="2"></td>
                </tr>
                </tfoot>
              </table>
            </div>
          </div> <!-- end card-body-->
        </article> <!-- end card-->
      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>
@endsection

