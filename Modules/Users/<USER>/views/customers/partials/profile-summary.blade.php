<div class="media">
  <span class="float-left m-2 mr-4" style="width: 10%; min-width: 100px;">
    <svg version="1.1"
         id="Capa_1"
         xmlns="http://www.w3.org/2000/svg"
         xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
         viewBox="0 0 188.149 188.149"
         style="enable-background:new 0 0 188.149 188.149;"
         xml:space="preserve">
      <g>
        <g>
          <defs>
            <circle
              id="SVGID_1_" cx="94.074" cy="94.075" r="94.074"/>
          </defs>
          <use
            xlink:href="#SVGID_1_"
            style="overflow:visible;fill:#E6E7E2;"/><clipPath
            id="SVGID_2_">
            <use xlink:href="#SVGID_1_" style="overflow:visible;"/>
          </clipPath>
          <path
            style="clip-path:url(#SVGID_2_);fill:#ECC19C;"
            d="M126.708,153.946h-0.02c-2.041-1.545-4.178-2.919-6.429-4.159
c-0.058-0.038-0.115-0.076-0.191-0.095c-10.646-5.876-17.857-17.209-17.857-30.239l-16.121-0.077
c0,13.069-7.269,24.459-18.01,30.315c0,0-0.019,0-0.038,0.019c-2.271,1.24-4.445,2.633-6.506,4.159
c-13.355,9.94-21.997,25.832-21.997,43.766h109.07C148.61,179.74,140.006,163.885,126.708,153.946z"/><path
            style="clip-path:url(#SVGID_2_);fill:#168EF7;"
            d="M148.609,197.629H39.538c0-17.934,8.642-33.826,21.997-43.766
c2.061-1.526,4.235-2.919,6.505-4.159c0.02-0.019,0.039-0.019,0.039-0.019c1.755-0.973,3.434-2.08,4.979-3.339
c5.342,5.476,12.802,8.872,21.063,8.872c8.242,0,15.683-3.396,21.024-8.853c1.526,1.259,3.187,2.366,4.922,3.32
c0.076,0.019,0.134,0.057,0.191,0.095c2.251,1.24,4.388,2.614,6.429,4.159h0.02C140.005,163.879,148.609,179.733,148.609,197.629z
"/><path style="clip-path:url(#SVGID_2_);fill:#ECC19C;" d="M52.217,38.091v42.836c0,28.976,25.437,52.465,41.858,52.465
c16.419,0,41.858-23.489,41.858-52.465V38.091H52.217z"/><path style="clip-path:url(#SVGID_2_);fill:#494846;" d="M129.114,30.207c-9.123-11.423-22.972-18.726-38.463-18.726
c-27.521,0-49.81,22.972-49.81,51.301c0,15.036,6.267,28.556,16.274,37.932c-2.578-6.47-4.018-13.722-4.018-21.38
c0-12.307,3.74-23.578,9.957-32.246c6.596,2.932,17.286,3.993,29.011,2.376c11.625-1.592,21.531-5.433,27.116-10.007
c10.185,8.996,16.806,23.502,16.806,39.877c0,8.39-1.719,16.275-4.802,23.199c9.83-4.069,17.058-18.574,17.058-35.835
C148.243,48.225,139.954,32.987,129.114,30.207z"/></g>
      </g>
      <g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
    </svg>
  </span>
  <div class="media-body">
    <h4 class="mt-1 mb-1 text-white">{{ $customer->name }}</h4>
    <p class="font-13 text-white-50"> Since: {{ $customer->created_at->format('M d, Y') }}</p>

    <ul class="mb-0 list-inline text-light">
      <li class="list-inline-item mr-3">
        <h4 class="mb-1 text-white">
          $ {{ number_format($customer->actual_account_balance, 2) }}</h4>
        <p class="mb-0 font-13">Account Balance:</p>
      </li>
      <li class="list-inline-item">
        <h4 class="mb-1 text-white">{{ $customer->invoicedOrders()->count() }}</h4>
        <p class="mb-0 font-13">Invoiced Orders</p>
      </li>
    </ul>
  </div> <!-- end media-body-->
</div>
