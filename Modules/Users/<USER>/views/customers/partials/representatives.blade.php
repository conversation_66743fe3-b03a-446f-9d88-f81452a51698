<div class="card border-top border-info">
  <div class="card-body">
    <div class="row align-items-center mb-3">
      <div class="col-sm-7">
        <h4 class="header-title mb-0">Representatives</h4>
      </div>
      <div class="col-sm-5 text-right">
        @can('update customer')
          <a class="btn btn-sm btn-outline-info"
             href="{{ route('users.customers.representatives.create', $customer) }}">
            <i class="mdi mdi-plus"></i>
            Add Representative
          </a>
        @endcan
      </div>
    </div>

    @if($customer->representatives->count())
      <div class="table-responsive">
        <table class="table table-sm table-hover table-centered mb-0">
          <thead>
          <tr>
            <th>Name</th>
            <th>Role</th>
            <th>Authorized</th>
            @can('update customer')
              <th class="text-right">Action</th>
            @endcan
          </tr>
          </thead>
          <tbody>
          @foreach($customer->representatives as $representative)
            <tr>
              <td>{{ $representative->name }}</td>
              <td>{{ ucfirst($representative->role) }}</td>
              <td>{{ $representative->authorized ? 'Yes' : 'No' }}</td>
              @can('update customer')
                <td class="text-right">
                  <a
                    href="{{ route('users.customers.representatives.edit', ['customer' => $customer, 'representative' => $representative]) }}"
                    class="action-icon">
                    <i class="mdi mdi-pencil"></i>
                  </a>

                  <a href="#"
                     data-target="#deleteItem"
                     data-toggle="modal"
                     class="action-icon delete-item-btn"
                     data-url="{{ route('users.customers.representatives.destroy', ['customer' => $customer, 'representative' => $representative]) }}">
                    <i class="mdi mdi-delete"></i>
                  </a>
                </td>
              @endcan
            </tr>
          @endforeach
          </tbody>
        </table>
      </div> <!-- end table responsive-->
    @else
      <p class="text-muted">{{ $customer->name }} does not have any representatives</p>
    @endif
  </div>
</div>
