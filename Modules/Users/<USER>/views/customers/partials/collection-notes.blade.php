<div class="card border-top border-info">
  <div class="card-body">
    <div class="row align-items-center mb-3">
      <div class="col-sm-7">
        <h4 class="header-title mb-0">Collection Notes</h4>
      </div>
      @if(! $customer->hasCollectionNote())
        <div class="col-sm-5 text-right">
          @can('update customer note')
            <button type="button" class="btn btn-outline-info btn-sm"
                    data-toggle="modal"
                    data-target="#addCollectionNoteModal">
              <i class="mdi mdi-plus"></i>
              Add Collection Notes
            </button>
          @endcan
        </div>
      @endif
    </div>

    @if($customer->hasCollectionNote())
      <div>
        <small>{{ $customer->collectionNote->formatted_date }}</small>
        <div>{!! nl2br($customer->collectionNote->body) !!}</div>
        @can('update customer note')
          <div class="text-right">
            <a class="btn btn-sm btn-link"
               href="{{ route('users.customers.notes.edit', ['customer' => $customer, 'note' => $customer->collectionNote]) }}">
              <i class="mdi mdi-pencil"></i>
              Edit
            </a>
            <a class="btn btn-sm btn-link text-danger delete-item-btn"
               href="#" data-toggle="modal"
               data-target="#deleteItem"
               data-url="{{ route('users.customers.notes.destroy', ['customer' => $customer, 'note' => $customer->collectionNote]) }}">
              <i class="mdi mdi-delete"></i> Delete
            </a>
          </div>
        @endcan
      </div>
    @else
      <p class="text-muted">There are no collection notes for {{ $customer->name }}.</p>
    @endif
  </div>
</div> <!-- end col-->
