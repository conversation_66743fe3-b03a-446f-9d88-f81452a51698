@push('js')
  <script>
    (function () {
      let paymentTermsSelect = $('#payment-terms');
      let otherPaymentTermsInput = $('#other-payment-terms');

      paymentTermsSelect.change(function () {
        let paymentTermSelected = $(this).val();

        if (paymentTermSelected === 'Other') {
          $(this).removeAttr('name');
          otherPaymentTermsInput.removeClass('d-none');
          otherPaymentTermsInput.attr('name', 'payment_terms');
          otherPaymentTermsInput.attr('required', true);
        } else {
          $(this).attr('name', 'payment_terms');
          otherPaymentTermsInput.addClass('d-none');
          otherPaymentTermsInput.removeAttr('name');
        }
      })
    }());
  </script>
@endpush
