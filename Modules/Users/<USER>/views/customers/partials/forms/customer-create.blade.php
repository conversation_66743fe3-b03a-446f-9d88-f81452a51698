<div id="basicwizard">

  <ul class="nav nav-pills nav-justified form-wizard-header mb-4">
    <li class="nav-item">
      <a href="#customerTab1" data-toggle="tab"
         class="nav-link rounded-0 pt-2 pb-2 active">
        <i class="mdi mdi-account-circle mr-1"></i>
        <span class="d-none d-sm-inline">Account</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="#customerTab2" data-toggle="tab" class="nav-link rounded-0 pt-2 pb-2">
        <i class="mdi mdi-map-marker mr-1"></i>
        <span class="d-none d-sm-inline">Addresses</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="#customerTab3" data-toggle="tab" class="nav-link rounded-0 pt-2 pb-2">
        <i class="mdi mdi-phone mr-1"></i>
        <span class="d-none d-sm-inline">Contacts</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="#customerTab4" data-toggle="tab" class="nav-link rounded-0 pt-2 pb-2">
        <i class="mdi mdi-check-all mr-1"></i>
        <span class="d-none d-sm-inline">Preferences</span>
      </a>
    </li>
  </ul>

  <div class="tab-content b-0 mb-0">
    <div class="tab-pane active" id="customerTab1">
      <div class="row justify-content-center">
        <div class="col-sm-12 col-md-10 col-lg-8 pb-5 pt-4">
          <div class="form-row mb-3">
            <div class="col-12">
              <label for="customer-name">Name</label>
              <input type="text"
                     name="name"
                     id="customer-name"
                     class="form-control @error('name') is-invalid @enderror"
                     value="{{ old('name', $customer->name) }}"
                     placeholder="Customer's Name"
                     required {{ $inputDisabled }}>
            </div>
          </div>
          <div class="form-row mb-3">
            <div class="col-6">
              <label for="customer-email">Email</label>
              <input type="text"
                     name="email"
                     id="customer-email"
                     class="form-control @error('email') is-invalid @enderror"
                     value="{{ old('email', $customer->email) }}"
                     placeholder="e.g. <EMAIL>"
                    {{ $inputDisabled }}>
            </div>
            <div class="col-6">
              <label for="customer-code">Account Number</label>
              <input class="form-control disabled"
                     type="text"
                     id="customer-code"
                     value="{{ $customer->account_number ?? \Modules\Users\Entities\Customer::nextAccountNumber() }}"
                     disabled>
            </div>
          </div>
          <div class="form-row mb-3">
            <div class="col-md-6">
              <label for="customer-type">Customer Type</label>
              <select class="form-control @error('type') is-invalid @enderror" name="type"
                      id="customer-type" required {{ $inputDisabled }}>
                <option value="">Select Customer Type</option>
                @foreach($customerTypeList as $typeKey => $typeName)
                  <option
                    value="{{ $typeKey }}" {{ old('type', $typeKey) === $customer->type ? 'selected' : '' }}>{{ $typeName }}</option>
                @endforeach
              </select>
            </div>
            <div class="col-md-6">
              <label for="price-level">Price Level</label>
              <select
                name="price_level"
                id="price-level"
                class="form-control @error('price_level') is-invalid @enderror" {{ auth()->user()->hasAnyPermission(['update customer', 'update customer price level']) ? '' : 'disabled' }}>
                <option value="">Select Price Level</option>
                @foreach($priceLevels as $levelKey => $levelName)
                  <option
                    value="{{ $levelKey }}" {{ old('price_level', $levelKey) == $customer->price_level ? 'selected' : '' }}>{{ $levelName }}</option>
                @endforeach
              </select>
            </div>
          </div>
          <div class="form-row mb-3">
            <div class="col-md-6">
              <label for="customer-phone">Phone</label>
              <input class="form-control" name="phone" id="customer-phone"
                     value="{{ old('phone', $customer->phone) }}" {{ $inputDisabled }}>
            </div>
            <div class="col-md-6">
              <label for="payment-terms">Payment Terms</label>

              <select class="form-control @error('type') is-invalid @enderror"
                      name="payment_terms"
                      id="payment-terms"
                      required
                      {{ $inputDisabled }}
                      {{ auth()->user()->hasAnyPermission(['update customer', 'update customer payment terms']) ? '' : 'disabled' }}>
                <option value="">Select Customer Type</option>
                @foreach($customerPaymentTermsList as $paymentTerm)
                  <option
                    value="{{ $paymentTerm }}"
                    {{ old('payment_terms') === $paymentTerm ? 'selected' : '' }}>
                    {{ $paymentTerm }}
                  </option>
                @endforeach
              </select>
              <input type="text" class="form-control d-none" name="payment_terms" id="other-payment-terms" placeholder="Enter other payment terms">
            </div>
          </div>
          <div class="form-row mb-3">
            <div class="col-md-6">
              <label for="customer-credit-limit">Credit Limit</label>
              <input class="form-control" type="number" name="credit_limit"
                     id="customer-credit-limit"
                     value="{{ old('credit_limit', $customer->credit_limit) }}"
                     step="0.01" min="0.00" {{ $inputDisabled }}>
            </div>
            <div class="col-md-6">
              <label for="commission-type">Commission Type</label>
              <select class="form-control" name="commission_type" id="commission-type">
                <option value="">Select Commission Type</option>
                @foreach($commissionTypeNames as $commissionType => $commissionName)
                  <option value="{{ $commissionType }}">{{ $commissionName }}</option>
                @endforeach
              </select>
            </div>
          </div>
          <fieldset>
            <legend class="text-muted h4">Taxation</legend>
            <div class="form-row mb-3">
              <div class="col-3">
                <label class="d-block">Taxable?</label>
                <input type="checkbox"
                       id="switch0"
                       data-switch="none"
                       value="1"
                       name="taxable"
                  {{ (bool) old('taxable', $customer->taxable ?? true) ? 'checked' : '' }} {{ $inputDisabled }}>
                <label for="switch0" data-on-label="Yes" data-off-label="No"></label>
              </div>
              <div class="col-9">
                <label for="tax-exempt-number">Tax Exempt Number <small>- Required when
                    tax exempted (Taxable = No)</small></label>
                <input type="text"
                       name="tax_exemption"
                       id="tax-exempt-number"
                       class="form-control mb-3 @error('tax_exemption') is-invalid @enderror"
                       value="{{ old('tax_exemption', $customer->tax_exemption) }}"
                  {{ $inputDisabled }}>

                <label for="tax-exempt-certificate">Tax Exempt Certificate <small>-
                    Required when tax exempted (Taxable = No)</small></label>
                <input type="file"
                       name="tax_certificate"
                       id="tax-exempt-certificate"
                       class="form-control mb-1 @error('tax_certificate') is-invalid @enderror"
                       accept="application/pdf" {{ $inputDisabled }}>
                @if($customer->tax_certificate_name)
                  <small class="font-italic">
                    Current Certificate:
                    <a class="text-info" href="{{ $customer->tax_certificate_url }}">
                      {{ $customer->tax_certificate_name }}
                    </a>
                  </small>
                @endif
              </div>
            </div>
          </fieldset>
        </div> <!-- end col -->
      </div> <!-- end row -->
    </div>

    <div class="tab-pane" id="customerTab2">
      <div class="row justify-content-center">
        <div class="col-sm-12 col-md-10 col-lg-8 pb-5 pt-4">
          <fieldset class="mb-4">
            <legend class="text-muted h4">Account Address</legend>
            <div class="form-row mb-3">
              <div class="col-md-9 col-sm-12">
                <label for="address-unit">Unit </label>
                <input type="text"
                       name="account_address_unit"
                       id="address-unit"
                       class="form-control @error('account_address_unit') is-invalid @enderror"
                       value="{{ old('account_address_unit', optional($accountAddress ?? null)->unit) }}"
                       placeholder="e.g 403 ABC Towers, Floor 3, Room 2" {{ $inputDisabled }}>
              </div>
              <div class="col-md-3 col-sm-12">
                <label for="address_zip">Zip Code</label>
                <input type="text"
                       name="account_address_zip"
                       id="account_address_zip"
                       class="form-control @error('account_address_zip') is-invalid @enderror"
                       value="{{ old('account_address_zip', optional($accountAddress ?? null)->zip) }}"
                       placeholder="e.g 90210" {{ $inputDisabled }}>
              </div>

            </div>
          </fieldset>

          <fieldset class="mb-4">
            <legend class="text-muted h4">Billing Address</legend>
            <div class="form-row mb-3">
              <div class="col-md-9 col-sm-12">
                <label for="billing-address-unit">Unit </label>
                <input type="text"
                       name="billing_address_unit"
                       id="billing-address-unit"
                       class="form-control @error('billing_address_unit') is-invalid @enderror"
                       value="{{ old('billing_address_unit', optional($billingAddress ?? null)->unit) }}"
                       placeholder="e.g 403 ABC Towers, Floor 3, Room 2" {{ $inputDisabled }}>
              </div>
              <div class="col-md-3 col-sm-12">
                <label for="billing_address_zip">Zip Code</label>
                <input type="text"
                       name="billing_address_zip"
                       id="billing_address_zip"
                       class="form-control @error('billing_address_zip') is-invalid @enderror"
                       value="{{ old('billing_address_zip', optional($billingAddress ?? null)->zip) }}"
                       placeholder="e.g 90210" {{ $inputDisabled }}>
              </div>
            </div>
          </fieldset>

          <fieldset class="mb-4">
            <legend class="text-muted h4">Shipping Address</legend>
            <div class="form-row mb-3">
              <div class="col-md-9 col-sm-12">
                <label for="shipping-address-unit">Unit </label>
                <input type="text"
                       name="shipping_address_unit"
                       id="shipping-address-unit"
                       class="form-control @error('shipping_address_unit') is-invalid @enderror"
                       value="{{ old('shipping_address_unit', optional($shippingAddress ?? null)->unit) }}"
                       placeholder="e.g 403 ABC Towers, Floor 3, Room 2" {{ $inputDisabled }}>
              </div>
              <div class="col-md-3 col-sm-12">
                <label for="shipping_address_zip">Zip Code</label>
                <input type="text"
                       name="shipping_address_zip"
                       id="shipping_address_zip"
                       class="form-control @error('shipping_address_zip') is-invalid @enderror"
                       value="{{ old('shipping_address_zip', optional($shippingAddress ?? null)->zip) }}"
                       placeholder="e.g 90210" {{ $inputDisabled }}>
              </div>
            </div>
          </fieldset>
        </div> <!-- end col -->
      </div> <!-- end row -->
    </div>

    <div class="tab-pane" id="customerTab3">
      <div class="row justify-content-center">
        <div class="col-sm-12 col-md-10 col-lg-8 pb-5 pt-3">
          <h4 class="mb-3 text-muted">Contact Information</h4>
          @if($customer->contacts->count())
            @foreach($customer->contacts as $contact)
              <fieldset class="mb-3">
                <div class="form-row mb-3">
                  <div class="col-12">
                    <label for="customer-contact-name">Name</label>
                    <input type="text"
                           name="contact_name[]"
                           id="customer-contact-name"
                           class="form-control"
                           value="{{ $contact->name }}"
                           placeholder="E.g name of contact of person, department etc" {{ $inputDisabled }}>
                  </div>
                </div>

                <div class="form-row mb-3">
                  <div class="col-md-6 col-sm-12">
                    <label for="contact-email">Email</label>
                    <input type="email"
                           name="contact_email[]"
                           id="contact-email"
                           value="{{ $contact->email }}"
                           class="form-control" {{ $inputDisabled }}>
                  </div>
                  <div class="col-md-6 col-sm-12">
                    <label for="contact-phone">Phone</label>
                    <input type="text"
                           name="contact_phone[]"
                           id="contact-phone"
                           value="{{ $contact->phone }}"
                           class="form-control" {{ $inputDisabled }}>
                  </div>
                </div>

                <div class="form-row mb-3">
                  <div class="col-md-6 col-sm-12">
                    <label for="contact-other">Other</label>
                    <input type="text"
                           name="contact_other[]"
                           id="contact-other"
                           class="form-control"
                           value="{{ $contact->other }}"
                           placeholder="e.g Skype ID, Whereby Url etc" {{ $inputDisabled }}>
                  </div>
                </div>
              </fieldset>
            @endforeach
          @else
            <fieldset class="duplicate">
              <div class="form-row mb-3">
                <div class="col-12">
                  <label for="customer-name">Name</label>
                  <input type="text"
                         name="contact_name[]"
                         id="customer-name"
                         class="form-control"
                         placeholder="E.g name of contact of person, department etc" {{ $inputDisabled }}>
                </div>
              </div>

              <div class="form-row mb-3">
                <div class="col-md-6 col-sm-12">
                  <label for="contact-email">Email</label>
                  <input type="email"
                         name="contact_email[]"
                         id="contact-email"
                         class="form-control" {{ $inputDisabled }}>
                </div>
                <div class="col-md-6 col-sm-12">
                  <label for="contact-phone">Phone</label>
                  <input type="text"
                         name="contact_phone[]"
                         id="contact-phone"
                         class="form-control" {{ $inputDisabled }}>
                </div>
              </div>

              <div class="form-row mb-3">
                <div class="col-md-6 col-sm-12">
                  <label for="contact-other">Other</label>
                  <input type="text"
                         name="contact_other[]"
                         id="contact-other"
                         class="form-control"
                         placeholder="e.g Skype ID, Whereby Url etc" {{ $inputDisabled }}>
                </div>
                <div class="col-sm-6">
                  <div class="form-group">
                    <label class="d-block">&nbsp;</label>
                    <button type="button"
                            class="btn btn-link remove-duplicate-btn text-danger"
                            style="display:none"><i class="mdi mdi-close"></i> Remove
                    </button>
                    <button type="button" class="btn btn-link duplicate-btn"
                            data-id="duplicate0"><i class="mdi mdi-plus"></i> Add
                    </button>
                  </div>
                </div>
              </div>
            </fieldset>
          @endif
        </div>
      </div>
    </div>

    <div class="tab-pane" id="customerTab4">
      <div class="row justify-content-center">
        <div class="col-sm-12 col-md-10 pb-5 pt-3">
          <h4 class="text-muted mb-3">Customer Preferences</h4>
          <div class="form-row mb-3 align-items-center">
            <label class="col-md-3 col-form-label" for="switch_use_mail"> Use Direct
              Mail</label>
            <div class="col-md-9">
              <div class="custom-control custom-switch mr-2 mb-1">
                <input type="checkbox"
                       name="use_mail"
                       class="custom-control-input"
                       id="switch_use_mail"
                       value="1" {{ (bool) old('use_mail', $customer->use_mail) ? 'checked' : '' }} {{ $inputDisabled }}>
                <label class="custom-control-label" for="switch_use_mail">Yes</label>
              </div>
            </div>
          </div>
          <div class="form-row mb-3 align-items-center">
            <label class="col-md-3 col-form-label" for="alert-email-input">Alert Email</label>
            <div class="col-md-9">
              <input type="text"
                     name="alert_email"
                     class="form-control"
                     id="alert-email-input"
                     value="{{ old('alert_email', $customer->alert_email) }}">
            </div>
          </div>
          @foreach($preferenceList as $prefId => $pref)
            <div class="form-row mb-3 align-items-center">
              <label class="col-md-3 col-form-label"
                     for="switch_{{ $pref['key'] }}"> {{ $pref['name'] }}</label>
              <div class="col-md-9">
                @if($pref['type'] === 'boolean')
                  <div class="custom-control custom-switch mr-2 mb-1">
                    <input type="checkbox"
                           name="{{ $pref['key'] }}"
                           class="custom-control-input"
                           id="switch_{{ $pref['key'] }}"
                           value="Yes" {{ strtolower($pref['value']) === 'yes' ? 'checked' : '' }} {{ $inputDisabled }}>
                    <label class="custom-control-label"
                           for="switch_{{ $pref['key'] }}">Yes</label>
                  </div>
                @else
                  <input type="text"
                         name="{{ $pref['key'] }}"
                         class="form-control"
                         id="switch_{{ $pref['key'] }}"
                         value="{{ $pref['value'] }}" {{ $inputDisabled }}>

                  @if ($pref['key'] === 'email_invoices')
                    <small>Separate each email with a comma <code>(,)</code> followed by
                      an optional space character.</small>
                  @endif
                @endif
              </div>
            </div>
          @endforeach

          <div class="form-row mb-3 align-items-center">
            <label class="col-md-3 col-form-label" for="manuals_warranty_label">
              Manuals Warranty Label <small class="font-italic"> - Each preference should
                be on its own line</small>
            </label>
            <div class="col-md-9">
              <textarea
                name="manuals_warranty_label"
                class="form-control"
                rows="5"
                id="manuals_warranty_label" {{ $inputDisabled }}>{{ old('manuals_warranty_label', $customer->getOption('manuals_warranty_label', '')) }}</textarea>
            </div>
          </div>
          <div class="form-row mb-3 align-items-center">
            <label class="col-md-3 col-form-label" for="special_instructions">
              Finished Order <small class="font-italic"></small>
            </label>
            <div class="col-md-9">
                            <textarea
                              name="special_instructions"
                              class="form-control"
                              rows="5"
                              id="special_instructions" {{ $inputDisabled }}>{{ old('special_instructions', $customer->special_instructions) }}</textarea>
            </div>
          </div>

          <div class="form-row mb-3 align-items-center">
            <label class="col-md-3 col-form-label" for="invoice_printing_instructions">
              Invoice Printing <small class="font-italic"></small>
            </label>
            <div class="col-md-9">
              <textarea
                name="invoice_printing_instructions"
                class="form-control"
                rows="5"
                id="invoice_printing_instructions" {{ $inputDisabled }}>{{ old('invoice_printing_instructions', $customer->invoice_printing_instructions) }}</textarea>
            </div>
          </div>

          <div class="form-row mb-3 align-items-center">
            <label class="col-md-3 col-form-label" for="customer-preference">
              Customer Preference <small class="font-italic"></small>
            </label>
            <div class="col-md-9">
              <textarea
                name="preference"
                class="form-control @error('preference') is-invalid @enderror"
                rows="5"
                id="customer-preference" {{ $inputDisabled }}>{{ old('preference', $customer->preference) }}</textarea>
            </div>
          </div>

          <div class="form-row mb-3 align-items-center">
            <label class="col-md-3 col-form-label" for="building-preference">
              Building Preference <small class="font-italic"></small>
            </label>
            <div class="col-md-9">
              <textarea
                name="building_preference"
                class="form-control"
                rows="5"
                id="building-preference" {{ $inputDisabled }}>{{ old('building_preference', $customer->building_preference) }}</textarea>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div> <!-- tab-content -->
</div> <!-- end #basicwizard-->

@include('users::customers.partials.forms.customer-profile-scripts')
