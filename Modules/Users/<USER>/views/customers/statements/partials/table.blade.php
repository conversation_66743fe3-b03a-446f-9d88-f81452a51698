<table class="table table-bordered mb-1">
  <thead>
  <tr class="table-header" style="font-size: 10px;">
    <th class="p-1">Invoice <br>Date</th>
    <th class="p-1">Item</th>
    <th class="text-right p-1">Reference<br>Number</th>
    <th class="text-right p-1">Payment</th>
    <th class="p-1">Customer<br>P.O #.</th>
    <th class="text-right p-1">Current</th>
    <th class="text-right p-1">Past<br>Due</th>
    <th class="p-1">Due<br>Date</th>
    <th class="text-right p-1">Invoice<br>Total</th>
    <th class="text-right p-1">Running<br>Total</th>
    @if (request('statement_type') === 'payment-log')
      <th style="border: 0; background-color: transparent">Payment Log</th>
    @endif
  </tr>
  </thead>
  <tbody>
  @php $runningTotal = 0; $totalPastDue = 0; $currentTotal = 0; @endphp
  @foreach($records as $record)
    <tr>
      <td>{{ $record['invoiced_at'] }}</td>
      <td>{{ $record['item'] }}</td>
      <td class="text-right">{{ $record['reference_number'] }}</td>
      <td class="text-right">
        @if (! is_null($record['payment']))
          {{ number_format($record['payment'], 2) }}
        @endif
      </td>
      <td>{{ $record['purchase_order_number'] }}</td>
      <td class="text-right">
        @if ($record['item'] === 'INV')
          @php $currentTotal += ($record['current'] ?? 0); @endphp
          @if (! is_null($record['current']))
            {{ number_format($record['current'], 2) }}
          @endif
        @else
          @php $currentTotal -= ($record['current'] ?? 0); @endphp
        @endif
      </td>
      <td class="text-right">
        @if(! is_null($record['past_due']))
          @php $totalPastDue += $record['past_due'] @endphp
          {{ number_format($record['past_due'], 2) }}
        @endif
      </td>
      <td>{{ $record['due_date'] }}</td>
      <td class="text-right">
        @if($record['item'] === 'INV')
          {{ number_format($record['invoice_total'], 2) }}
        @endif
      </td>
      <td class="text-right">
        {{ number_format($runningTotal += $record['invoice_balance'], 2) }}
      </td>
      @if (request('statement_type') === 'payment-log')
        <td style="border: 0;">
          @foreach($record['payment_log'] ?? [] as $log)
            <div>{{ $log }}</div>
          @endforeach
        </td>
      @endif
    </tr>
  @endforeach
  </tbody>
  <tfoot class="border-0">
  <tr class="border-0">
    <td colspan="6" class="font-16">
      Thank you for your business
    </td>
    <td colspan="4" style="padding: 0">
      <table class="table" style="border: none;">
        <tr>
          <td class="text-right w-50 font-weight-bold" colspan="2"><strong>Current</strong></td>
          <td class="font-weight-bold text-right" colspan="2"><strong>${{ number_format($currentTotal, 2) }}</strong>
          </td>
        </tr>

        <tr>
          <td class="text-right font-weight-bold" colspan="2"><strong>Past Due</strong></td>
          <td class="font-weight-bold text-right" colspan="2"><strong>${{ number_format($totalPastDue, 2) }}</strong>
          </td>
        </tr>
        <tr>
          <td class="text-right font-weight-bold font-18 table-header text-uppercase text-dark" colspan="2"><strong>Total</strong>
          </td>
          <td class="font-18 font-weight-bold text-right" colspan="2">
            <strong>${{ number_format($runningTotal, 2) }}</strong></td>
        </tr>
      </table>
    </td>
  </tr>
  </tfoot>
</table>
