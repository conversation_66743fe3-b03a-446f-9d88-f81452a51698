@extends('layouts.master')

@section('title', 'Customer Orders')

@prepend('styles')
    <!-- third party css -->
    <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
    <!-- third party css end -->
@endprepend

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.show', $customer) }}">{{ $customer->name }}</a></li>
                            <li class="breadcrumb-item active">Order # {{ $order->id }}</li>
                        </ol>
                    </div>
                    <h4 class="page-title"><a href="{{ route('users.customers.show', $customer) }}">{{ $customer->name }}'s</a> <span class="text-muted"> | </span> Order #{{ $order->id }}</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        @include('orders::orders.partials.order-details')
        <!-- end row -->
    </div>

    <div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content modal-filled bg-danger">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="dripicons-wrong h1"></i>
                        <h4 class="mt-2">Confirm Action!</h4>
                        <p class="mt-3">This will delete this item from Order# {{ $order->id }}.</p>
                        <form action="#" method="post" id="delete-item-form">
                            @csrf
                            @method('delete')
                            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
                        </form>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>

    <div id="editItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content ">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="dripicons-document-edit h1"></i>
                        <h4 class="mt-2">Update Item Details!</h4>
                    </div>
                        <form action="#" method="post" id="edit-item-form">
                            @csrf
                            @method('put')

                            <div class="form-group">
                                <label for="item-quantity">Quantity</label>
                                <input type="number" class="form-control" name="quantity" id="item-quantity" value="1">
                            </div>
                            <div class="form-group">
                                <label for="item-price">Price Override</label>
                                <input type="number" class="form-control" name="price" id="item-price" step="0.01" min="0">
                            </div>
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary my-2" data-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary my-2">Save Changes!</button>
                            </div>
                        </form>

                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- container -->
@endsection

@push('js')
    <script src="{{ asset('js/pages/demo.products.js') }}"></script>
    <script>
        $('.edit-item-btn').click(function (e) {
            let form = $('#edit-item-form');
            let qtyInput = form.find('#item-quantity');
            let priceInput = form.find('#item-price');

            form.attr('action', $(this).data('url'));
            qtyInput.val($(this).data('quantity'));
            priceInput.val($(this).data('price'));
        })
    </script>
@endpush
