<div class="table-responsive">
  <table class="table">
    <thead>
    <tr>
      <th>Qty</th>
      <th>Part No.</th>
      <th>Code</th>
      <th>Location</th>
      <th>Description</th>
      <th>Notes</th>
      <th class="text-right">Price</th>
      <th class="text-right">Total</th>
      <th class="extra-detail d-none">Cost Date</th>
      <th class="text-right extra-detail d-none">Cost</th>
      <th class="text-right extra-detail d-none">Cost Total</th>
      <th class="text-right extra-detail d-none">Markup</th>
      <th class="extra-detail d-none">Comm. Type</th>
      <th class="text-right extra-detail d-none">Qty Available</th>
      <th class="text-right"></th>
    </tr>
    </thead>
    <tbody id="items-table-body" data-items-total="">
    @if (! is_null($serialNumberPart))
      <tr id="item-{{ $serialNumberPart->id }}">
        <td class="d-flex flex-nowrap align-items-center">
          <i class="mdi mdi-sort mr-1"></i>
          <input class="item-quantity-input text-center form-control show-change"
                 type="number"
                 step="0.01"
                 name="quantity[]"
                 value="1"
                 style="min-width: 30px; padding: 3px;">
        </td>
        <td>
          <div class="form-group">
            <input class="form-control show-change"
                   type="text"
                   name="code[]"
                   value="{{ $serialNumberPart->code }}">
            <input type="hidden" name="item_id[]" value="{{ $serialNumberPart->id }}">
          </div>
        </td>
        <td>
          <div class="form-group">
            <input class="form-control show-change"
                   type="text"
                   name="number[]"
                   value="{{ $serialNumberPart->number }}">
          </div>
        </td>
        <td>
          <div class="form-group">
            <input class="form-control show-change"
                   type="text"
                   name="location[]"
                   value="">
          </div>
        </td>
        <td>
          <div class="form-group">
            <textarea
              class="form-control show-change"
              name="description[]">{{ $serialNumberPart->description }}</textarea>
            <input type="hidden"
                   name="weight[]"
                   step="0.001"
                   min="0"
                   value="{{ $serialNumberPart->weight }}">
            <input type="hidden"
                   name="freight[]"
                   step="0.001"
                   min="0"
                   value="{{ $serialNumberPart->freight }}">
          </div>
        </td>
        <td>
          <div class="form-group">
            <textarea class="form-control show-change"
                      name="build_instructions[]">{{ $serialNumberPart->build_instructions }}</textarea>
          </div>
        </td>
        <td class="text-right {{ $hidePrices ? 'hide-price d-none' : '' }}">
          <input
            class="price-override-input text-center form-control show-change d-inline-block"
            type="text"
            name="price[]"
            value="0.00" step="0.01" data-price="0.00" style="width: 100px">
        </td>
        <td class="text-right {{ $hidePrices ? 'hide-price d-none' : '' }} item-total">
          0.00
        </td>
        <td class="extra-detail d-none text-right"></td>
        <td class="extra-detail d-none text-right">
          <div class="form-group">
            <input class="cost-override-input text-center form-control show-change"
                   type="text"
                   name="cost[]"
                   value="{{ number_format($serialNumberPart->cost, 2, '.', '') }}"
                   data-cost="{{ number_format($serialNumberPart->cost, 2, '.', '') }}"
                   step="0.01"
                   style="width: 70px">
          </div>
        </td>
        <td class="text-right extra-detail d-none item-total-cost">0.00</td>
        <td class="text-right extra-detail d-none item-markup">0</td>
        <td class="extra-detail d-none"></td>
        <td class="extra-detail d-none"></td>
        @if($order->notInvoiced())
          <td class="text-right">
            <div class="d-flex flex-nowrap">
              <a href="#" class="delete-item-btn text-muted"><i
                  class="mdi mdi-delete"></i></a>
            </div>
          </td>
        @endif
      </tr>
    @endif

    </tbody>
  </table>
  <div class="text-right">
    <button type="button" class="btn btn-sm btn-link d-none" id="extra-details-btn">View
      Extra Details
    </button>
  </div>
</div>

@include('orders::shared.forms.inventory-item-details')
@include('orders::shared.forms.custom-item-details')

@include('users::customers.shared.item-list')

@push('js')
  @include('users::customers.orders.partials.add-item-script')
@endpush
