@extends('layouts.master')

@section('title', 'Add Items to Order# ' . $order->id)

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.show', $order->customer) }}">{{ $order->customer->name }}</a></li>
                            <li class="breadcrumb-item active">Add Items to Order# {{ $order->id }}</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Add Items to <a href="{{ route('users.customers.orders.show', [$order->customer, $order]) }}">Order# {{ $order->id }}</a> for {{ $order->customer->name }}</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <form action="{{ route('users.orders.items.store', $order) }}" method="post">
                            @csrf

                            <fieldset>
                                <div class="form-row">
                                    <div class="col-sm-12 col-md-5">
                                        <label for="item-id">Item</label>
                                        <select
                                            name="item_id"
                                            id="item-id"
                                            class="form-control select2"
                                            data-toggle="select2">
                                            <option value="">Select Item</option>
                                            @foreach($itemList as $itemId => $itemName)
                                                <option value="{{ $itemId }}">{{ $itemName }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="item-quantity">Quantity</label>
                                        <input type="number" class="form-control" name="quantity" id="item-quantity" value="1">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="item-price">Price Override</label>
                                        <input type="number" class="form-control" name="price" id="item-price" step="0.01" min="0">
                                    </div>
                                </div>
                            </fieldset>

                            <div class="form-group d-flex justify-content-end mt-4">
                                <a class="btn btn-light mr-2" href="{{ route('users.customers.orders.show', [$order->customer, $order]) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary"><i class="mdi mdi-plus"></i> Add Items</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
