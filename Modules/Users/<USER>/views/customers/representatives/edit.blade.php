@extends('layouts.master')

@section('title', 'Edit Representative')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.show', $customer) }}">{{ $customer->name }}</a></li>
                            <li class="breadcrumb-item active">Edit Representative</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Representative</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-10 col-sm-12">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Use the form below to edit this representative</p>
                        <form action="{{ route('users.customers.representatives.update', [
                            'customer' => $customer,
                            'representative' => $representative
                            ]) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')

                            @include('users::customers.representatives.partials.form')

                            <div class="form-group d-flex justify-content-end">
                                <a class="btn btn-light mr-2" href="{{ route('users.customers.show', $customer) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection

