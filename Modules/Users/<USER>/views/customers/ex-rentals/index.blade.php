@extends('layouts.master')

@section('title', 'Customers')

@prepend('styles')
  <!-- third party css -->
  <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <!-- third party css end -->
@endprepend

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item active">Ex-Rental Customers</li>
            </ol>
          </div>
          <h4 class="page-title">Ex-Rental Customers</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row mb-2">
              <div class="col-sm-8">
                @include('shared.advanced-search', ['searchFields' => $searchFields, 'searchConditions' => $searchConditions])
              </div>
              <div class="col-sm-4 text-right">
                @can('create customer')
                  <a href="{{ route('users.customers.create') }}" class="btn btn-danger mb-2"><i
                      class="mdi mdi-plus-circle mr-1"></i> Add Customer</a>
                @endcan
              </div><!-- end col-->
            </div>

            <div class="table-responsive pb-5">
              <table class="table table-sm table-centered w-100 dt-responsive nowrap datatable search-autofocus">
                <thead class="thead-light">
                <tr>
                  <th>Acct#</th>
                  <th class="all">Name</th>
                  <th>Phone</th>
                  <th>Account Address</th>
                  <th>Bill To Address</th>
                  <th>Shipping Address</th>
                  <th>Contacts</th>
                  <th class="text-right" style="min-width: 85px;">Action</th>
                </tr>
                </thead>
                <tbody>
                @foreach ($customers as $customer)
                  <tr>
                    <td>{{ optional($customer)->account_number }}</td>
                    <td>
                      <p class="m-0 d-inline-block align-middle font-16">
                        <a href="{{ route('users.customers.show', $customer->user_id) }}"
                           class="text-info">{{ $customer->user_name }}</a>
                      </p>
                    </td>
                    <td>{{ $customer->user_phone }}</td>
                    <td class="font-13">{!! nl2br($customer->account_address) !!}</td>
                    <td class="font-13">{!! nl2br($customer->billing_address) !!}</td>
                    <td class="font-13">{!! nl2br($customer->shipping_address) !!}</td>
                    <td class="font-13">{!! nl2br($customer->customer_contacts) !!}</td>
                    <td class="table-action text-right">
                      <a href="#"
                         data-target="#deleteItem"
                         data-toggle="modal"
                         class="text-muted delete-data-item-btn"
                         data-url="{{ route('users.customers.destroy', $customer->user_id) }}">
                        <i class="mdi mdi-delete"></i> Delete
                      </a>
                    </td>
                  </tr>
                @endforeach
                </tbody>
              </table>
            </div>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>

  <div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content modal-filled bg-danger">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-wrong h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">This will delete the customer from the system.</p>
            <form action="#" method="post" id="delete-item-form">
              @csrf
              @method('delete')
              <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  <!-- container -->
@endsection

@push('js')
  <!-- third party js -->
  <script src="{{ asset('js/vendor/jquery.dataTables.min.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.bootstrap4.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.responsive.min.js') }}"></script>
  <script src="{{ asset('js/vendor/responsive.bootstrap4.min.js') }}"></script>
  <!-- third party js ends -->

  <!-- demo app -->
  <script src="{{ asset('js/pages/demo.products.js') }}"></script>
  @include('inventory::shared.forms.duplicate')
@endpush
