@extends('layouts.master')

@section('title', $customer->name)
@push('styles')
  @livewireStyles
@endpush
@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
              <li class="breadcrumb-item active">Customer Details</li>
            </ol>
          </div>
          <h4 class="page-title">Customer Details</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->
    <div class="row">
      <div class="col-sm-12">
        <!-- Profile -->
        <div class="card bg-info">
          <div class="card-body profile-user-box">

            <div class="row">
              <div class="col-sm-12 col-md-6">
                @include('users::customers.partials.profile-summary')
              </div> <!-- end col-->

              <div class="col-sm-12 col-md-6">
                @include('users::customers.partials.customer-menu')
              </div> <!-- end col-->
            </div> <!-- end row -->

          </div> <!-- end card-body/ profile-user-box-->
        </div><!--end profile/ card -->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

    <div class="row">
      <div class="col-md-4">
        <!-- Personal-Information -->
        @include('users::customers.partials.account-details')
        @include('users::customers.partials.contacts')
        @include('users::customers.partials.addresses')
        @include('users::customers.partials.preferences')
      </div>

      <div class="col-md-8">
        @livewire('users.customers.customer-statistics', ['customer' => $customer])
        @livewire('users.customers.customer-receivables', ['customer' => $customer])
        @livewire('users.customers.customer-recent-orders', ['customer' => $customer])
        @include('users::customers.partials.recent-quotations')
        @include('users::customers.partials.special-price-items')
        @include('users::customers.partials.category-discounts')
        @include('users::customers.partials.notes')
        @include('users::customers.partials.collection-notes')
        @include('users::customers.partials.referrals')
        @include('users::customers.partials.representatives')
      </div>
    </div>
  </div>
  <!-- container -->
  @include('users::customers.partials.profile-modals')
@endsection
@include('shared.datatables')
@push('js')
  @livewireScripts
  <script src="{{ asset('js/pages/demo.products.js') }}"></script>
  <script>
    $('.delete-customer-detail-btn').on('click', function (e) {
      let form = $('#delete-item-form');
      form.attr('action', $(this).attr('data-url'));
    });
    $('.activate-address-btn').on('click', function () {
      $('#activate-address-form').attr('action', $(this).data('url'));
    });
  </script>
@endpush
