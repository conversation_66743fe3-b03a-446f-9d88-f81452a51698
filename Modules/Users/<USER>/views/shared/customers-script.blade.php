<script>

    (function () {
        $('#customers-list').change(function () {
            var selectedCustomer = $(this).val();
            var url = window.location.origin + '/api/customers/' + selectedCustomer;
            axios.get(url)
                .then(function (response) {
                    var customer = response.data.customer;

                    updateCustomerFields(customer);
                })
                .catch(function (error) {
                    alert('Could not retrieve item details.');

                    return false;
                });
        });

        function updateCustomerFields(customer) {
            var shippingAddress = {};

            if (customer.shipping_addresses.length) {
                shippingAddress = customer.shipping_addresses[0];
            }

            $('#account_number').val(customer.account_number);
            $('#telephone').val(customer.user.phone);

            if (customer.account_address) {
                let accountAddress = customer.account_address;
                let address = `${accountAddress.unit} \n${accountAddress.city}, ${accountAddress.state} ${accountAddress.zip}`;

                $('#billing_company').val(accountAddress.name);
                $('#billing_unit').val(accountAddress.unit);
                $('#billing_address').val(address);

                if (accountAddress.unit !== undefined || accountAddress.city !== undefined) {
                    $('#billing_address_address').val(address);
                } else {
                    $('#billing_address').val('');
                }
            } else {
                $('#billing_company').val(customer.user.name);
                $('#billing_unit').val('');
                $('#billing_address').val('');
            }

            if (shippingAddress) {
                let address = `${shippingAddress.unit} \n${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.zip}`;
                $('#shipping_phone').val(customer.user.phone);
                $('#shipping_company').val(customer.user.name);
                $('#shipping_unit').val(shippingAddress.unit);
                $('#terms').val(customer.payment_terms);

                if (shippingAddress.unit !== undefined || shippingAddress.city !== undefined) {
                    $('#shipping_address').val(address);
                } else {
                    $('#shipping_address').val('');
                }
            } else {

            }

        }
    }());
</script>
