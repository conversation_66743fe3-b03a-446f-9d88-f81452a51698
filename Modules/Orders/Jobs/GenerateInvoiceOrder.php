<?php

namespace Modules\Orders\Jobs;

use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Storage;
use Modules\Orders\Actions\GenerateInvoiceAction;
use Modules\Orders\Entities\Order;
use Modules\Users\Entities\Customer;
use Modules\Users\Services\StatementService;

class GenerateInvoiceOrder implements ShouldQueue
{
  use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

  protected $invoice;

  /**
   * Create a new job instance.
   *
   * @param Order $invoice
   */
  public function __construct(Order $invoice)
  {
    $this->invoice = $invoice;
  }

  /**
   * Execute the job.
   *
   * @return void
   */
  public function handle()
  {
    app(GenerateInvoiceAction::class)->execute($this->invoice);
  }
}
