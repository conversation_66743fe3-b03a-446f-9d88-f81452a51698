<?php

namespace Modules\Orders\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\Orders\Entities\Order;

class DeleteCanceledBackOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Order::default()
            ->get()
            ->filter(function ($order) {
                return $order->backOrderCanceled() && now()->diffInHours($order->back_order_canceled_at) >= config('settings.orders.canceled.lifetime');
            })
            ->each(function ($order) {
                $order->delete();
            });
    }
}
