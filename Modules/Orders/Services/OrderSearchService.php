<?php


namespace Modules\Orders\Services;

use Illuminate\Support\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Modules\Orders\Entities\Order;
use Modules\Orders\Interfaces\OrderSearchInterface;
use Modules\Orders\Traits\SearchesOrders;

class OrderSearchService implements OrderSearchInterface
{
  use SearchesOrders;

  const OPERATOR_BETWEEN = 'BETWEEN';
  const OPERATOR_NOT_BETWEEN = 'NOT BETWEEN';
  const OPERATOR_IS_NULL = 'IS NULL';
  const OPERATOR_IS_NOT_NULL = 'IS NOT NULL';

  protected bool $completed = false;

  protected bool $pending = false;

  protected bool $printed = false;

  protected bool $assigned = true;

  protected bool $archived = false;

  static protected array $hideFromSearch = [
    'id',
    'acknowledged_at',
    'assigned',
    'canceled',
    'canceled_at',
    'canceled_by',
    'customer_id',
    'editable',
    'imported',
    'parent_id',
    'created_by',
    'updated_at',
    'deleted_at',
    'type',
    'final_date',
    'fulfilled_at',
    'options',
    'completed_by',
    'amount_paid',
    'last_payment_date',
    'negative_balance_utilized',
    'quotation_expires_at',
    'quotation_contact',
    'quotation_job_name',
    'quotation_lead_time',
    'quotation_add_tax',
    'quotation_add_freight',
    'quotation_show_total',
    'paid',
    'paid_at',
    'payment_log',
    'packing_list_printed',
    'packing_list_printed_at',
    'packing_list_printed_by',
    'printed_shop_order',
  ];

  static protected array $relatedSearchFields = [
    'customers.account_number' => 'Customer Account Number',
    'users.name' => 'Customer Name',
    'staff_users.name' => 'Created By',
    'order_line.quantity' => 'Item Quantity',
    'order_line.description' => 'Item Description',
    'order_line.code' => 'Item Part Number',
    'order_line.price' => 'Item Price',
    'order_line.cost' => 'Item Cost',
    'order_line.weight' => 'Item Weight',
    'order_line.build_instructions' => 'Item Build Instructions',
    'orders.created_at' => 'Date'
  ];

  /**
   * @return Builder
   */
  public function searchQuery(): Builder
  {
    $query = $this->defaultQuery();

    if (request()->filled('fields')) {
      // Build the query with the search params
      $this->getFilters()
        ->each(function ($condition, $field) use (&$query) {
          $query = $this->buildQuery($query, $condition, $field);
        });
    } else {
      if (! $this->archived) {
        $query = $query->where('orders.created_at', '>=', now()->subMonths(6)->toDateTimeString());
      }
    }


    return $query->groupBy(['orders.id', 'order_line.id'])
      ->orderByDesc('orders.created_at');
  }

  /**
   * @return \Illuminate\Support\Collection
   */
  public function find(): \Illuminate\Support\Collection
  {
    return $this->searchQuery()
      ->get()
      ->groupBy('id');
  }

  /**
   * @param Builder $query
   * @param $condition
   * @param null $field
   * @return Builder
   */
  protected function buildQuery(Builder $query, $condition, $field = null): Builder
  {
    $value = $condition['value'];
    $operator = $condition['operator'];

    if ($operator === self::OPERATOR_BETWEEN) {
      $pieces = explode(' ', $value);
      return $query->whereBetween($field, [$pieces[0], $pieces[2]]);
    }

    if ($operator === self::OPERATOR_NOT_BETWEEN) {
      $pieces = explode(' ', $value);
      return $query->whereNotBetween($field, [$pieces[0], $pieces[2]]);
    }

    if ($operator === self::OPERATOR_IS_NULL) {
      return $query->whereNull($field);
    }

    if ($operator === self::OPERATOR_IS_NOT_NULL) {
      return $query->whereNotNull($field);
    }

    return $query->where($field, $operator, $value);

  }

  /**
   * @return \Illuminate\Support\Collection
   */
  protected function getFilters(): \Illuminate\Support\Collection
  {
    return collect(request('fields'))
      ->mapWithKeys(function ($field, $key) {
        return [
          $field => Arr::get(
            self::conditions(request('terms')[$key]),
            request('conditions')[$key],
            []
          )
        ];
      });
  }

  /**
   * @return $this
   */
  public function completed(): OrderSearchService
  {
    $this->completed = true;

    return $this;
  }

  /**
   * @return $this
   */
  public function unassigned(): OrderSearchService
  {
    $this->assigned = false;

    return $this;
  }

  /**
   * @return $this
   */
  public function pending(): OrderSearchService
  {
    $this->pending = true;

    return $this;
  }

  /**
   * @return $this
   */
  public function archived(): OrderSearchService
  {
    $this->archived = true;

    return $this;
  }

  public function defaultQuery()
  {
    $query = DB::table('orders')
      ->select(
        'orders.id',
        'orders.number',
        'orders.full_number',
        'orders.invoiced',
        'orders.total_amount',
        'orders.amount',
        'orders.tax_amount',
        'orders.shipping_cost',
        'orders.shipping_actual_freight',
        'orders.canceled',
        'orders.status',
        'orders.purchase_order_number',
        'orders.shipping_name',
        'orders.shipping_date',
        'orders.shipping_date_option',
        'orders.printed_shop_order',
        'customers.account_number as customer_account_number',
        'users.name as customer_name',
        'orders.customer_id',
        'orders.created_by as created_by_id',
        'orders.payment_terms',
        'staff_users.name as created_by_name',
        'order_line.description',
        'order_line.quantity',
        'order_line.price',
        'order_line.code as part_number',
      )
      ->selectRaw("
        DATE_FORMAT(orders.created_at, '%m/%d/%y') as created_at,
        DATE_FORMAT(orders.invoiced_at, '%m/%d/%y') as invoiced_at,
        IF(order_line.back_ordered = 1, order_line.quantity_available, order_line.quantity) as real_quantity,
        IF(order_line.back_ordered = 1, order_line.quantity_available, order_line.quantity) * order_line.price as item_total
      ")
      ->where('orders.type', Order::TYPE_ORDER)
      ->whereNull('orders.canceled_at');

    if ($this->archived) {
      $query = $query->where('orders.archived', 1);

      return $this->lastDefaultQueryPart($query);
    }

    if (! $this->assigned) {
      $query = $query->where('orders.assigned', false)
        ->where('orders.invoiced', false)
        ->where('orders.printed_shop_order', false)
        ->where('orders.canceled', false)
        ->where('orders.acknowledged', false)
        ->where('orders.packing_list_printed', false)
        ->where('orders.delivered', false);
    }

    if ($this->pending) {
      $query = $query->where('orders.invoiced', false);
    }

    if ($this->completed) {
      $query = $query->where('orders.invoiced', false)
        ->where('status', Order::STATUS_COMPLETED);
    }

    return $this->lastDefaultQueryPart($query);
  }

  private function lastDefaultQueryPart($query)
  {
    return $query
      ->join('customers', 'customers.user_id', '=', 'orders.customer_id')
      ->join('users', 'users.id', '=', 'customers.user_id')
      ->leftJoin('users as staff_users', 'staff_users.id', '=', 'orders.created_by')
      ->leftJoin('order_line', 'orders.id', '=', 'order_line.order_id');
  }

  public function searchFields(): array
  {
    return collect(Schema::getColumnListing('orders'))
      ->reject(function ($column) {
        return in_array($column, self::$hideFromSearch);
      })
      ->mapWithKeys(function ($column) {
        return [
          'orders.' . $column => Str::title(str_replace('_', ' ', $column))
        ];
      })
      ->merge(self::$relatedSearchFields)
      ->sort()
      ->all();
  }

  public static function conditions($term = ''): array
  {
    return [
      'like' => [
        'label' => 'Contains',
        'operator' => 'LIKE',
        'value' => "%{$term}%"
      ],
      'not_like' => [
        'label' => 'Does Not Contain',
        'operator' => 'NOT LIKE',
        'value' => "%{$term}%"
      ],
      'starts_with' => [
        'label' => 'Begins With',
        'operator' => 'LIKE',
        'value' => "{$term}%"
      ],
      'ends_with' => [
        'label' => 'Ends With',
        'operator' => 'LIKE',
        'value' => "%{$term}"
      ],
      'equal' => [
        'label' => 'Is Equal To',
        'operator' => '=',
        'value' => $term
      ],
      'not_equal' => [
        'label' => 'Is Not Equal To',
        'operator' => '<>',
        'value' => $term
      ],
      'less_than' => [
        'label' => 'Is Less Than',
        'operator' => '<',
        'value' => $term
      ],
      'greater_than' => [
        'label' => 'Is Greater Than',
        'operator' => '>',
        'value' => $term
      ],
      'less_than_equal' => [
        'label' => 'Is Less Than or Equal To',
        'operator' => '<=',
        'value' => $term
      ],
      'greater_than_equal' => [
        'label' => 'Is Greater Than or Equal To',
        'operator' => '>=',
        'value' => $term
      ],
      'between' => [
        'label' => 'Is Between',
        'operator' => 'BETWEEN',
        'value' => $term
      ],
      'not_between' => [
        'label' => 'Is Not Between',
        'operator' => 'NOT BETWEEN',
        'value' => $term
      ],
      'empty' => [
        'label' => 'Is Empty',
        'operator' => 'IS NULL',
        'value' => $term
      ],
      'not_empty' => [
        'label' => 'Is Not Empty',
        'operator' => 'IS NOT NULL',
        'value' => $term
      ]
    ];
  }
}
