<?php


namespace Modules\Orders\Services;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\Inventory\Actions\UpdateMultipleItemQuantityAction;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Services\PriceCalculator;
use Modules\Orders\Actions\Quotations\CreateQuotationAction;
use Modules\Orders\Actions\Quotations\DuplicateQuotationAction;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Quotation;
use Modules\Orders\Events\Orders\OrderCreated;
use Modules\Orders\Events\Orders\OrderUpdated;
use Modules\Orders\Events\Orders\QuotationCreated;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\User;


class OrderService
{
  const CUSTOM_ITEM_PREFIX = 'custom_item';
  /**
   * @var Order
   */
  protected $order;

  protected $hasBackOrder = false;

  protected $user;
  protected $customer;
  protected $request;

  public function __construct()
  {
    $this->request = request();

    if ($this->request->order && is_object($this->request->order)) {
      $this->order = $this->request->order;
      $this->hasBackOrder = $this->order->hasBackOrder();
    } else if (is_string($this->request->order)) {
      $this->order = Order::find($this->request->order);
      $this->hasBackOrder = $this->order && $this->order->hasBackOrder();
    }

    if (is_null($this->order)) {
      if (is_object($customer = $this->request->customer)) {
        $this->customer = $customer;
      } else {
        $this->customer = Customer::find($customer);
      }
    } else {
      $this->customer = $this->order->customer;
    }

    $this->user = auth()->user();
  }

  public function getNextOrderNumber($type = Order::TYPE_ORDER): int
  {
    if ($type === Order::TYPE_QUOTATION) {
      return (new Quotation)->nextNumber();
    }

    return (new Order)->nextOrderNumber();
  }

  public function getNumberInitials($type = Order::TYPE_ORDER): string
  {
    $initials = auth()->user()->staff->initials;

    if ($type === Order::TYPE_QUOTATION) {
      return 'Q' . $initials;
    }

    return $initials;
  }

  public function convertToOrder(Order $quotation): ?Order
  {
    $number = $this->getNextOrderNumber();
    $order = $quotation;
    $order->number = $number;
    $order->full_number = $this->getNumberInitials() . $number;
    $order->type = Order::TYPE_ORDER;
    $order->created_by = auth()->id();
    $order->options = $quotation->options;
    $order->created_at = now();
    $order->customer_taxable = $quotation->customer->taxable;

    if ($quotation->imported &&
      empty($quotation->billing_name) &&
      ! empty($billingAddress = $quotation->customer->accountAddress)) {
      $order->billing_name = $billingAddress->name;
      $order->billing_address = $billingAddress->address;
    }

    $order->save();
    $order = $order->fresh();
    $order->items()->saveMany($quotation->items);

    event(new OrderCreated($order));

    return $order;
  }

  /**
   * Capture record details to create a new order
   *
   * @return array
   */
  public function getOrderDetailsFromRequest(): array
  {
    $record = $this->getUpdateOrderDetailsFromRequest();
    $record['created_by'] = auth()->id();
    $record['amount'] = $this->getItemAmount($this->getItems());

    return $record;
  }

  protected function getItemAmount($items = [])
  {
    return collect($items)->reduce(function ($carry, $item) {
      return $carry + ($item['price'] * $item['quantity']);
    });
  }

  protected function customer()
  {
    return $this->customer ?? $this->order->customer;
  }


  /**
   * Capture record details to update an existing order
   *
   * @return array
   */
  public function getUpdateOrderDetailsFromRequest(): array
  {
    $record = [
      'purchase_order_number' => request('purchase_order_number'),
      'out_of_state' => $this->outOfState(),
      'customer_taxable' => $this->customer->taxable,
      'payment_terms' => $this->customer->payment_terms,
      'shipping_name' => request('shipping_company'),
      'shipping_address' => request('shipping_address'), // city, state zip
      'shipping_phone' => request('shipping_phone'),
      'shipping_via' => request('ship_via'),
      'shipping_date' => request('shipping_date'), // when order should be shipped
      'shipping_date_option' => request('shipping_date_option'),
      'shipping_cost' => request('shipping_cost_override'),
      'shipping_actual_freight' => request('actual_freight'),
      'shipping_actual_date' => request('date_shipped'), // when order was shipped
      'shipping_note' => request('shipping_note'),
      'customer_pickup_by' => request('pickup_by'),
      'customer_placed_by' => request('order_by'),
      'shipping_tracking_number' => request('shipping_tracking_number'),
      'billing_name' => request('billing_company'),
      'billing_address' => request('billing_address'), // unit, city, state zip
      'billing_phone' => request('telephone'),
      'salesperson' => request('salesperson'),
      'shop_notes' => request('shop_notes'),
      'private_notes' => request('private_notes'),
      'created_at' => request('order_date') ?? now(),
      'options' => $this->getOptions(),
    ];

    return array_merge(
      $record,
      $this->getServiceDetails(),
      $this->getForceNoTaxDetails(),
      $this->getForceTaxDetails()
    );
  }

  public function getQuotationDetailsFromRequest(): array
  {
    $nextOrderNumber = $this->getNextOrderNumber(Order::TYPE_QUOTATION);

    $record = [
      'created_by' => auth()->id(),
      'created_at' => request('quotation_date'),
      'type' => Order::TYPE_QUOTATION,
      'salesperson' => request('salesperson'),
      'shipping_name' => request('shipping_company'),
      'shipping_address' => request('shipping_address'), // city, state zip
      'shipping_phone' => request('shipping_phone'),
      'billing_name' => request('billing_company'),
      'billing_address' => request('billing_address'), // unit, city, state zip
      'billing_phone' => request('telephone'),
      'quotation_date' => request('quotation_date'),
      'quotation_number' => $nextOrderNumber,
      'quotation_full_number' => $this->getNumberInitials(Order::TYPE_QUOTATION) . $nextOrderNumber,
      'quotation_expires_at' => request('expiry_date'),
      'quotation_contact' => request('contact'),
      'quotation_job_name' => request('job_name'),
      'quotation_lead_time' => request('lead_time'),
      'quotation_add_tax' => (bool)request('add_tax'),
      'quotation_add_freight' => (bool)request('freight_included'),
      'quotation_show_total' => (bool)request('show_total'),
      'shop_notes' => request('shop_notes'),
      'private_notes' => request('private_notes'),
      'options' => $this->getOptions(),
      'force_no_tax' => (bool) request('no_tax'),
      'force_tax' => (bool) request('yes_tax'),
    ];

    return array_merge($record, $this->getServiceDetails());
  }

  /**
   * Get the details of the quotation to update
   *
   * @return array
   */
  public function getUpdateQuotationDetailsFromRequest(): array
  {
    $record = [
      'options' => $this->getOptions(),
      'salesperson' => request('salesperson'),
      'created_at' => request('quotation_date'),
      'shipping_name' => request('shipping_company'),
      'shipping_address' => request('shipping_address'), // city, state zip
      'shipping_phone' => request('shipping_phone'),
      'billing_name' => request('billing_company'),
      'billing_address' => request('billing_address'), // unit, city, state zip
      'billing_phone' => request('telephone'),
      'expires_at' => request('expiry_date'),
      'contact' => request('contact'),
      'purchase_order_number' => request('purchase_order_number'),
      'lead_time' => request('lead_time'),
      'add_tax' => (bool) request('add_tax'),
      'add_freight' => (bool) request('freight_included'),
      'show_total' => (bool) request('show_total'),
      'shop_notes' => request('shop_notes'),
      'private_notes' => request('private_notes'),
      'force_no_tax' => (bool) request('no_tax'),
      'force_tax' => (bool) request('yes_tax'),
      'out_of_state' => $this->outOfState()
    ];

    return array_merge($record, $this->getServiceDetails());
  }

  /**
   * Duplicate quotation for current customer
   *
   * @param Quotation $quotation
   * @return Order|null
   * @deprecated Use DuplicateQuotationAction class instead
   */
  public function makeDuplicateQuotationForCurrentCustomer(Quotation $quotation): ?Quotation
  {
    return (app(DuplicateQuotationAction::class))->execute($quotation);
  }

  public function makeDuplicateQuotationForCustomer($customerId): \Illuminate\Database\Eloquent\Model
  {
    return (app(CreateQuotationAction::class))->execute(Customer::find($customerId));
  }

  /**
   * Get items from form
   * @param int $position
   * @return array
   */
  public function getItems(int $position = 0): array
  {
    return collect(request('item_id'))
      ->map(function ($code, $key) use ($position) {
        $itemCode = $code;

        if (Str::contains($itemCode, self::CUSTOM_ITEM_PREFIX)) {
          $itemCode = self::CUSTOM_ITEM_PREFIX . '_' . $key;

          return $this->getCustomItem($itemCode, $key, $position);
        }

        return $this->getItem($code, $key, $position);
      })
      ->all();
  }

  /**
   * Get inventory item details to attach to order
   *
   * @return array
   */
  public function getInventoryItemDetails(): array
  {
    return collect(request('item_id'))
      ->reject(function ($itemId) {
        // Remove any custom items
        return Str::contains($itemId, 'custom_item');
      })
      ->mapWithKeys(function ($id, $key) {
        return $this->getItem($id, $key);
      })
      ->all();
  }

  /**
   * Get inventory item details
   *
   * @param $id
   * @param $key
   * @param $position
   * @return array
   */
  protected function getItem($id, $key, $position): array
  {
    return array_merge(
      $this->getDefaultAttributes($key),
      [
        'item_id' => $id,
        'number' => Arr::get(request('number'), $key),
        'location' => Arr::get(request('location'), $key),
        'position' => $position + $key
      ]
    );
  }

  /**
   * Get custom item details
   *
   * @param $code
   * @param $key
   * @param $position
   * @return mixed
   */
  public function getCustomItem($code, $key, $position)
  {
    return array_merge([
      'item_id' => $code, // Matches the custom item to inventory item
      'number' => null,
      'type' => 'custom',
      'position' => $position + $key,
      'location' => null
    ], $this->getDefaultAttributes($key));
  }

  /**
   * @param $key
   * @return array
   */
  protected function getDefaultAttributes($key): array
  {
    $qty = floatval(request('quantity')[$key]);
    $qtyAvailable = null;
    $qtyOriginal = null;

    if ($this->hasBackOrder) {
      if (request()->has('quantity_available')) {
        $qtyAvailable = Arr::get(request('quantity_available'), $key);
      }

      if (request()->has('quantity_original')) {
        $qtyOriginal = Arr::get(request('quantity_original'), $key);
      }
    }

    return [
      'code' => request('code')[$key],
      'description' => request('description')[$key],
      'quantity' => $qty,
      'quantity_available' => $qtyAvailable,
      'quantity_original' => $qtyOriginal,
      'price' => request('price')[$key],
      'cost' => request('cost')[$key],
      'build_instructions' => request('build_instructions')[$key],
      'weight' => request('weight')[$key],
      'commission_type' => Arr::get(request('commission_type'), $key),
    ];
  }

  public function getSerialNumberItem(): array
  {
    $item = Item::whereCode(Item::WSN_CODE)->first();

    if (empty($item)) {
      return [];
    }

    return [
      'item_id' => $item->id,
      'code' => $item->code,
      'description' => $item->description,
      'quantity' => 1,
      'quantity_available' => null,
      'quantity_original' => null,
      'price' => 0.00,
      'cost' => 0.00,
      'build_instructions' => '',
      'weight' => 0.000,
      'number' => $item->number,
      'location' => null,
    ];
  }

  /**
   * Get options for the order
   *
   * @return array
   */
  public function getOptions(): array
  {
    return [
      //'items' => $this->getItems(),
      'statuses' => [
        'created' => [
          'name' => 'created',
          'created_at' => now()->format('m-d-y H:i:s'),
          'created_by' => auth()->id(),
          'previous' => null,
          'note' => ''
        ]
      ]
    ];
  }

  protected function getServiceDetails(): array
  {
    $serviceDetails = [
      'is_service' => false,
      'service_hours' => request('service_hours'),
      'service_rate' => request('service_rate'),
      'service_call' => request('service_call')
    ];

    if ($this->isService()) {
      $serviceDetails['is_service'] = true;
    }

    return $serviceDetails;
  }

  protected function getForceTaxDetails(): array
  {
    if ($this->forceTax()) {
      return [
        'force_tax' => true,
      ];
    }

    return [
      'force_tax' => false
    ];
  }

  protected function getForceNoTaxDetails(): array
  {
    if ($this->forceNoTax()) {
      return [
        'force_no_tax' => true,
      ];
    }

    return [
      'force_no_tax' => false
    ];
  }

  protected function forceNoTax(): bool
  {
    return (bool) request('no_tax');
  }

  protected function forceTax(): bool
  {
    if ($this->forceNoTax()) {
      return false;
    }

    return request()->filled('yes_tax') && (bool)request('yes_tax') === true;
  }

  protected function isService(): bool
  {
    return request('type') || request('is_service');
  }

  protected function isNotService(): bool
  {
    return ! $this->isService();
  }

  /**
   * Store an order for a customer
   *
   * @param Customer $customer
   * @return Order
   */
  public function store(Customer $customer)
  {
    // User has specified a different customer for the order.
    if (request()->filled('customer_id')) {
      $customer = Customer::findOrFail(request()->customer_id);
    }

    $order = $customer->orders()->create($this->getOrderDetailsFromRequest());

    $order->items()->createMany($this->getItems());

    event(new OrderCreated($order->fresh()));

    return $order;
  }

  public function update(Order $order)
  {
    if (!request()->user()->can('update order')) {
      $order->update(request()->only(['shipping_date', 'shipping_date_option', 'shipping_address']));
    } else {
      $order->update($this->getUpdateOrderDetailsFromRequest());
    }

    $lastPosition = $order->items()->count();

    if (count($newOrderLineItems = $this->getItems($lastPosition))) {
      // We have new order line items to add to the order
      $orderLines = $order->items()->createMany($newOrderLineItems);
      app(UpdateMultipleItemQuantityAction::class)->execute($orderLines);
    }

    $order = $order->fresh();
    event(new OrderUpdated($order));
    log_activity($order, 'Updated this order');

    return $order;
  }

  protected function outOfState(): bool
  {
    $inStateStrings = ['Utah', 'utah', ' UT ', 'UTAH'];

    return !Str::contains(request('billing_address'), $inStateStrings) &&
      !Str::contains(request('shipping_address'), $inStateStrings);
  }
}
