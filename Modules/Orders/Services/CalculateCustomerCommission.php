<?php


namespace Modules\Orders\Services;

use Illuminate\Support\Arr;
use Modules\Orders\Entities\Commission;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;
use Modules\Users\Entities\Referral;

class CalculateCustomerCommission
{
  protected Order $order;

  public function __construct(Order $order)
  {
    $this->order = $order;
  }

  public function saveCommission(): void
  {
    $this->saveSpecialCustomerCommission();
    $this->saveReferralCommission();
  }

  /**
   * @return boolean
   */
  public function updateItemCommission(): bool
  {
    // Delete all existing commissions
    Commission::query()
      ->where('order_id', $this->order->id)
      ->delete();
    // Generate anew the commission records for the order
    $this->saveCommission();

    return true;
  }

  /**
   * @param $commissionType
   * @return int|mixed
   */
  protected function getDiscount($commissionType)
  {
    $discounts = [
      Commission::TYPE_RO => $this->getCommercialRODiscount(),
      Commission::TYPE_WS => $this->getWoodruffDiscount(),
      Commission::TYPE_WES => $this->getWesleyDiscount()
    ];


    if (! Arr::has($discounts, $commissionType)) {
      return 0;
    }

    return $discounts[$commissionType];
  }

  protected function saveSpecialCustomerCommission(): bool
  {

    $commissionRecords = $this->order->items->reject(function (OrderLine $item) {
      return ! $item->isCommissioned() || $item->code === OrderLine::SERIAL_NUMBER_CODE;
    })->map(function ($item) {
      return $this->makeCommissionItemRecord($item);
    })->all();

    if (empty($commissionRecords)) {
      return false;
    }

    $this->order->customer->commissions()->createMany($commissionRecords);

    return true;
  }

  protected function getWesleyDiscount()
  {
    return settings('global.commissions.types.wes.value', 0);
  }

  protected function getWoodruffDiscount()
  {
    return settings('global.commissions.types.ws.value', 0);
  }

  private function getCommercialRODiscount()
  {
    return settings('global.commissions.types.ro.value', 0);
  }

  protected function makeCommissionItemRecord(OrderLine $item, $discount = null, $commissionType = null): array
  {
    if (is_null($discount) && is_null($commissionType)) {
      list($discount, $commissionType) = $this->getCommissionTypeDiscount($item);
    }

    return $this->generateRecord($item, $discount, $commissionType);
  }

  /**
   * @return bool
   */
  protected function saveReferralCommission(): bool
  {
    // Check if this order is for a referral.
    $referral = Referral::whereNewCustomerId($this->order->customer_id)->first();

    if (empty($referral)) {
      return false;
    }

    $commissionItems = $this->order->items
      ->map(function ($item) use ($referral) {
        return $this->makeCommissionItemRecord($item, $referral->discount, 'referral');
      })->all();

    $this->order->customer->commissions()->createMany($commissionItems);

    return true;
  }

  protected function newItemCommission(OrderLine $item): bool
  {
    list($discount, $commissionType) = $this->getCommissionTypeDiscount($item);

    if (is_null($commissionType) || $discount === 0) {
      return false;
    }

    $this->createCommission($item, $discount, $commissionType);

    return true;
  }

  /**
   * @param OrderLine $item
   * @return array
   */
  protected function getCommissionTypeDiscount(OrderLine $item): array
  {
    $discount = 0;
    $commissionType = null;

    if ($item->forWesleyCommission()) {
      $discount = $this->getWesleyDiscount();
      $commissionType = Commission::TYPE_WES;
    } else if ($item->forCommercialRoCommission()) {
      $discount = $this->getCommercialRODiscount();
      $commissionType = Commission::TYPE_RO;
    } else if ($item->forWoodruffCommission()) {
      $discount = $this->getWoodruffDiscount();
      $commissionType = Commission::TYPE_WS;
    } else if ($item->forOtherWoodruffCommission()) {
      $discount = $this->getWoodruffDiscount();
      $commissionType = Commission::TYPE_WS_CS;
    }

    return array($discount, $commissionType);
  }

  /**
   * @param OrderLine $item
   * @param $discount
   * @param $commissionType
   * @return mixed
   */
  protected function createCommission(OrderLine $item, $discount, $commissionType): mixed
  {
    if (! $item->isCommissioned()) {
      return false;
    }

    return $this->order->customer
      ->commissions()
      ->create(
        $this->generateRecord(
          $item,
          $discount,
          $commissionType
        )
      );
  }

  /**
   * @param OrderLine $item
   * @param $discount
   * @param string $commissionType
   * @return array
   */
  protected function generateRecord(OrderLine $item, $discount, string $commissionType): array
  {
    $discount = round($discount / 100, 3);

    return [
      'order_id' => $item->order_id,
      'part_number' => $item->code,
      'description' => $item->description,
      'price' => $item->price,
      'quantity' => $item->real_quantity,
      'commission' => round($item->price * $item->real_quantity * $discount, 2),
      'percentage' => $discount,
      'commission_type' => $commissionType
    ];
  }
}
