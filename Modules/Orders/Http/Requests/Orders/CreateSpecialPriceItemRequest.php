<?php

namespace Modules\Orders\Http\Requests\Orders;

use Illuminate\Foundation\Http\FormRequest;

class CreateSpecialPriceItemRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'product_category_id' => 'nullable|required_without:part_category_id|array',
            'part_category_id' => 'nullable|required_without:product_category_id|array',
            'product_id.*' => 'nullable|exists:products,id',
            'part_id.*' => 'nullable|exists:items,id',
            'product_fixed_price.*' => 'nullable|numeric',
            'part_fixed_price.*' => 'nullable|numeric',
            'product_discount.*' => 'nullable|numeric',
            'part_discount.*' => 'nullable|numeric',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
