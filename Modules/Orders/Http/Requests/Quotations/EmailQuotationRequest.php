<?php

namespace Modules\Orders\Http\Requests\Quotations;

use Illuminate\Foundation\Http\FormRequest;

class EmailQuotationRequest extends FormRequest
{
  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'recipients' => 'required|string',
      'recipient_emails.*' => 'email'
    ];
  }

  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  public function prepareForValidation()
  {
    $this->merge(['recipient_emails' => email_recipients($this->recipients)]);
  }
}
