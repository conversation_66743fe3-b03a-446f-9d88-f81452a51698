<?php

namespace Modules\Orders\Http\Requests;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Payment;
use Modules\Users\Entities\Refund;
use Modules\Users\Entities\Customer;
use Money\Money;

class StoreRefund extends FormRequest
{
  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'invoice_id' => 'required|exists:orders,id',
      'amount' => 'required|numeric',
      'notes' => 'nullable|string|max:1000',
      'method' => [
        'required',
        Rule::in(['cash', 'check', 'credit_card', 'eft'])
      ],
      'reference' => 'nullable|required_if:method,check,credit_card,eft|string|max:255', // Check Number | Last 4
      'card_brand' => 'nullable|required_if:method,credit_card',
      'check_name' => 'nullable|required_if:method,check|string|max:255',
      'check_date' => 'nullable|required_if:method,check|max:255',
      'bank_id' => 'nullable|required_if:method,check|string|max:255',
    ];
  }

  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }
}
