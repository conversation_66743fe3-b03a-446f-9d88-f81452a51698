<?php

namespace Modules\Orders\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Receivable;
use Modules\Orders\Services\OrderSearchService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class OrderController extends Controller
{
  public function index(OrderSearchService $orderSearchService)
  {
    if (request('archived') === '1') {
      $orderSearchService->archived();
    }

    return DataTables::of($orderSearchService->searchQuery())->make(true);
  }

  public function showByNumber($invoiceNumber)
  {
    return Receivable::select('record_id', 'record_date', 'reference_number', 'debit_amount', 'amount_paid', 'age', 'balance')
      ->where('reference_number', $invoiceNumber)
      ->where('type', Order::TYPE_ORDER)
      ->first();
  }
}
