<?php

namespace Modules\Orders\Http\Controllers\Api;

use Illuminate\Routing\Controller;
use Modules\Orders\Services\QuotationSearchService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class QuotationController extends Controller
{
  public function index(QuotationSearchService $quotationSearchService)
  {
    return DataTables::of($quotationSearchService->searchQuery())
      ->addColumn('item_total', function ($item) {
        return number_format($item->price * $item->quantity, 2);
      })
      ->addColumn('view_quotation', function ($item) {
        return route('orderModule.quotations.show', $item->id);
      })
      ->make(true);
  }
}
