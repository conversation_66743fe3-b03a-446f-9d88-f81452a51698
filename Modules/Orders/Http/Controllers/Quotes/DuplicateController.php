<?php

namespace Modules\Orders\Http\Controllers\Quotes;

use App\Services\ListService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Inventory\Services\PriceCalculator;
use Modules\Orders\Actions\Quotations\DuplicateQuotationAction;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Quotation;
use Modules\Orders\Services\OrderService;
use Modules\Users\Entities\Customer;

class DuplicateController extends Controller
{
  use AuthorizesRequests;

  protected $lists;
  protected $orderService;

  public function __construct(ListService $lists, OrderService $orderService)
  {
    $this->authorizeResource(Order::class, 'quotation');
    $this->lists = $lists;
    $this->orderService = $orderService;
  }

  /**
   * Show the form for creating a new resource.
   * @param Quotation $quotation
   * @return Response
   */
  public function create(Quotation $quotation)
  {
    $nextQuotationNumber = request()->user()->staff->initials . $quotation->nextNumber();
    $customer = $quotation->customer;
    $itemList = $this->lists->itemCodeDescriptionList();
    $staffList = $this->lists->staffList();
    $customerList = $this->lists->customerWithNumberList();
    $defaultShippingAddress = $quotation->customer->defaultShippingAddress();
    $requiresHiddenPrices = $quotation->customer->requiresHiddenPrices();
    $commissionTypes = $this->lists->commissionTypes();
    $serialNumberPart = null;

    return view(
      'orders::quotations.duplicates.create',
      compact(
        'quotation',
        'itemList',
        'nextQuotationNumber',
        'staffList',
        'customerList',
        'defaultShippingAddress',
        'requiresHiddenPrices',
        'customer',
        'serialNumberPart',
        'commissionTypes'
      )
    );
  }

  /**
   * Store a newly created resource in storage.
   * @param DuplicateQuotationAction $action
   * @param Quotation $quotation
   * @return \Illuminate\Http\RedirectResponse
   */
  public function store(DuplicateQuotationAction $action, Quotation $quotation)
  {
    try {
      return redirect()
        ->route('orderModule.quotations.show', $action->execute($quotation))
        ->with('success', 'Quotation duplicated successfully');

    } catch (\Exception $e) {
      logger()->debug($e->getMessage(), $e->getTrace());

      return back()->with('error', 'There was a problem creating a duplicate for this quotations. Error has been logged for fixing.');
    }
  }

  public function storeForCustomer(Request $request, Quotation $quotation)
  {
    $request->validate([
      'customer_id' => 'required|exists:customers,user_id'
    ]);

    $newQuotation = $this->orderService->makeDuplicateQuotationForCustomer(request('customer_id'));

    return redirect()
      ->route('orderModule.quotations.show', $newQuotation)
      ->with('success', 'Quotation created successfully');
  }
}
