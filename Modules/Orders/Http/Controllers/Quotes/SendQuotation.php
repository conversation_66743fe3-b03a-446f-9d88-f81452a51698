<?php

namespace Modules\Orders\Http\Controllers\Quotes;

use Illuminate\Routing\Controller;
use Modules\Orders\Actions\SendQuotationAction;
use Modules\Orders\Entities\Quotation;
use Modules\Orders\Http\Requests\Quotations\EmailQuotationRequest;

class SendQuotation extends Controller
{
  public function __invoke(EmailQuotationRequest $request, SendQuotationAction $sendQuotationAction, Quotation $quotation)
  {
    try {
      $sendQuotationAction->execute($quotation, $request->all());

      return back()
        ->with('success', 'Quotation sent successfully');
    } catch (\Exception $e) {
      return back()->with('error', $e->getMessage());
    }
  }
}
