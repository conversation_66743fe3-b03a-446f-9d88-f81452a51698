<?php

namespace Modules\Orders\Http\Controllers\Quotes;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Orders\Actions\Quotations\DeleteLineItemAction;
use Modules\Orders\Actions\Quotations\UpdateQuotationItemAction;
use Modules\Orders\Entities\Quotation;
use Modules\Orders\Entities\QuotationLine;
use Modules\Orders\Http\Requests\Quotations\UpdateQuotationLineRequest;

class QuotationLineController extends Controller
{
  use AuthorizesRequests;

  /**
   * Update item details
   *
   * @param Request $request
   * @param UpdateQuotationItemAction $action
   * @param Quotation $quotation
   * @param QuotationLine $item
   * @return Response
   */
  public function update(UpdateQuotationLineRequest $request, UpdateQuotationItemAction $action, Quotation $quotation, QuotationLine $item)
  {
    $action->execute($item, $request->validated());

    if ($request->wantsJson()) {
      return response()->json($item->fresh(), Response::HTTP_CREATED);
    }

    return redirect()
      ->back()
      ->withSuccess('Quotation line item updated successfully');
  }

  public function destroy(DeleteLineItemAction $action, Quotation $quotation, QuotationLine $item)
  {
    try {
      $action->execute($item);

      if (request()->wantsJson()) {
        return response()->json([], Response::HTTP_NO_CONTENT);
      }

      return redirect()->back()->withSuccess('Quotation line item deleted successfully');
    } catch (\Exception $exception) {
      \Log::debug($exception->getMessage());

      if (request()->wantsJson()) {
        return response()->json([], Response::HTTP_NO_CONTENT);
      }

      return redirect()->back()->withError('Quotation line item could not be deleted.');
    }

  }
}
