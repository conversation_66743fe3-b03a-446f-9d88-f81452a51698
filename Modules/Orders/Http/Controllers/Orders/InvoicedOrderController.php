<?php

namespace Modules\Orders\Http\Controllers\Orders;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Modules\Orders\Actions\FilterInvoiceOrdersForPrintOrEmailAction;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;
use Modules\Orders\Entities\OrderReturn;
use Modules\Orders\Jobs\EmailInvoicedOrders;
use Modules\Orders\Jobs\PrintInvoicedOrders;

class InvoicedOrderController extends Controller
{
  use AuthorizesRequests;

  public function index()
  {
    return view('orders::invoiced-orders.index');
  }

  public function store(Request $request, FilterInvoiceOrdersForPrintOrEmailAction $action)
  {
    $request->validate([
      'invoice_email_ids' => 'array|required_without:invoice_print_ids|min:1',
      'invoice_print_ids' => 'array|required_without:invoice_email_ids|min:1',
      'batch_type' => [
        'required',
        Rule::in(['email', 'print'])
      ],
      'body' => [
        'required_if:batch_type,email'
      ]
    ]);

    $requestedBatchPrint = $request->batch_type === 'print';
    $invoiceIds = $requestedBatchPrint ? $request->invoice_print_ids : $request->invoice_email_ids;
    $separatedInvoices = $action->execute($invoiceIds);

    if ($requestedBatchPrint) {
      if (count(Arr::get($separatedInvoices, 'print', [])) === 0) {
        return back()
          ->with('error', 'There are no invoiced orders to print');
      }

      // Process invoices for printing
      dispatch(new PrintInvoicedOrders($separatedInvoices['print']));

      return back()
        ->with('success', 'The system is processing request in the background. You may continue with other tasks.');
    } else {
      if (count($separatedInvoices['email']) === 0) {
        return back()
          ->with('error', 'There are no invoices to email to customers.');
      }

      // Process invoices for batch email
      dispatch(new EmailInvoicedOrders($separatedInvoices['email'], $request->body));

      if (count($invalidAddresses = $separatedInvoices['invalid_address'])) {
        $invalidAddressString = '';

        foreach($invalidAddresses as $address) {
          $invalidAddressString .= $address['name'] . ' - ' . $address['email'] . '; ';
        }

        return back()
          ->with('error', 'Some invoices were not sent because the 1 or more of the emails were found to be invalid. These are: ' .
            $invalidAddressString);
      }

      return back()
        ->with('success', 'The system is sending the emails in the background. You may continue with other tasks.');
    }
  }
}
