<?php

namespace Modules\Orders\Http\Controllers\Orders;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Orders\Actions\SendDeliveryReceiptAction;
use Modules\Orders\Entities\Order;

class SendDelivery extends Controller
{
  public function __invoke(Request $request, SendDeliveryReceiptAction $sendDeliveryReceiptAction, Order $order)
  {
    $details = $request->validate([
      'recipients' => 'required|string|max:255',
      'body' => 'required|string|max:10000',
      'price' => 'nullable|boolean'
    ]);

    $sendDeliveryReceiptAction->execute($order, $details);

    return back()
      ->with('success', 'Tracking number sent successfully');
  }
}
