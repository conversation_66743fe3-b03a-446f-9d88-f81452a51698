<?php

namespace Modules\Orders\Http\Controllers\Orders;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Routing\Controller;
use Modules\Orders\Entities\OrderReturn;

/**
 * Class ApproveOrderReturn
 * @package Modules\Orders\Http\Controllers\Orders
 * @codeCoverageIgnore
 */
class ApproveOrderReturn extends Controller
{
  use AuthorizesRequests;

  public function __invoke(OrderReturn $return)
  {
    $return->approved_by = auth()->id();
    $return->approved_at = now();
    $return->save();

    if ($return->shouldSettleWithReplacement()) {
      $return->itemsReturned()->update([
        'replaced' => true
      ]);
    }

    return back()
      ->withSuccess('Order return has been approved successfully');
  }
}
