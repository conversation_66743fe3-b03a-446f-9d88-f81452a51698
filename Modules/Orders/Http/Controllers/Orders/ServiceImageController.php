<?php

namespace Modules\Orders\Http\Controllers\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Orders\Entities\Order;

/**
 * Class ServiceImageController
 * @package Modules\Orders\Http\Controllers\Orders
 * @codeCoverageIgnore
 */
class ServiceImageController extends Controller
{
  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @param Order $order
   * @return Response
   */
    public function store(Request $request, Order $order)
    {
        $request->validate([
            'service_image' => 'required|array',
            'service_image*' => 'image|max:5120',
            'image_type' => 'required|string|between:5,6'
        ]);

        $order->addMultipleMediaFromRequest(['service_image'])
            ->each(function ($fileAdder) {
                $fileAdder->toMediaCollection(request('image_type') . 'Service');
            });

        return back()
            ->with('success', 'Service image uploaded successfully');
    }
}
