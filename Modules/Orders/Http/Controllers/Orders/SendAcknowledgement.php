<?php

namespace Modules\Orders\Http\Controllers\Orders;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Mail;
use Modules\Orders\Actions\SendAcknowledgementAction;
use Modules\Orders\Emails\Orders\OrderAcknowledged;
use Modules\Orders\Entities\Order;

class SendAcknowledgement extends Controller
{
  public function __invoke(Request $request, SendAcknowledgementAction $sendAcknowledgementAction, Order $order)
  {
    $request->validate(['recipients' => 'required|string|max:255', 'body' => 'required|string|max:1000']);

    $sendAcknowledgementAction->execute($order, $request->all());

    return back()
      ->with('success', 'Acknowledgement email sent successfully');
  }
}
