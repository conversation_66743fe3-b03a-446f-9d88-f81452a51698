<?php

namespace Modules\Orders\Http\Controllers\Orders;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;
use Modules\Orders\Events\Orders\OrderUpdated;
use Modules\Orders\Notifications\UserCustomNotification;
use Modules\Orders\Services\BackOrderSearchService;
use Modules\Orders\Services\OrderService;

class BackOrderController extends Controller
{
  use AuthorizesRequests;

  protected $orderService;

  public function __construct(OrderService $orderService)
  {
    $this->orderService = $orderService;
  }

  /**
   * Display a listing of the resource.
   * @param BackOrderSearchService $searchService
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index(BackOrderSearchService $searchService)
  {
    $this->authorize('view order', Order::class);

    return view('orders::back-orders.index', [
      'orders' => $searchService->find(),
      'searchFields' => $searchService->searchFields(),
      'searchConditions' => $searchService->conditions(),
    ]);
  }

  /**
   * Show the form for creating a new resource.
   * @param Order $order
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function create(Order $order)
  {
    $this->authorize('update order', Order::class);

    return view('orders::back-orders.create', compact('order'));
  }

  /**
   * Store a newly created resource in storage.
   * @param Request $request
   * @param Order $order
   * @return \Illuminate\Http\RedirectResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function store(Request $request, Order $order)
  {
    $this->authorize('update order', Order::class);

    $request->validate([
      'item_id' => 'required|array|min:1',
      'quantity_available' => 'required|array',
      'quantity_available.*' => 'nullable|numeric'
    ]);


    $backOrderItems = []; // The items to be placed on back order
    $items = $order->items->reject(function (OrderLine $item) {
      return $item->isSerialNumberItem();
    })->values();

    $selectedColumns = [
      'item_id',
      "type",
      "code",
      "description",
      "quantity",
      "quantity_original",
      "quantity_available",
      "price",
      "cost",
      "weight",
      "build_instructions",
      "location",
      "number",
      "commission_type",
    ];

    // The items to be updated with back order details
    $items->each(function (OrderLine $item, $key) use (&$backOrderItems, $selectedColumns) {
      $availableQuantity = request('quantity_available')[$key] * 1;

      if ($availableQuantity < ($item->quantity * 1)) {
        $item->quantity_available = $availableQuantity;
        $item->quantity_original = $item->quantity * 1;
        $item->quantity -= $availableQuantity;
        $item->back_ordered = true;

        $backOrderItem = Arr::only($item->toArray(), $selectedColumns);
        $backOrderItem['quantity'] = $backOrderItem['quantity_original'] - floatval($backOrderItem['quantity_available']);
        $backOrderItem['quantity_available'] = null;
        $backOrderItem['quantity_original'] = null;
        $backOrderItems[] = $backOrderItem;
        $item->save();
      }
    });

    if (empty($backOrderItems)) {
      return back()
        ->with('error', 'Could not create backorder because all items are available. To create a back order on an item, the Quantity available must be less then the Quantity ordered.');
    }

    // We create the back order
    $nextOrderNumber = $order->nextOrderNumber();
    $backOrder = $order->replicate();
    $backOrder->number = $nextOrderNumber;
    $backOrder->full_number = auth()->user()->staff->initials . $nextOrderNumber;
    $backOrder->parent_id = $order->id;
    $backOrder->created_by = auth()->user()->id;
    $backOrder->created_at = now();
    $backOrder->status = Order::STATUS_CREATED;

    // Update options
    if ($order->customer->requiresSerialNumber() &&
      ! empty($serialNumberItem = $this->orderService->getSerialNumberItem())) {
      // We need to get a fresh WSN part as the one from order may have been updated with that order's specific details
      $backOrderItems[] = $serialNumberItem;
    }

    $backOrderOptions = $order->options;
    $backOrderOptions['statuses'] = [
      'created' => [
        'name' => 'created',
        'created_at' => now()->format('m-d-y H:i:s'),
        'created_by' => auth()->id(),
        'previous' => null,
        'note' => ''
      ]
    ]; // Reset status details


    $backOrder->options = $backOrderOptions;
    $backOrder->save();

    $backOrder->items()->createMany($backOrderItems);

    event(new OrderUpdated($backOrder->fresh()));
    event(new OrderUpdated($order));

    $request->user()->notify(
      new UserCustomNotification([
        'type' => 'Back Order',
        'instruction' => 'Back Order has been created.'
      ], $backOrder)
    );


    return redirect()
      ->route('orderModule.orders.show', $backOrder)
      ->with('success', 'Back order created successfully');
  }

  /**
   * Show the specified resource.
   * @param Order $order
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   * @codeCoverageIgnore Back order is updated via main order page
   */
  public function show(Order $order)
  {
    $this->authorize('update order', Order::class);

    return view('orders::back-orders.show', compact('order'));
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param Order $order
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   * @codeCoverageIgnore Back order is updated via main order page
   */
  public function update(Request $request, Order $order)
  {
    $this->authorize('update order', Order::class);

    $backOrderDetails = $order->back_order;
    $backOrderDetails['completed_at'] = now();
    $backOrderDetails['completed_by'] = $request->user()->name;
    $backOrderDetails['status'] = 'completed';
    $options = $order->options;
    $options['back_order'] = $backOrderDetails;
    $order->options = $options;
    $order->save();

    return back()
      ->with('success', 'Back order marked as completed.');
  }

  /**
   * Remove the specified resource from storage.
   * @param Order $order
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   * @codeCoverageIgnore Back order is updated via main order page
   */
  public function destroy(Order $order)
  {
    $this->authorize('update order', Order::class);

    $backOrderDetails = $order->back_order;
    $backOrderDetails['cancelled'] = true;
    $backOrderDetails['cancelled_at'] = now();
    $backOrderDetails['cancelled_by'] = request()->user()->name;
    $backOrderDetails['status'] = 'canceled';
    $options = $order->options;
    $options['back_order'] = $backOrderDetails;
    $order->options = $options;
    $order->save();

    return back()
      ->with('success', 'Back order cancelled successfully. It will be deleted in 24hrs.');
  }
}
