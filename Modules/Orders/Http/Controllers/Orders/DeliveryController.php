<?php

namespace Modules\Orders\Http\Controllers\Orders;

use App\Services\ListService;
use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Routing\Controller;
use Modules\Orders\Actions\PrintDeliveryAction;
use Modules\Orders\Entities\Order;
use Modules\Orders\Notifications\OrderSpecialInstruction;

class DeliveryController extends Controller
{
  use AuthorizesRequests;

  protected $lists;

  public function __construct(ListService $lists)
  {
    $this->authorizeResource(Order::class, 'order');
    $this->lists = $lists;
  }

  /**
   * Show the specified resource.
   * @param Order $order
   * @return Response
   */
  public function show(Order $order)
  {
    $pricingCompleted = $order->pricingCompleted();

    return view(
      'orders::orders.delivery',
      compact('order', 'pricingCompleted')
    );
  }

  /**
   * Download pdf for printing.
   *
   * @param Order $order
   * @return Response
   */
  public function download(Order $order)
  {
    if ($order->notInvoiced() && $order->requiresPickup() && empty($order->shipping_pickup_by)) {
      return back()
        ->with('error', 'This order is to ship via: ' . $order->shipping_via . '. However, no pickup representative has been specified. Please specify the pickup representative on the order form and try again.');
    }

    if ($order->notDelivered()) {
      $this->deliverOrder($order);

      // We check if the customer has a special instruction
      // Then we check if the notification was not already sent. This could have been done using the Update Status modal
      if (! empty($order->customer->special_instructions) &&
        empty(DatabaseNotification::where('data->order->id', $order->id)->first())) {
        request()->user()->notify(new OrderSpecialInstruction($order));
      }
    }

    log_activity($order, 'printed delivery receipt');

    $price = request('price');

    return app()->make('snappy.pdf.wrapper')
      ->loadView('orders::orders.print-delivery', ['order' => $order, 'price' => $price])
      ->inline('delivery-order-' . $order->number . '.pdf');
  }

  /**
   * Mark the order as delivered
   *
   * @param Order $order
   * @return bool
   */
  protected function deliverOrder(Order $order)
  {
    $currentDateTime = now()->format('m-d-Y');
    // Update delivery information
    $order->delivered_at = now();
    $order->delivered_by = auth()->user()->name;
    $order->delivered = true;
    $options = $order->options;
    $options['delivery']['delivered_at'] = $currentDateTime; // TODO: Deprecate saving delivery in options
    // Update status information
    $options['statuses'][Order::STATUS_DELIVERED] = [
      'name' => Order::STATUS_DELIVERED,
      'created_at' => $currentDateTime,
      'created_by' => auth()->id(),
      'previous' => $order->status,
    ];

    $order->options = $options;
    $order->status = Order::STATUS_DELIVERED;

    return $order->save();
  }
}
