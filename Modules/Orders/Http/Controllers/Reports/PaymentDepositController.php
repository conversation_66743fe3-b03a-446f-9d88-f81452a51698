<?php

namespace Modules\Orders\Http\Controllers\Reports;

use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Payment;
use Modules\Orders\Entities\Reports\PaymentDepositReport;

class PaymentDepositController extends Controller
{
  use AuthorizesRequests;

  public function create()
  {
    return view('orders::reports.payment-deposits.create', [
      'startReportDate' => now()->toDateString(),
      'endReportDate' => now()->toDateString()
    ]);
  }

  public function add(Request $request)
  {
    $request->validate([
      'report_start_date' => 'required|date'
    ]);

    return view('orders::reports.payment-deposits.add');
  }

  public function store(Request $request)
  {
    $record = $request->validate([
      'created_at' => 'required|date',
      'amount' => 'required|numeric',
      'check_name' => 'required|string|max:255',
      'check_date' => 'nullable|date',
      'check_number' => 'required|string|max:50',
      'bank_id' => 'nullable|string|max:50'
    ]);

    PaymentDepositReport::create($record);

    return redirect()
      ->route(
        'orderModule.reports.paymentDeposits.show',
        [
          'report_start_date' => $request->report_start_date,
          'report_end_date' => $request->report_end_date,
        ]
      )
      ->withSuccess('Payment deposit added successfully');
  }

  public function show(Request $request)
  {
    $request->validate([
      'report_start_date' => 'required|date',
      'report_end_date' => 'required|date'
    ]);

    return view('orders::reports.payment-deposits.show', [
      'paymentDeposits' => $this->getPaymentDeposits(),
      'formattedStartDate' => Carbon::parse($request->report_start_date)->format('F d, Y'),
      'formattedEndDate' => Carbon::parse($request->report_end_date)->format('F d, Y')
    ]);
  }

  public function edit(PaymentDepositReport $deposit)
  {
    return view('orders::reports.payment-deposits.edit', [
      'deposit' => $deposit,
    ]);
  }

  public function update(Request $request, PaymentDepositReport $deposit)
  {
    $deposit->update(
      $request->validate([
        'created_at' => 'required|date',
        'amount' => 'required|numeric',
        'check_name' => 'required|string|max:255',
        'check_date' => 'nullable|date',
        'check_number' => 'required|string|max:50',
        'bank_id' => 'nullable|string|max:50'
      ])
    );


    return redirect()
      ->route(
        'orderModule.reports.paymentDeposits.show',
        [
          'report_start_date' => $request->report_start_date,
          'report_end_date' => $request->report_end_date
        ]
      )
      ->withSuccess('Payment deposit updated successfully');
  }

  /**
   * @param Request $request
   * @param $depositId
   * @return mixed
   */
  public function destroy(Request $request, $depositId)
  {

    try {
      if ($request->type === 'custom') {
        PaymentDepositReport::findOrFail($depositId)->delete();
      } else {
        $payment = Payment::findOrFail($depositId);

        $payment->update(['in_payment_deposits' => false]);
      }

      return back()->withSuccess('Payment deposit deleted successfully');
    } catch (\Exception $e) {
      \Log::debug($e->getMessage());
      return back()->withSuccess('There was a problem deleting the payment record');
    }
  }

  public function print(Request $request)
  {
    $request->validate([
      'report_start_date' => 'required|date',
      'report_end_date' => 'required|date'
    ]);

    $payments = $this->getPaymentDeposits();
    $formattedStartDate = Carbon::parse($request->report_start_date)->format('F d, Y');
    $formattedEndDate = Carbon::parse($request->report_end_date)->format('F d, Y');

    $pdf = app()->make('snappy.pdf.wrapper');

    return $pdf->loadView('orders::reports.payment-deposits.print', compact('payments', 'formattedStartDate', 'formattedEndDate'))
      ->setPaper('a4', 'landscape')
      ->setOptions([
        'header-right' => 'Page [page] of [topage]',
        'header-font-size' => '9'
      ])->inline('payment-deposits-' . $request->report_start_date . '-to-' . $request->report_end_date . '.pdf');
  }

  protected function getPaymentDeposits()
  {
    $customDepositRecords = DB::table('payment_deposit_reports')
      ->select(['id', 'check_number', 'check_name', 'bank_id', 'amount', 'type'])
      ->selectRaw('DATE_FORMAT(check_date, "%m/%d/%y") as check_date, DATE_FORMAT(created_at, "%m/%d/%y") as payment_date')
      ->whereBetween('created_at', [
        request('report_start_date'),
        Carbon::parse(request('report_end_date'))->endOfDay()->toDateTimeString()
      ])
      ->whereNull('deleted_at');

    return DB::table('payments')
      ->select(['id', 'reference as check_number', 'check_name', 'bank_id', 'amount', 'type'])
      ->selectRaw('DATE_FORMAT(check_date, "%m/%d/%y") as check_date, DATE_FORMAT(created_at, "%m/%d/%y") as payment_date')
      ->whereMethod('check')
      ->whereBetween('created_at', [
        request('report_start_date'),
        Carbon::parse(request('report_end_date'))->endOfDay()->toDateTimeString()
      ])
      ->whereNull('deleted_at')
      ->where('in_payment_deposits', true)
      ->unionAll($customDepositRecords)
      ->orderBy('payment_date')
      ->get();
  }
}
