<?php

namespace Modules\Orders\Http\Controllers\Reports;

use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Orders\Entities\Commission;
use Modules\Orders\Entities\Order;

class ReferralCommissionReportController extends Controller
{
  use AuthorizesRequests;

  /**
   * Display a listing of the resource.
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index()
  {
    $this->authorize('view report', Order::class);

    return view('orders::reports.commissions.referrals.index');
  }

  public function print()
  {
    $carbonStartDate = Carbon::parse(request('start_date'));
    $carbonEndDate = Carbon::parse(request('end_date'));
    $startDate = $carbonStartDate->toDateTimeString();
    $endDate = $carbonEndDate->endOfDay()->toDateTimeString();
    $commissionGroupsQuery = DB::table('commissions as c')
      ->selectRaw('c.id,
        c.order_id,
        c.commissionable_id,
        date_format(o.created_at, "%m/%d/%y") AS order_date,
        o.purchase_order_number,
        c.quantity,
        c.description,
        c.part_number,
        c.price,
        c.price * c.quantity AS amount,
        c.percentage,
        round(c.price * c.quantity * c.percentage, 2) AS commission,
        date_format(o.invoiced_at, "%m/%d/%y") AS final_date,
        o.number AS invoice_number,
        o.full_number AS order_full_number,
        u.name AS customer_name')
      ->join('orders as o', 'o.id', '=', 'c.order_id')
      ->join('users as u', 'u.id', '=', 'c.commissionable_id')
      ->where('c.commission_type', Commission::TYPE_REFERRAL)
      ->where('c.created_at', '>=', $startDate)
      ->where('c.created_at', '<=', $endDate);

    if (! empty(request('customer_id'))) {
      $commissionGroupsQuery = $commissionGroupsQuery->where('c.commissionable_id', request('customer_id'));
    }

    $commissionGroups = $commissionGroupsQuery->orderBy('c.order_id')
      ->get()
      ->groupBy('order_id')
      ->all();
    $formattedStartDate = $carbonStartDate->format('m/d/y');
    $formattedEndDate = $carbonEndDate->format('m/d/y');

    $pdf = PDF::loadView('orders::reports.commissions.referrals.print', compact('commissionGroups', 'formattedStartDate', 'formattedEndDate'))
      ->setPaper('a4', 'landscape')
      ->setOptions([
        'margin-bottom' => '0mm',
        'margin-right' => '0mm',
        'margin-top' => '0mm',
        'margin-left' => '0mm',
        'images' => true,
        'isPhpEnabled' => true
      ]);

    return $pdf->stream('commission-report-referrals-' . now()->toDateString() . '.pdf');
  }
}
