<?php

namespace Modules\Orders\Http\Controllers\Reports;

use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Payment;
use Modules\Orders\Services\PaymentReceiptGenerator;

class PaymentReceiptController extends Controller
{
  use AuthorizesRequests;

  /**
   * @var mixed
   */
  protected $paymentTotal;
  protected array $paymentGroupTotals;

  public function create()
  {
    return view('orders::reports.payment-receipts.create', [
      'startReportDate' => now()->toDateString(),
      'endReportDate' => now()->toDateString()
    ]);
  }

  public function show(Request $request)
  {
    $request->validate([
      'start_report_date' => 'required|date',
      'end_report_date' => 'required|date'
    ]);

    return view('orders::reports.payment-receipts.show');
  }

  public function print(Request $request)
  {
    $request->validate([
      'start_report_date' => 'required|date',
      'end_report_date' => 'required|date'
    ]);

    $paymentReceipts = $this->getPaymentReceipts(
      $request->start_report_date,
      $request->end_report_date
    );
    $paymentGroupTotals = $this->paymentGroupTotals;
    $paymentTotal = $this->paymentTotal;
    $reportStartDate = Carbon::parse($request->start_report_date);
    $formattedStartDate = $reportStartDate->format('F d, Y');
    $formattedPrintDate = $reportStartDate->format('F d, Y \a\t g:i A');
    $reportEndDate = Carbon::parse($request->end_report_date);
    $formattedEndDate = $reportEndDate->format('F d, Y');

    $pdf = app()->make('snappy.pdf.wrapper');

    return $pdf->loadView('orders::reports.payment-receipts.print', compact(
      'paymentReceipts',
      'paymentGroupTotals',
      'paymentTotal',
      'formattedEndDate',
      'formattedStartDate',
      'formattedPrintDate'))
      ->setPaper('a4', 'landscape')
      ->setOptions([
        'header-right' => 'Page [page] of [topage]',
        'header-font-size' => '9'
      ])->inline('payment-receipt-' . $request->report_date . '.pdf');
  }

  protected function getPaymentReceipts($startDate, $endDate): \Illuminate\Support\Collection
  {
    $payments = Payment::with('invoices', 'customer.user')
      ->whereBetween('created_at', [
        $startDate,
        Carbon::parse($endDate)->endOfDay()->toDateTimeString()
      ])
      ->whereNull('deleted_at')
      ->get()
      ->sortBy(function ($payment) {
        return $payment->customer->name;
      });

    $paymentGroups = $payments->groupBy('method')
      ->mapWithKeys(function ($groupPayments, $groupName) {
        return [
          strtolower($groupName) => $groupPayments->sum('amount')
        ];
      });
    $this->paymentTotal = $paymentGroups->sum();
    $paymentGroups->put('card', $paymentGroups->filter(function ($amount, $method) {
      return Str::contains(strtolower($method), 'card');
    })->sum());
    $this->paymentGroupTotals = $paymentGroups->all();

    return $payments;
  }
}
