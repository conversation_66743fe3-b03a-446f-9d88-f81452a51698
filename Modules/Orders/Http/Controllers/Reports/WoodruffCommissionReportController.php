<?php

namespace Modules\Orders\Http\Controllers\Reports;

use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Carbon;
use Modules\Orders\Entities\Commission;
use Modules\Orders\Entities\Order;

class WoodruffCommissionReportController extends Controller
{
  use AuthorizesRequests;

  /**
   * Display a listing of the resource.
   * @return Response
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index()
  {
    $this->authorize('view report', Order::class);

    return view('orders::reports.commissions.woodruff.index');
  }

  public function print()
  {
    $commissionDiscount = settings('global.commissions.types.ws.value', 0);
    $commissionType = (bool) request('standard') ? Commission::TYPE_WS : Commission::TYPE_WS_CS;

    $carbonStartDate = Carbon::parse(request('start_date'));
    $carbonEndDate = Carbon::parse(request('end_date'));
    $startDate = $carbonStartDate->toDateTimeString();
    $endDate = $carbonEndDate->endOfDay()->toDateTimeString();
    $commissionGroups = Commission::where('commission_type', $commissionType)
      ->whereBetween('created_at', [
        $startDate,
        Carbon::parse($endDate)->endOfDay()
      ])
      ->orderBy('order_id')
      ->get()
      ->groupBy('order_id');
    $formattedStartDate = $carbonStartDate->format('m/d/y');
    $formattedEndDate = $carbonEndDate->format('m/d/y');

    $pdf = PDF::loadView('orders::reports.commissions.woodruff.print', compact('commissionGroups', 'formattedStartDate', 'formattedEndDate', 'commissionDiscount'))
      ->setPaper('a4', 'landscape')
      ->setOptions([
        'margin-bottom' => '0mm',
        'margin-right' => '0mm',
        'margin-top' => '0mm',
        'margin-left' => '0mm',
        'images' => true,
        'isPhpEnabled' => true
      ]);

    return $pdf->stream('commission-report-woodruff-' . now()->toDateString() . '.pdf');
  }
}
