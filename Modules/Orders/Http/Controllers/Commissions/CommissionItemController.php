<?php

namespace Modules\Orders\Http\Controllers\Commissions;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Modules\Orders\Entities\Commission;
use Modules\Orders\Entities\CommissionItem;
use Modules\Orders\Entities\Order;
use Modules\Orders\Events\Orders\OrderUpdated;

class CommissionItemController extends Controller
{
  use AuthorizesRequests;

  /**
   * Display a listing of the resource.
   * @return Response
   */
  public function index()
  {
    $this->authorize('view commission');

    $commissions = Commission::with('order.createdBy.staff', 'commissionable')
      ->whereYear('created_at', date('Y'))
      ->latest()
      ->get();

    $quarterlyCommissionTotals = $commissions->groupBy(function ($commission) {
      return $commission->created_at->quarter;
    })
      ->mapWithKeys(function ($quarterCommissions, $key) {
        return [
          $key => $quarterCommissions->sum('amount')
        ];
      })
      ->union([1 => 0, 2 => 0, 3 => 0, 4 => 0])
      ->sortKeys();

    return view('orders::commissions.index', compact('commissions', 'quarterlyCommissionTotals'));
  }

  /**
   * Show the specified resource.
   * @param Commission $commission
   * @return Response
   */
  public function show(Commission $commission)
  {
    $this->authorize('view commission');

    return view('orders::commissions.show', compact('commission'));
  }

  /**
   * Show the form for editing the specified resource.
   * @param Commission $commission
   * @return Response
   */
  public function edit(Commission $commission)
  {
    return view('orders::commissions.edit', compact('commission'));
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param int $id
   * @return Response
   */
  public function update(Request $request, CommissionItem $commissionItem)
  {
    $updatedRecord = $request->validate([
      'quantity' => 'required|numeric',
      'part_number' => 'required|string|max:100',
      'description' => 'required|string|max:500',
      'price' => 'required|numeric',
      'commission' => 'required|numeric',
      'percentage' => 'nullable|numeric',
    ]);

    $commissionItem->update($updatedRecord);

    return back()->withSuccess('Commission item updated successfully.');
  }

  /**
   * Remove the specified resource from storage.
   * @param Commission $commission
   * @return Response
   */
  public function destroy(CommissionItem $commissionItem)
  {
    try {
      if ($commissionItem->commissionable->items->count() === 1) {
        /**
         * We are deleting the last item on a commission,
         * so we first delete the entire commission record
         * then delete the commission item record itself.
         * The DB won't do this for us since there's no FK constraint enforced.
         */
        $commissionItem->commissionable->delete();
        $commissionItem->delete();
      } else {
        $commissionItem->delete();
      }


      return back()
        ->with('success', 'Commission item record deleted successfully');
    } catch (\Exception $e) {
      Log::debug($e->getMessage());

      return back()
        ->with('error', 'Commission item record could not be deleted');
    }
  }
}
