<?php

namespace Modules\Orders\Http\Controllers\Commissions;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Modules\Orders\Entities\Commission;
use Modules\Orders\Entities\Order;
use Modules\Orders\Events\Orders\OrderUpdated;

class CommissionController extends Controller
{
  use AuthorizesRequests;

  /**
   * Display a listing of the resource.
   * @return Response
   */
  public function index()
  {
    $this->authorize('view commission');

    $commissions = Commission::with('order.createdBy.staff', 'commissionable')
      ->latest()
      ->get();

    return view('orders::commissions.index', compact('commissions'));
  }

  public function create()
  {
    return view('orders::commissions.create');
  }

  /**
   * Show the specified resource.
   * @param Commission $commission
   * @return Response
   */
  public function show(Commission $commission)
  {
    $this->authorize('view commission');

    return view('orders::commissions.show', compact('commission'));
  }

  /**
   * Show the form for editing the specified resource.
   * @param Commission $commission
   * @return Response
   */
  public function edit(Commission $commission)
  {
    return view('orders::commissions.edit', compact('commission'));
  }

  /**
   * Update the specified resource in storage.
   * @param Request $request
   * @param int $id
   * @return Response
   */
  public function update(Request $request, Commission $commission)
  {
    //
  }

  /**
   * Remove the specified resource from storage.
   * @param Commission $commission
   * @return \Illuminate\Http\RedirectResponse
   */
  public function destroy(Commission $commission)
  {
    try {
      $commission->delete();

      return back()
        ->with('success', 'Commission record deleted successfully');
    } catch (\Exception $e) {
      \Log::debug($e->getMessage());

      return back()
        ->with('error', 'Commission record could not be deleted');
    }
  }
}
