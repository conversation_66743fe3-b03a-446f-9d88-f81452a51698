<?php


namespace Modules\Orders\ViewModels;


use Illuminate\Support\Str;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Payment;

/**
 * Class ReceivableInvoiceViewModel
 * @package Modules\Orders\ViewModels
 * @codeCoverageIgnore
 */
class ReceivableInvoiceViewModel
{
    /**
     * @var Order
     */
    protected $invoice;

    public $timestamp;

    public function __construct(Order $invoice)
    {
        $this->invoice = $invoice;
        $this->make();
    }

    public function make()
    {
        $this->timestamp = $this->invoice->created_at->timestamp;
        $this->type = 'Invoice';
        $this->record_id = $this->invoice->id;
        $this->customer_account_number = $this->invoice->customer->account_number;
        $this->customer_id = $this->invoice->customer->user_id;
        $this->customer_name = $this->invoice->customer->name;
        $this->purchase_order_number = $this->invoice->purchase_order_number;
        $this->reference_number = $this->invoice->number;
        $this->balance_due = number_format($this->invoice->negative_balance_utilized ? 0 : $this->invoice->balance, 2);
        $this->date = $this->invoice->formatted_invoice_date;
        $this->date_log = []; // $this->invoice->payments->map(function (Payment $payment) {
            //return $payment->log_statement;
        //})->all();
        $this->debit = number_format($this->invoice->total_amount, 2);
        $this->credit = null;
        $this->payment_method = '';
        $this->amount_paid = number_format($this->invoice->amount_paid, 2);
        $this->age = $this->invoice->age;
        $this->payment_terms = $this->invoice->payment_terms;
        $this->notes = $this->invoice->invoice_notes;
        $this->status = $this->invoice->invoice_payment_status;
        $this->reference = '';
    }
}
