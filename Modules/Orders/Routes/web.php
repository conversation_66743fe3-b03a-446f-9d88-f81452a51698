<?php

use Illuminate\Support\Facades\Route;
use Modules\Orders\Http\Controllers\Commissions\CommissionItemController;
use Modules\Orders\Http\Controllers\Orders\AssignmentController;
use Modules\Orders\Http\Controllers\Orders\AttachmentController;
use Modules\Orders\Http\Controllers\Quotes\AttachmentController as QuoteAttachmentController;
use Modules\Orders\Http\Controllers\Orders\BackOrderController;
use Modules\Orders\Http\Controllers\Orders\CompletedOrderController;
use Modules\Orders\Http\Controllers\Orders\DeliveryController;
use Modules\Orders\Http\Controllers\Orders\InvoiceController;
use Modules\Orders\Http\Controllers\Orders\OrderController;
use Modules\Orders\Http\Controllers\Orders\ServiceImageController;
use Modules\Orders\Http\Controllers\Orders\UnassignedOrderController;
use Modules\Orders\Http\Controllers\Quotes\DuplicateController;
use Modules\Orders\Http\Controllers\Reports\CommercialROCommissionReportController;
use Modules\Orders\Http\Controllers\Reports\CustomerInvoiceSummaryController;
use Modules\Orders\Http\Controllers\Reports\PaymentDepositController;
use Modules\Orders\Http\Controllers\Reports\PaymentReceiptController;
use Modules\Orders\Http\Controllers\Reports\PendingOrderController;
use Modules\Orders\Http\Controllers\Reports\ReferralCommissionReportController;
use Modules\Orders\Http\Controllers\Reports\SalesTaxReportController;
use Modules\Orders\Http\Controllers\Reports\WesCommissionReportController;
use Modules\Orders\Http\Controllers\Reports\WoodruffCommissionReportController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('orders')
  ->middleware(['auth', 'staff'])
  ->name('orderModule.')
  ->group(function () {
    // Mails
    Route::post('/orders/{order}/send-invoice', 'Orders\SendInvoice')
      ->name('orders.sendInvoice');
    Route::post('/orders/{order}/send-delivery', 'Orders\SendDelivery')
      ->name('orders.sendDelivery');
    Route::post('/orders/{order}/send-acknowledgement', 'Orders\SendAcknowledgement')
      ->name('orders.sendAcknowledgement');
    Route::get('zipped-invoiced-orders', [\Modules\Orders\Http\Controllers\Orders\ZippedInvoicedOrderController::class, 'index'])
      ->name('zipped-invoiced-orders.index');
    Route::get('zipped-invoiced-orders/{file}', [\Modules\Orders\Http\Controllers\Orders\ZippedInvoicedOrderController::class, 'download'])
      ->name('zipped-invoiced-orders.download');
    Route::delete('zipped-invoiced-orders/{file}', [\Modules\Orders\Http\Controllers\Orders\ZippedInvoicedOrderController::class, 'destroy'])
      ->name('zipped-invoiced-orders.destroy');
    Route::resource('invoiced-orders', '\Modules\Orders\Http\Controllers\Orders\InvoicedOrderController');
    Route::get('/invoices/pending', [\Modules\Orders\Http\Controllers\Orders\PendingInvoiceController::class, 'index'])
      ->name('pendingInvoices.index');
    Route::get('/invoices/recent', [\Modules\Orders\Http\Controllers\Orders\RecentInvoiceController::class, 'index'])
      ->name('invoices.recent.index');
    Route::get('/invoices', [InvoiceController::class, 'index'])
      ->name('invoices.index');
    Route::get('/orders/{order}/invoice', [InvoiceController::class, 'show'])
      ->name('orders.showInvoice');
    Route::put('/orders/{order}/invoice', 'Orders\UpdateInvoiceNote')
      ->name('orders.updateInvoice');
    Route::get('/orders/{order}/invoice-download', [InvoiceController::class, 'download'])
      ->name('orders.downloadInvoice');
    Route::get('/orders/{order}/delivery', [DeliveryController::class, 'show'])
      ->name('orders.showDelivery');
    Route::get('/orders/{order}/download-delivery-receipt', [DeliveryController::class, 'download'])
      ->name('orders.downloadDeliveryReceipt');
    Route::get('/orders/{order}/print-acknowledgement', 'Orders\PrintAcknowledgement')
      ->name('orders.print.acknowledgement');
    Route::get('/orders/{order}/print-packing-list/{type}', 'Orders\PrintPackingList')
      ->name('orders.print.packingList');
    Route::get('/orders/{order}/print-pallet-label', 'Orders\PrintPalletLabels')
      ->name('orders.print.palletLabels');
    Route::get('/order/{order}/order-download', [OrderController::class, 'download'])
      ->name('orders.downloadOrder');
    Route::put('orders/{order}/cancel', 'Orders\CancelOrder')->name('orders.cancel');
    Route::put('orders/{order}/status', 'Orders\UpdateOrderStatus')->name('orders.updateStatus');
    Route::post('orders/{order}/attachments/create', [AttachmentController::class, 'store'])
      ->name('orders.attachments.store');
    Route::delete('attachments/{attachment}', [AttachmentController::class, 'destroy'])
      ->name('attachments.destroy');
    Route::post('orders/{order}/images/create', [ServiceImageController::class, 'store'])
      ->name('orders.images.store');
    Route::delete('orders/{order}/images/{image}', [ServiceImageController::class, 'destroy'])
      ->name('orders.images.destroy');
    Route::get('orders/archived', 'Orders\ListArchivedOrders')
      ->name('orders.archived.index');
    Route::get('back-orders', [BackOrderController::class, 'index'])
      ->name('backOrders.index');
    Route::get('back-orders/{order}/create', [BackOrderController::class, 'create'])
      ->name('backOrders.create');
    Route::post('back-orders/{order}/create', [BackOrderController::class, 'store'])
      ->name('backOrders.store');
    Route::get('back-orders/{order}', [BackOrderController::class, 'show'])
      ->name('backOrders.show');
    Route::put('back-orders/{order}', [BackOrderController::class, 'update'])
      ->name('backOrders.update');
    Route::delete('back-orders/{order}', [BackOrderController::class, 'destroy'])
      ->name('backOrders.destroy');
    Route::get('completed-orders', [CompletedOrderController::class, 'index'])
      ->name('completed.index');
    Route::get('unassigned-orders', [UnassignedOrderController::class, 'index'])
      ->name('unassigned.index');
    Route::put('orders/{order}/locked-invoice', 'Orders\UpdateLockedInvoice')
      ->name('invoices.locked.update');
    Route::resource('orders', 'Orders\OrderController');
    Route::put('assignments/{assignment}/complete', [AssignmentController::class, 'complete'])
      ->name('assignments.complete');
    Route::post('orders/{order}/duplicates/customer', [Modules\Orders\Http\Controllers\Orders\DuplicateController::class, 'storeForCustomer'])
      ->name('orders.duplicates.storeForCustomer');
    Route::put('returns/{return}/approve', 'Orders\ApproveOrderReturn')
      ->name('returns.approve');
    Route::resource('orders.items', 'Orders\OrderLineController');
    Route::resource('orders.duplicates', 'Orders\DuplicateController');
    Route::resource('orders.assignments', 'Orders\AssignmentController');
    Route::resource('orders.payments', 'Orders\PaymentController');
    Route::resource('orders.returns', 'Orders\OrderReturnController');
    Route::resource('returns', 'Orders\ReturnsController');
    Route::post('quotations/{quotation}/attachments/create', [QuoteAttachmentController::class, 'store'])
      ->name('quotations.attachments.store');
    Route::post('quotations/{quotation}/make-order', 'Quotes\ConvertToOrder')->name('quotations.makeOrder');
    Route::get('quotations/{quotation}/duplicates/create', [DuplicateController::class, 'create'])
      ->name('quotations.duplicates.create');
    Route::post('quotations/{quotation}/duplicates', [DuplicateController::class, 'store'])
      ->name('quotations.duplicates.store');
    Route::post('quotations/{quotation}/duplicates/customer', [DuplicateController::class, 'storeForCustomer'])
      ->name('quotations.duplicates.storeForCustomer');
    Route::post('/quotations/{quotation}/send', 'Quotes\SendQuotation')
      ->name('quotations.send');
    Route::get('/quotations/{quotation}/print', 'Quotes\PrintQuotation')
      ->name('quotations.print');

    Route::resource('quotations.items', 'Quotes\QuotationLineController');
    Route::resource('quotations', 'Quotes\QuotationController');
    Route::resource('levels', 'Levels\PriceLevelController');
    Route::resource('levels.categories', 'Levels\PriceLevelCategoryController');

    // Commissions
    Route::resource('commissions', 'Commissions\CommissionController');
    Route::resource('decommissions', 'Commissions\DecommissionController');

    Route::delete('notifications/selected', 'Orders\DeleteSelectedNotifications')
      ->name('notifications.destroy.selected');
    Route::resource('notifications', 'Orders\NotificationController');
    Route::get('reports/sales-tax', [SalesTaxReportController::class, 'index'])
      ->name('reports.salesTax.index');
    Route::get('reports/sales-tax/create', [SalesTaxReportController::class, 'create'])
      ->name('reports.salesTax.create');
    Route::post('reports/sales-tax', [SalesTaxReportController::class, 'store'])
      ->name('reports.salesTax.store');
    Route::get('reports/sales-tax/{report}', [SalesTaxReportController::class, 'show'])
      ->name('reports.salesTax.show');
    Route::get('reports/sales-tax/{report}/edit', [SalesTaxReportController::class, 'edit'])
      ->name('reports.salesTax.edit');
    Route::put('reports/sales-tax/{report}', [SalesTaxReportController::class, 'update'])
      ->name('reports.salesTax.update');
    Route::get('reports/sales-tax/{report}/summary-report', 'Reports\SalesTaxReportController@showSummaryReport')
      ->name('reports.salesTax.showSummaryReport');
    Route::get('reports/sales-tax/{report}/print-sales-report', 'Reports\SalesTaxReportController@printSalesReport')
      ->name('reports.salesTax.printSalesReport');
    Route::get('reports/sales-tax/{report}/print-summary-report', 'Reports\SalesTaxReportController@printSummaryReport')
      ->name('reports.salesTax.printSummaryReport');
    Route::get('reports/payment-receipts/create', [PaymentReceiptController::class, 'create'])
      ->name('reports.paymentReceipts.create');
    Route::get('reports/payment-receipts/show', [PaymentReceiptController::class, 'show'])
      ->name('reports.paymentReceipts.show');
    Route::get('reports/payment-receipts/print', [PaymentReceiptController::class, 'print'])
      ->name('reports.paymentReceipts.print');
    Route::get('reports/payment-deposits/create', [PaymentDepositController::class, 'create'])
      ->name('reports.paymentDeposits.create');
    Route::get('reports/payment-deposits/add', [PaymentDepositController::class, 'add'])
      ->name('reports.paymentDeposits.add');
    Route::post('reports/payment-deposits/add', [PaymentDepositController::class, 'store'])
      ->name('reports.paymentDeposits.store');
    Route::get('reports/payment-deposits/show', [PaymentDepositController::class, 'show'])
      ->name('reports.paymentDeposits.show');
    Route::get('reports/payment-deposits/{deposit}/edit', [PaymentDepositController::class, 'edit'])
      ->name('reports.paymentDeposits.edit');
    Route::put('reports/payment-deposits/{deposit}/edit', [PaymentDepositController::class, 'update'])
      ->name('reports.paymentDeposits.update');
    Route::delete('reports/payment-deposits/{deposit}', [PaymentDepositController::class, 'destroy'])
      ->name('reports.paymentDeposits.destroy');
    Route::get('reports/payment-deposits/print', [PaymentDepositController::class, 'print'])
      ->name('reports.paymentDeposits.print');
    Route::get('reports/pending-orders', [PendingOrderController::class, 'index'])
      ->name('reports.pendingOrders.index');
    Route::get('reports/pending-orders/print', [PendingOrderController::class, 'print'])
      ->name('reports.pendingOrders.print');
    Route::get('reports/open-invoices', [\Modules\Orders\Http\Controllers\Reports\OpenInvoiceController::class, 'index'])
      ->name('reports.openInvoices.index');
    Route::get('reports/open-invoices/print', [\Modules\Orders\Http\Controllers\Reports\OpenInvoiceController::class, 'print'])
      ->name('reports.openInvoices.print');
    Route::get('reports/customer-invoice-summary', [CustomerInvoiceSummaryController::class, 'index'])
      ->name('reports.customerInvoiceSummary.index');
    Route::get('reports/customer-invoice-summary/print', [CustomerInvoiceSummaryController::class, 'print'])
      ->name('reports.customerInvoiceSummary.print');
    Route::get('reports/commissions/wes', [WesCommissionReportController::class, 'index'])
      ->name('reports.commissions.wes.index');
    Route::get('reports/commissions/wes/print', [WesCommissionReportController::class, 'print'])
      ->name('reports.commissions.wes.print');
    Route::get('reports/commissions/woodruff', [WoodruffCommissionReportController::class, 'index'])
      ->name('reports.commissions.woodruff.index');
    Route::get('reports/commissions/woodruff/print', [WoodruffCommissionReportController::class, 'print'])
      ->name('reports.commissions.woodruff.print');
    Route::get('reports/commissions/commercial-ro', [CommercialROCommissionReportController::class, 'index'])
      ->name('reports.commissions.commercialRO.index');
    Route::get('reports/commissions/commercial-ro/print', [CommercialROCommissionReportController::class, 'print'])
      ->name('reports.commissions.commercialRO.print');
    Route::get('reports/commissions/referrals', [ReferralCommissionReportController::class, 'index'])
      ->name('reports.commissions.referrals.index');
    Route::get('reports/commissions/referrals/print', [ReferralCommissionReportController::class, 'print'])
      ->name('reports.commissions.referrals.print');
    Route::delete('commission-items/{commissionItem}', [CommissionItemController::class, 'destroy'])
      ->name('commissionItems.destroy');
  });
