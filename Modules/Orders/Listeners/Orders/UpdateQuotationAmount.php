<?php

namespace Modules\Orders\Listeners\Orders;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Modules\Orders\Entities\Order;
use Modules\Orders\Events\Orders\OrderUpdated;
use Modules\Orders\Services\CalculateCustomerCommission;
use Modules\Orders\Services\CalculateStaffCommission;
use Modules\Orders\Traits\UpdatesOrderAmounts;
use Modules\Orders\Traits\UpdatesQuotationAmounts;
use Money\Money;

class UpdateQuotationAmount
{
  use UpdatesQuotationAmounts;

  /**
   * Handle the event.
   *
   * @param $event
   * @return void
   */
  public function handle($event)
  {
    $this->updateAmounts($event->quotation);
  }
}
