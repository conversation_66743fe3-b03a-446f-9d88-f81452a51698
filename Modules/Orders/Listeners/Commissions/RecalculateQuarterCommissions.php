<?php

namespace Modules\Orders\Listeners\Commissions;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Orders\Entities\Order;
use Modules\Orders\Events\Commissions\DecommissionsUpdated;
use Modules\Orders\Events\Orders\OrderUpdated;
use Modules\Orders\Services\CalculateCustomerCommission;
use Modules\Orders\Services\CalculateStaffCommission;

class RecalculateQuarterCommissions implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param OrderUpdated $event
     * @return void
     */
    public function handle(DecommissionsUpdated $event)
    {
        Order::whereYear('created_at', now()->year)
            ->get()
            // Get orders completed in the current quarter
            ->filter(function ($order) {
                return $order->quarter === now()->quarter && $order->completed;
            })
            // Loop through all to recalculate commissions
            ->each(function ($order) {
                (new CalculateCustomerCommission($order))->saveCommission();

                if ($order->isService()) {
                    // Staff members earn commissions only on service order
                    (new CalculateStaffCommission($order))->saveCommissions();
                }
            });


    }

    /**
     * Determine whether the listener should be queued.
     *
     * @param OrderUpdated $event
     * @return bool
     */
//    public function shouldQueue(OrderUpdated $event)
//    {
//        //return $event->order->completed;
//    }
}
