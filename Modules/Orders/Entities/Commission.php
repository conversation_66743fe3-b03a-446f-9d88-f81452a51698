<?php

namespace Modules\Orders\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Commission extends Model implements Auditable
{
  use \OwenIt\Auditing\Auditable, SoftDeletes;

  const TYPE_WES = 'WES'; // Wesley
  const TYPE_RO = 'RO'; // Commercial RO
  const TYPE_WS = 'WS'; // Woodruff Sales
  const TYPE_WS_CS = 'WS_CS'; // Woodruff Sales Commercial Softeners
  const TYPE_WES_NAME = 'Wesley Commission';
  const TYPE_RO_NAME = 'Commercial RO Commission';
  const TYPE_WS_NAME = 'Woodruff Sales Commission';
  const TYPE_WS_CS_NAME = 'Woodruff Sales for Other Commercial Softeners Commission';
  const TYPE_REFERRAL = 'referral';
  const TYPE_REFERRAL_NAME = 'Referral Commission';
  const TYPE_SYSTEM = 'system';
  const TYPE_CUSTOM = 'custom';

  protected $guarded = [];

  protected $appends = [
    'amount'
  ];

  protected $dates = [
    'final_date'
  ];

  public function commissionable()
  {
    return $this->morphTo();
  }

  public function order()
  {
    return $this->belongsTo(Order::class);
  }

  public function getAmountAttribute()
  {
    return $this->quantity * $this->price;
  }

  public function getFormattedCommissionTypeAttribute()
  {
    return [
      Commission::TYPE_WS => Commission::TYPE_WS_NAME,
      Commission::TYPE_WS_CS => Commission::TYPE_WS_CS_NAME,
      Commission::TYPE_WES => Commission::TYPE_WES_NAME,
      Commission::TYPE_RO => Commission::TYPE_RO_NAME,
      Commission::TYPE_REFERRAL => Commission::TYPE_REFERRAL_NAME
    ][$this->commission_type];
  }
}
