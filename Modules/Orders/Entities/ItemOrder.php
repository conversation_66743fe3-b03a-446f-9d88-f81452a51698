<?php

namespace Modules\Orders\Entities;

use Illuminate\Database\Eloquent\Relations\Pivot;
use Modules\Inventory\Entities\Item;

class ItemOrder extends Pivot
{
  protected $table = 'item_order';

  protected $casts = [
    'options' => 'array'
  ];

  public function order()
  {
    return $this->belongsTo(Order::class);
  }

  public function item()
  {
    return $this->belongsTo(Item::class);
  }
}
