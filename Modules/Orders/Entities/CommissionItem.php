<?php

namespace Modules\Orders\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class CommissionItem extends Model implements Auditable
{
  use \OwenIt\Auditing\Auditable, SoftDeletes;

  protected $guarded = [];

  public function commissionable()
  {
    return $this->belongsTo(Commission::class, 'commission_id');
  }
}
