<?php

namespace Modules\Orders\Entities\Reports;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Modules\Orders\Entities\Payment;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\User;
use OwenIt\Auditing\Contracts\Auditable;

class PaymentDepositReport extends Model implements Auditable
{
  use \OwenIt\Auditing\Auditable;

  const DATE_FORMAT = 'm/d/y';
  const FORM_DATE_FORMAT = 'Y-m-d';
  const TYPE_CUSTOM = 'custom';

  protected $guarded = [];

  protected $dates = [
    'report_start_date',
    'report_end_date',
    'check_date'
  ];

  protected static function booted()
  {
    static::creating(function ($model) {
      $model->created_by = auth()->user()->name;
    });
  }

  public function payment()
  {
    return $this->belongsTo(Payment::class);
  }

  public function isCustom(): bool
  {
    return $this->type === self::TYPE_CUSTOM;
  }

  public function getFormattedCheckDateAttribute()
  {
    return optional($this->check_date)->format(self::DATE_FORMAT);
  }

  public function getFormattedReportEndDateAttribute()
  {
    return optional($this->report_end_date)->format(self::FORM_DATE_FORMAT);
  }
}
