<?php

use Faker\Generator as Faker;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;
use Modules\Users\Entities\User;

$factory->define(Order::class, function (Faker $faker) {
  $number = $faker->randomNumber(6);
  return [
    'number' => $number,
    'full_number' => 'TU' . $number,
    'customer_id' => function () {
      return factory(User::class)->state('customer')->create()->id;
    },
    'created_by' => function () {
      return factory(User::class)->state('staff')->create()->id;
    },
    'type' => Order::TYPE_ORDER,
    'purchase_order_number' => $faker->randomNumber(6, true),
    'shipping_via' => $faker->randomElement([Order::SHIP_VIA_CALL, 'Shipping Company', 'Dats Trucking', 'Link Trucking', 'UPS']),
    'customer_pickup_by' => $faker->name(),
    'shipping_cost' => $faker->randomNumber(2, true),
  ];
});

$factory->state(Order::class, 'withCustomItems', function ($faker) {
  return [];
});

$factory->state(Order::class, 'withItemsAndCustomItems', function ($faker) {
  return [];
});

$factory->state(Order::class, 'invoiced', function ($faker) {
  return [
    'invoiced' => true,
    'invoiced_at' => now(),
    'invoiced_by' => 1
  ];
});

$factory->afterCreating(Order::class, function (Order $order, Faker $faker) {
  $order->items()->createMany(factory(OrderLine::class, mt_rand(2, 10))->make()->toArray());

  updateOrder($order);
});

$factory->afterCreatingState(Order::class, 'withCustomItems', function (Order $order, Faker $faker) {
  $order->items()->saveMany(factory(OrderLine::class, mt_rand(2, 10))->state('custom')->make());

  updateOrder($order);
});

if (! function_exists('updateOrder')) {
  function updateOrder(Order $order): bool
  {
    $order->refresh();

    return $order->forceFill([
      'amount' => money_add($order->items->sum('amount'), $order->service_cost),
      'tax_amount' => $order->tax,
      'total_amount' => money_amount($order->grand_total),
      'taxed' => $order->taxable(),
    ])->save();
  }
}

if (! function_exists('getOptions')) {
  function getOptions(): array
  {
    return [
      'statuses' => [
        'created' => [
          'name' => 'created',
          'created_at' => now()->format('m-d-y H:i:s'),
          'created_by' => 1,
          'previous' => null,
          'note' => ''
        ]
      ]
    ];
  }
}

