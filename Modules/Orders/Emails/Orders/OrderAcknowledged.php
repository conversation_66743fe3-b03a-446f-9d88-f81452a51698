<?php

namespace Modules\Orders\Emails\Orders;

use Illuminate\Bus\Queueable;
use App\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Arr;
use Modules\Orders\Entities\Order;
use Modules\Orders\Traits\HasReplaceableText;

class OrderAcknowledged extends Mailable implements ShouldQueue
{
  use Queueable, SerializesModels, HasReplaceableText;

  public $order;
  protected array $data;

  /**
   * Create a new message instance.
   *
   * @param Order $order
   */
  public function __construct(Order $order, array $data = [])
  {
    $this->order = $order;
    $this->data = $data;
  }

  /**
   * Build the message.
   *
   * @return $this
   */
  public function build()
  {
    return $this->view('orders::emails.acknowledgement')
      ->subject(config('app.name') . ' Acknowledgement for Order No. ' . $this->order->formatted_number)
      ->with(
        'body',
        $this->replaceEmailBody(
          $this->order,
          Arr::get($this->data, 'body', '')
        )
      )
      ->attachFromStorageDisk('local', "generated/acknowledgements/{$this->order->number}.pdf");
  }
}
