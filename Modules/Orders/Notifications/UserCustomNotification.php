<?php

namespace Modules\Orders\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Arr;
use Modules\Orders\Entities\Order;

class UserCustomNotification extends Notification
{
  protected ?Order $order;
  protected array $data;

  /**
   * Create a new notification instance.
   * @param Order|null $order
   * @param array $data
   */
  public function __construct(array $data, Order $order = null)
  {
    $this->order = $order;
    $this->data = $data;
  }

  /**
   * Get the notification's delivery channels.
   *
   * @param  mixed  $notifiable
   * @return array
   */
  public function via($notifiable)
  {
    return ['database'];
  }

  /**
   * Get the array representation of the notification.
   *
   * @param  mixed  $notifiable
   * @return array
   */
  public function toArray($notifiable)
  {
    $record = [
      'purpose' => 'User Custom Notification',
      'instruction' => Arr::get($this->data, 'instruction'),
      'type' => Arr::get($this->data, 'type')
    ];

    if (! empty($this->order)) {
      $record['customer'] = [
        'id' => $this->order->customer->user_id,
        'name' => $this->order->customer->name
      ];
      $record['order'] = [
        'id' => $this->order->id,
        'number' => $this->order->number,
        'formatted_number' => $this->order->formatted_number
      ];
    }

    return $record;
  }
}
