<?php

namespace Modules\Orders\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Modules\Orders\Entities\Order;

class OrderSpecialInstruction extends Notification
{
    use Queueable;

    /**
     * @var Order
     */
    protected $order;

    /**
     * Create a new notification instance.
     *
     * @param Order $order
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'customer' => [
                'id' => $this->order->customer->user_id,
                'name' => $this->order->customer->name
            ],
            'order' => [
                'id' => $this->order->id,
                'number' => $this->order->number,
                'formatted_number' => $this->order->full_number
            ],
            'instruction' => $this->order->customer->special_instructions,
            'type' => 'Finished Order'
        ];
    }
}
