<?php

namespace Modules\Orders\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Arr;
use Modules\Orders\Entities\Order;
use Modules\Users\Entities\Customer;

class InvoicedOrderDownloadNotification extends Notification
{
  protected Customer $customer;
  protected string $zipFile;

  /**
   * Create a new notification instance.
   * @param Customer $customer
   * @param string $file
   */
  public function __construct(Customer $customer, string $file)
  {
    $this->customer = $customer;
    $this->zipFile = $file;
  }

  /**
   * Get the notification's delivery channels.
   *
   * @param  mixed  $notifiable
   * @return array
   */
  public function via($notifiable)
  {
    return ['database'];
  }

  /**
   * Get the array representation of the notification.
   *
   * @param  mixed  $notifiable
   * @return array
   */
  public function toArray($notifiable)
  {
    $record = [
      'purpose' => 'Customer Invoiced Download Notification',
      'instruction' => 'Customers invoiced orders have been generated for printing.',
      'type' => 'download'
    ];

    if (! empty($this->order)) {
      $record['file'] = [
        'name' => $this->zipFile,
        'path'
      ];
    }

    return $record;
  }
}
