<?php


namespace Modules\Orders\Traits;


use Illuminate\Support\Facades\DB;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Quotation;
use Money\Money;

trait UpdatesQuotationAmounts
{
  public function updateAmounts(Quotation $quotation): bool
  {
    $quotation->refresh();
    $amount = money_add($quotation->items->sum('amount'), $quotation->service_cost);
    $tax = $quotation->taxable() ? money_multiply($amount, settings('global.tax_rate.value')) : 0;
    $total = money_add($amount, $tax);

    return $quotation->update([
      'amount' => $amount,
      'tax_amount' => $tax,
      'total_amount' => $total,
    ]);
  }
}
