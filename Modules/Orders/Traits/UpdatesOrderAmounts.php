<?php


namespace Modules\Orders\Traits;


use Illuminate\Support\Facades\DB;
use Modules\Orders\Entities\Order;
use Money\Money;

trait UpdatesOrderAmounts
{
  public function updateOrderAmounts(Order $order): bool
  {
    $amount = Money::USD(str_money(round($order->items->sum('amount') + $order->service_cost, 2)));
    $amountPaidSoFar = Money::USD(str_money(
      DB::table('order_payment')
        ->where('order_id', $order->id)
        ->sum('amount')
    ));
    $grandTotal = Money::USD(str_money($order->grand_total));
    $balance = $grandTotal->subtract($amountPaidSoFar);
    $hasZeroBalance = $balance->equals(Money::USD(0));

    $paymentLogWasEmpty = is_null($order->payment_log);
    $record = [
      'amount' => $amount->getAmount() / 100,
      'tax_amount' => $order->tax,
      'total_amount' => $grandTotal->getAmount() / 100,
      'taxed' => $order->taxable(),
      'amount_paid' => $amountPaidSoFar->getAmount() / 100,
      'balance' => $balance->getAmount() / 100,
      'paid' => $hasZeroBalance,
      'paid_at' => $hasZeroBalance ? now() : null,
      'last_payment_date' => now(),
      'payment_log' => $order->getPaymentLog(),
    ];

    if (!$paymentLogWasEmpty && is_null($record['payment_log']) && $record['balance'] < 0 && $order->negative_balance_utilized) {
      /**
       * The payment attached to this invoice was probably deleted but it had had its negative balance utilized on a payment
       */
      $record['negative_balance_utilized'] = 0;
    }

    return $order->fill($record)->save();
  }
}
