<?php


namespace Modules\Orders\Traits;


use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\Orders\Entities\Order;

trait HasReplaceableText
{
  /**
   * TODO: Move to helper method
   * @param Order $order
   * @param string $body
   * @return array|string|string[]
   */
  public function replaceEmailBody(Order $order, string $body)
  {
    $body = str_replace('$customer', $order->customer->name, $body);

    return str_replace('$order', $order->number, $body);
  }

  /**
   * @param string $body
   * @param array $replacements
   * @return array|mixed|string|string[]
   */
  public function customizeEmailMessage(string $body, array $replacements = [])
  {
    if (empty($replacements)) {
      return $body;
    }

    foreach($replacements as $key => $value) {
      if (Str::contains($body, $key)) {
        if (! Str::startsWith($key, '$')) {
          $key = '$' . $key;
        }

        $body = str_replace($key, $value, $body);
      }
    }

    return $body;
  }
}
