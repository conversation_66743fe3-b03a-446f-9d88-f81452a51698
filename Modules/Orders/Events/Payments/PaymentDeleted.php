<?php

namespace Modules\Orders\Events\Payments;

use Illuminate\Queue\SerializesModels;
use Modules\Orders\Entities\Payment;

class PaymentDeleted
{
    use SerializesModels;

    public $payment;

  /**
   * Create a new event instance.
   *
   * @param Payment $payment
   */
    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
