<?php

namespace Modules\Orders\Events\Quotations;

use Illuminate\Queue\SerializesModels;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Quotation;

class QuotationUpdated
{
    use SerializesModels;

    public Quotation $quotation;

    /**
     * Create a new event instance.
     *
     * @param Quotation $quotation
     */
    public function __construct(Quotation $quotation)
    {
        $this->quotation = $quotation;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
