@extends('layouts.master')

@section('title', 'Return Items')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('orderModule.orders.index') }}">Orders</a></li>
              <li class="breadcrumb-item"><a
                  href="{{ route('orderModule.orders.show', $order) }}">#{{ $order->full_number }}</a></li>
              <li class="breadcrumb-item active">Return Items</li>
            </ol>
          </div>
          <h4 class="page-title">Return Items</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-lg-8 col-md-10 col-sm-12">
        <div class="card">
          <div class="card-body">
            <form action="{{ route('orderModule.orders.returns.store', $order) }}" method="post">
              @csrf
              <div class="form-group">
                <label for="returned-by">Returned By <span class="text-danger">*</span></label>
                <input class="form-control"
                       type="text"
                       name="returned_by"
                       id="returned-by"
                       value="{{ old('returned_by') }}"
                       required>
                @error('returned_by')
                <span class="text-error">{{ $error('returned_by') }}</span>
                @enderror
              </div>
              <div class="form-group">
                <label for="reason">Reason</label>
                <textarea class="form-control"
                          name="reason"
                          id="reason">{{ old('reason') }}</textarea>
                @error('reason')
                <span class="text-error">{{ $error('reason') }}</span>
                @enderror
              </div>
              <div class="form-group row">
                <div class="col-md-6">
                  <label for="settle-with">Settle With <span class="text-danger">*</span></label>
                  <select class="form-control" name="settle_with" required>
                    <option value="">Select Options</option>
                    <option value="credit">Credit</option>
                    <option value="refund">Refund</option>
                    <option value="replacement">Item Replacement</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label for="settle-note">Settlement Note</label>
                  <input class="form-control" id="settle-note" name="settle_note">
                </div>
              </div>
              <p>Specify the quantity for the items that have been returned.</p>
              <table class="table table-sm">
                <thead>
                <tr>
                  <th>Part#</th>
                  <th>Qty</th>
                  <th>Qty Returned <span class="text-danger">*</span></th>
                </tr>
                </thead>
                <tbody>
                @foreach($order->items as $item)
                  <tr>
                    <td>
                      {{ $item->code }}
                      <input type="hidden" name="order_line_id[]" value="{{ $item->id }}">
                    </td>
                    <td>{{ $item->real_quantity }}</td>
                    <td>
                      <input class="form-control w-50" type="number" name="quantity_returned[]" value="0">
                    </td>
                  </tr>
                @endforeach
                </tbody>
              </table>

              <div class="form-row">
                <div class="col-sm-12 d-flex justify-content-end">
                  <a class="btn btn-light mr-2" href="{{ route('orderModule.orders.show', $order) }}"><i class="mdi mdi-cancel mr-1"></i> Cancel</a>
                  <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
                </div>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col-->
    </div>
    <!-- end row-->

  </div>
  <!-- container -->
@endsection

