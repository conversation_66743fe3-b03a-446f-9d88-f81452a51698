<div class="table-responsive">
  <table class="table table-centered table-sm w-100 dt-responsive nowrap datatable search-autofocus">
    <thead class="thead-light">
    <tr>
      <th class="text-right">Order #</th>
      <th>Part#</th>
      <th>Description</th>
      <th class="text-right">Original<br>Qty</th>
      <th class="text-right">Qty<br>Returned</th>
      <th class="text-right">Price</th>
      <th class="text-right">Total</th>
      <th>Returned By</th>
      <th>Created By</th>
      <th>Date</th>
      <th>Approved</th>
      <th>Approved By</th>
      <th class="text-right" style="min-width: 85px;">Action</th>
    </tr>
    </thead>
    <tbody>
    @foreach ($returns as $returnGroup)
      @foreach($returnGroup as $returnItem)
        @if($loop->first)
          <tr>
            <td class="text-right">
              <a href="{{ route('orderModule.orders.show', $returnItem->order_id) }}">
                {{ $returnItem->orderReturn->order->full_number }}
              </a>
            </td>
            <td>{{ $returnItem->code }}</td>
            <td>{{ $returnItem->description }}</td>
            <td class="text-right">{{ $returnItem->quantity }}</td>
            <td class="text-right">{{ $returnItem->quantity_returned }}</td>
            <td class="text-right">{{ number_format($returnItem->price, 2) }}</td>
            <td class="text-right">
              {{ number_format($returnItem->price * $returnItem->quantity_returned, 2) }}
            </td>
            <td>{{ $returnItem->orderReturn->returned_by }}</td>
            <td>
              <a href="{{ route('users.staff.show', $returnItem->orderReturn->created_by) }}">
                {{ $returnItem->orderReturn->createdBy->name }}
              </a>
            </td>
            <td>{{ $returnItem->orderReturn->created_at->format('m/d/y') }}</td>
            <td>{{ $returnItem->orderReturn->approved ? 'Yes' : 'No' }}</td>
            <td>
              @if($returnItem->orderReturn->approved)
                <a href="{{ route('users.staff.show', $returnItem->orderReturn->approved_by) }}">
                  {{ $returnItem->orderReturn->approvedBy->name }}
                </a>
              @endif
            </td>
            <td class="table-action text-right">
              @if($returnItem->orderReturn->notApproved())
              <a href="{{ route('orderModule.returns.edit', $returnItem->orderReturn) }}" class="mr-1 d-inline-block">
                <i class="mdi mdi-pencil"></i>
                Edit
              </a>
              @endif
              @if(! $returnItem->orderReturn->approved)
                @can('approve return')
                  <a href="#"
                     data-target="#approveReturnModal"
                     data-toggle="modal"
                     class="approve-return-btn"
                     data-url="{{ route('orderModule.returns.approve', $returnItem->return_id) }}">
                    <i class="mdi mdi-check-box-outline"></i>
                    Approve
                  </a>
                @endcan
                @can('delete return')
                  <a href="#"
                     data-target="#deleteItem"
                     data-toggle="modal"
                     class="delete-item-btn text-muted"
                     data-url="{{ route('orderModule.returns.destroy', $returnItem->return_id) }}">
                    <i class="mdi mdi-delete"></i>
                    Delete
                  </a>
                @endcan
              @endif
            </td>
          </tr>
        @else
          <tr>
            <td class="text-right"></td>
            <td>{{ $returnItem->code }}</td>
            <td>{{ $returnItem->description }}</td>
            <td class="text-right">{{ $returnItem->quantity }}</td>
            <td class="text-right">{{ $returnItem->quantity_returned }}</td>
            <td class="text-right">{{ number_format($returnItem->price, 2) }}</td>
            <td class="text-right">
              {{ number_format($returnItem->price * $returnItem->quantity_returned, 2) }}
            </td>
            <td class="text-right"></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td class="table-action text-right"></td>
          </tr>
        @endif
      @endforeach
    @endforeach
    </tbody>
  </table>
</div>
