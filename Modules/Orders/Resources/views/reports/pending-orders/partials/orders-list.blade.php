<div class="table-responsive">
  <table class="table table-centered table-sm table-hover datatable search-autofocus">
    <thead class="thead-light">
    <tr>
      <th>Customer</th>
      <th class="text-right">Date</th>
      <th class="text-right">Order #</th>
      <th>PO#</th>
      <th class="text-right">Qty</th>
      <th style="width: 250px">Description</th>
      <th>Part#</th>
      <th class="text-right">Price</th>
      <th class="text-right">Total</th>
      <th class="text-right">Ship Date</th>
      <th>Printed</th>
      <th>Final Date</th>
      <th>Status</th>
    </tr>
    </thead>
    <tbody>
    @foreach ($orders as $group)
      @foreach($group as $order)
        @if($loop->first)
          <tr>
            <td>{{ $order->customer_name }}</td>
            <td class="text-right">{{ $order->formatted_date }}</td>
            <td class="text-right">{{ $order->full_number }}</td>
            <td>{{ $order->purchase_order_number }}</td>
            <td class="text-right">{{ $order->quantity }}</td>
            <td style="width: 250px">{!! nl2br($order->description) !!}</td>
            <td>{{ $order->code }}</td>
            <td class="text-right">{{ number_format($order->price, 2) }}</td>
            <td class="text-right">{{ number_format($order->item_amount, 2) }}</td>
            <td>{{ $order->formatted_shipping_date ?? $order->shipping_date_option }}</td>
            <td>{{ $order->printed_shop_order ? 'Yes' : 'No' }}</td>
            <td class="text-right">{{ $order->final_date }}</td>
            <td>{{ $order->status }}</td>
          </tr>
        @else
          <tr>
            <td colspan="4"></td>
            <td class="text-right">{{ number_format($order->quantity, 2) }}</td>
            <td>{{ $order->description }}</td>
            <td>{{ $order->code }}</td>
            <td class="text-right">{{ number_format($order->price, 2) }}</td>
            <td class="text-right">{{ number_format($order->item_amount, 2) }}</td>
            <td colspan="4"></td>
          </tr>
        @endif
      @endforeach
      <tr>
        <td colspan="8"></td>
        <td class="text-right font-weight-bold">{{ number_format($order->total_amount, 2) }}</td>
        <td colspan="4"></td>
      </tr>
    @endforeach
    </tbody>
  </table>
</div>
