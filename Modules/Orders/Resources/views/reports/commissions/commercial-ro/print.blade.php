<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  @include('shared.print.default-print-styles')
</head>

<body>
  <!-- end row -->
  <div class="mb-sm">
    <div class="col-sm-12">
      <table class="table table-sm mb-0">
        <tr>
          <td colspan="4" class="text-left" style="border: 2px solid #333; border-right: none;">
            <h2>Commission Report for Commercial ROs</h2>
          </td>
          <td colspan="3" class="text-center" style="border-top: 2px solid #333; border-bottom: 2px solid #333;">
            Reporting Period<br>
            From: {{ $formattedStartDate }}<br>
            To: {{ $formattedEndDate }}
          </td>
          <td colspan="3" class="text-right" style="border: 2px solid #333; border-left: none;">
            Printed On: {{ now()->format('m/d/Y') }}
          </td>
        </tr>
        <tr>
          <th class="bg-transparent" style="border-bottom: 2px solid #333; border-left: 2px solid #333;">Order Date</th>
          <th class="bg-transparent" style="border-bottom: 2px solid #333;">PO#</th>
          <th class="bg-transparent text-right" style="border-bottom: 2px solid #333;">Qty</th>
          <th class="bg-transparent" style="border-bottom: 2px solid #333;">Description</th>
          <th class="bg-transparent" style="border-bottom: 2px solid #333;">Part#</th>
          <th class="bg-transparent text-right" style="border-bottom: 2px solid #333;">Price</th>
          <th class="bg-transparent text-right" style="border-bottom: 2px solid #333;">Total</th>
          <th class="bg-transparent text-right" style="border-bottom: 2px solid #333;">Comm</th>
          <th class="bg-transparent" style="border-bottom: 2px solid #333;">Final Date</th>
          <th class="bg-transparent" style="border-bottom: 2px solid #333; border-right: 2px solid #333;">Invoice #</th>
        </tr>
        @php $totalCommissions = 0; $totalOrders = 0; @endphp
        @foreach($commissionGroups as $commissionGroup)
        @php
          $totalCommissions += $commissionGroup->sum('commission');
          $totalOrders += $commissionGroup->sum('amount');
        @endphp
        @foreach($commissionGroup as $commission)
        @if($loop->first)
        <tr>
          <td style="border-bottom: 1px solid #333;">
            {{ $commission->order->formatted_date }}
          </td>
          <td style="border-bottom: 1px solid #333;">
            {{ $commission->order->purchase_order_number }}
          </td>
          <td class="text-right" style="border-bottom: 1px solid #333;">
            {{ $commission->quantity }}
          </td>
          <td class="" style="border-bottom: 1px solid #333;">
            {{ $commission->description }}
          </td>
          <td class="" style="border-bottom: 1px solid #333;">
            {{ $commission->part_number }}
          </td>
          <td class="text-right" style="border-bottom: 1px solid #333;">
            {{ number_format($commission->price, 2) }}
          </td>
          <td class="text-right" style="border-bottom: 1px solid #333;">
            {{ number_format($commission->amount, 2) }}
          </td>
          <td class="text-right" style="border-bottom: 1px solid #333;">
            {{ number_format($commission->commission, 2) }}
          </td>
          <td class="" style="border-bottom: 1px solid #333;">
            {{ $commission->order->formatted_invoice_date }}
          </td>
          <td class="" style="border-bottom: 1px solid #333;">
            {{ $commission->order->number }}
          </td>
        </tr>
        @else
        <tr>
          <td style="border-bottom: 1px solid #333;" colspan="2"></td>
          <td class="text-right" style="border-bottom: 1px solid #333;">
            {{ $commission->quantity }}
          </td>
          <td class="" style="border-bottom: 1px solid #333;">
            {{ $commission->description }}
          </td>
          <td class="" style="border-bottom: 1px solid #333;">
            {{ $commission->part_number }}
          </td>
          <td class="text-right" style="border-bottom: 1px solid #333;">
            {{ number_format($commission->price, 2) }}
          </td>
          <td class="text-right" style="border-bottom: 1px solid #333;">
            {{ number_format($commission->amount, 2) }}
          </td>
          <td class="text-right" style="border-bottom: 1px solid #333;">
            {{ number_format($commission->commission, 2) }}
          </td>
          <td class="" style="border-bottom: 1px solid #333;">
            {{ $commission->order->formatted_invoice_date }}
          </td>
          <td class="" style="border-bottom: 1px solid #333;">
            {{ $commission->order->number }}
          </td>
        </tr>
        @endif

        @endforeach
        <tr>
          <td colspan="3" style="border-bottom: 1px solid #333;">
            <strong>{{ $commission->order->customer->name }}</strong><br>
            <strong>Order No. {{ $commission->order->full_number }}
          </td>
          <td class="text-right pb-3" colspan="4"  style="border-bottom: 1px solid #333;">
            <strong>Order Total Less Sales Tax: {{ number_format($commissionGroup->sum('amount'), 2) }}</strong><br>
            <strong>Commission Total: {{ number_format($commissionGroup->sum('commission'), 2) }}</strong>
          </td>
          <td colspan="3" style="border-bottom: 1px solid #333;"></td>
        </tr>
        @endforeach
        <tr>
          <td colspan="10" style="border-bottom: 1px solid #333;"></td>
        </tr>
        <tr>
          <td class="text-right" colspan="7">
            <strong>Total Orders: ${{ number_format($totalOrders, 2)}}</strong><br>
            <strong>Total Commission: ${{ number_format($totalCommissions, 2)}}</strong>
          </td>
          <td colspan="3"></td>
        </tr>
      </table>
    </div>
  </div>
  <!-- container -->
  <script type="text/php">
    $text = "Page {PAGE_NUM} of {PAGE_COUNT}";
    $size = 10;
    $font = $fontMetrics->getFont("Verdana");
    $width = $fontMetrics->get_text_width($text, $font, $size) - 90;
    $x = ($pdf->get_width() - $width) + 2;
    $y = 70;
    $pdf->page_text($x, $y, $text, $font, $size, array(0,0,0));
</script>
</body>

</html>
