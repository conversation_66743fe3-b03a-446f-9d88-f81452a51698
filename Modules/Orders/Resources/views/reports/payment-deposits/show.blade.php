@extends('layouts.master')

@section('title', 'Payment Deposits')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item active">Payment Deposits</li>
            </ol>
          </div>
          <h4 class="page-title">Payment Deposits</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row mb-2">
              <div class="col-sm-12 text-right">
                <a
                  href="{{ route('orderModule.reports.paymentDeposits.print', ['report_start_date' => request('report_start_date'), 'report_end_date' => request('report_end_date')]) }}"
                  class="btn btn-light mr-1">
                  <i class="mdi mdi-file-pdf mr-1"></i>
                  Print
                </a>
                <a
                  href="{{ route('orderModule.reports.paymentDeposits.add', ['report_start_date' => request('report_start_date'), 'report_end_date' => request('report_end_date')]) }}"
                  class="btn btn-primary">
                  <i class="mdi mdi-plus mr-1"></i>
                  Add Deposit
                </a>
              </div><!-- end col-->
            </div>

            <div class="table-responsive">
              <table class="table table-centered table-sm dt-responsive nowrap">
                <thead class="thead-light">
                <tr>
                  <th class="py-3 bg-white" colspan="4">
                    <div style="display: inline-block; float: left;">
                      <img
                        style="display: block;"
                        src="{{ asset('images/logo.png') }}"
                        alt="{{ config('app.name') }} logo">
                      <span class="d-block" style="display: block; padding-left: 70px; margin-top: -24px;">
                        200 W. Haven Ave. <br>Salt Lake City, UT 84115 <br>Phone (*************
                      </span>
                    </div>
                  </th>
                  <th class="bg-white text-right" colspan="3">
                    <span class="font-18">Payment Deposits for</span> <br>{{ $formattedStartDate }}
                    to {{ $formattedEndDate }}
                  </th>
                </tr>
                <tr>
                  <th class="bg-white text-uppercase" style="border-bottom: 3px solid #333333;">Payment Date</th>
                  <th class="bg-white text-uppercase" style="border-bottom: 3px solid #333333;">Check Date</th>
                  <th class="bg-white text-uppercase" style="border-bottom: 3px solid #333333;">Name</th>
                  <th class="bg-white text-right" style="border-bottom: 3px solid #333333;">Amount</th>
                  <th class="bg-white" style="border-bottom: 3px solid #333333;">Check#</th>
                  <th class="bg-white" style="border-bottom: 3px solid #333333;">Bank ID</th>
                  <th class="bg-white text-right" style="border-bottom: 3px solid #333333;">Action</th>
                </tr>
                </thead>
                <tbody>
                @foreach($paymentDeposits as $deposit)
                  <tr>
                    <td style="border-bottom: 1px solid #333333;">{{ $deposit->payment_date }}</td>
                    <td style="border-bottom: 1px solid #333333;">{{ $deposit->check_date }}</td>
                    <td style="border-bottom: 1px solid #333333;">{{ $deposit->check_name }}</td>
                    <td class="text-right"
                        style="border-bottom: 1px solid #333333;">{{ number_format($deposit->amount, 2) }}</td>
                    <td
                      style="border-bottom: 1px solid #333333;">{{ $deposit->check_number }}</td>
                    <td style="border-bottom: 1px solid #333333;">{{ $deposit->bank_id }}</td>
                    <td class="text-right" style="border-bottom: 1px solid #333333;">
                      @if($deposit->type === 'custom')
                        <a
                          class="text-info mr-1"
                          href="{{ route('orderModule.reports.paymentDeposits.edit', [
                            'deposit' => $deposit->id,
                            'report_start_date' => request('report_start_date'),
                            'report_end_date' => request('report_end_date')
                          ]) }}"><i
                            class="mdi mdi-pencil"></i> Edit</a>
                      @endif
                        <a
                          href="#"
                          class="delete-item-btn text-muted"
                          data-target="#deleteItem"
                          data-toggle="modal"
                          data-url="{{ route('orderModule.reports.paymentDeposits.destroy', ['deposit' => $deposit->id, 'type' => $deposit->type]) }}"><i
                            class="mdi mdi-trash-can"></i> Delete</a>
                    </td>
                  </tr>
                @endforeach
                <tr>
                  <td></td>
                  <td></td>
                  <td class="font-weight-bold text-uppercase">Total</td>
                  <td class="text-right font-weight-bold">{{ number_format($paymentDeposits->sum('amount'), 2) }}</td>
                  <td colspan="2"></td>
                </tr>
                </tbody>
              </table>
            </div>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>
  <!-- container -->

  <div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content modal-filled bg-danger">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-wrong h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">This will delete the payment deposit entry from the system. This action cannot be undone. Are you sure about this?</p>
            <form action="#" method="post" id="delete-item-form">
              @csrf
              @method('delete')
              <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
@endsection
@push('js')
  <script src="{{ asset('js/pages/demo.products.js') }}"></script>
@endpush
