<div class="form-group row">
  <div class="col-md-6">
    <label for="payment-date">Payment Date <span class="text-danger">*</span></label>
    <input
      class="form-control"
      type="date"
      name="created_at"
      id="payment-date"
      value="{{ old('created_at', optional($deposit->created_at)->toDateString()) }}"
      required>
    <input
      class="form-control"
      type="hidden"
      name="report_start_date"
      id="report-start-date"
      value="{{ old('report_start_date', request('report_start_date')) }}"
      required>
    <input
      class="form-control"
      type="hidden"
      name="report_end_date"
      id="report-end-date"
      value="{{ old('report_end_date', request('report_end_date')) }}">
  </div>
  <div class="col-md-6">
    <label for="payment-amount">Amount <span class="text-danger">*</span></label>
    <input type="number"
           class="form-control"
           id="payment-amount"
           name="amount"
           step="0.01"
           value="{{ old('amount', $deposit->amount) }}"
           required>
  </div>
</div>
<div class="form-group">
  <label for="payment-check-name">Check Name <span class="text-danger">*</span></label>
  <input class="form-control @error('check_name') is-invalid @enderror"
         type="text"
         name="check_name"
         id="payment-check-name"
         value="{{ old('check_name', $deposit->check_name) }}"
         required>
</div>
<div class="form-group row">
  <div class="col-md-6">
    <label for="payment-check-date">Check Date</label>
    <input class="form-control @error('check_date') is-invalid @enderror"
           type="date"
           name="check_date"
           id="payment-check-date"
           value="{{ old('check_date', optional($deposit->check_date)->toDateString()) }}">
  </div>
  <div class="col-md-6">
    <label for="payment-check-number">Check Number</label>
    <input class="form-control @error('check_number') is-invalid @enderror"
           type="text"
           name="check_number"
           id="payment-check-number"
           value="{{ old('check_number', $deposit->check_number) }}">
  </div>
</div>
<div class="form-group row">

  <div class="col-md-6">
    <label for="payment-bank-id">Bank ID</label>
    <input class="form-control @error('bank_id') is-invalid @enderror"
           type="text"
           name="bank_id"
           id="payment-bank-id"
           value="{{ old('bank_id', $deposit->bank_id) }}">
  </div>
</div>
