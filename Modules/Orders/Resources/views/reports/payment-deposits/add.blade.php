@extends('layouts.master')

@section('title', 'Add Payment Deposit')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item active">Generate Payment Deposit</li>
            </ol>
          </div>
          <h4 class="page-title">Generate Payment Deposit</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-6">
        <div class="card">
          <div class="card-body">
            <form action="{{ route('orderModule.reports.paymentDeposits.store') }}" method="post">
              @csrf
              @include('orders::reports.payment-deposits.partials.form', ['deposit' => new \Modules\Orders\Entities\Reports\PaymentDepositReport()])
              <div class="form-group text-right">
                <a class="btn btn-light mr-1"
                   href="{{ route('orderModule.reports.paymentDeposits.show', ['report_start_date' => request('report_start_date'), 'report_end_date' => request('report_end_date')]) }}">Cancel</a>
                <button class="btn btn-primary" type="submit">Add Record</button>
              </div>
            </form>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>
  <!-- container -->
@endsection
