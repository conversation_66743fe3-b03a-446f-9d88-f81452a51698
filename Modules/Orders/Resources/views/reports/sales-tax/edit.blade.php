@extends('layouts.master')

@section('title', 'Sales Tax Reports')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('orderModule.reports.salesTax.index') }}">Sales Tax Reports</a></li>
                            <li class="breadcrumb-item active">Generate Sales Tax Report</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Generate Sales Tax Report</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-8">
                <div class="card">
                    <div class="card-body">
                        <form action="{{ route('orderModule.reports.salesTax.update', $report) }}" method="post">
                            @csrf
                            @method('put')
                            <div class="form-row mb-2">
                                <div class="col-md-6">
                                    <label for="tax-period">Tax Period</label>
                                    <select class="form-control"
                                            name="period"
                                            id="tax-period"
                                            required>
                                        @foreach($taxPeriods as $period)
                                            <option value="{{ $period }}" {{ $report->period === $period ? 'selected' : '' }}>{{ $period }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="current-tax-rate">Current Tax Rate</label>
                                    <input class="form-control"
                                           type="number"
                                           step="0.0001"
                                           name="tax_rate"
                                           id="current-tax-rate"
                                           value="{{ old('tax_rate', $report->tax_rate) }}"
                                           required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="tax-free-amount">Amount for items purchased tax free and used by Us</label>
                                <input class="form-control"
                                       type="number"
                                       step="0.01"
                                       name="tax_free_amount"
                                       id="tax-free-amount"
                                       value="{{ old('tax_free_amount', $report->tax_free_amount) }}">
                            </div>
                            <div class="form-row mb-3">
                                <div class="col-md-4">
                                    <label for="adjustment-amount">Any existing adjustment</label>
                                    <input class="form-control"
                                           type="number"
                                           step="0.01"
                                           name="adjustment_amount"
                                           id="adjustment-amount"
                                           value="{{ old('adjustment_amount', $report->adjustment_amount) }}">
                                </div>
                                <div class="col-md-8">
                                    <label for="adjustment-amount-reason">Adjustment Reason</label>
                                    <input class="form-control"
                                           type="text"
                                           name="adjustment_reason"
                                           id="adjustment-amount-reason"
                                           value="{{ old('adjustment_reason', $report->adjustment_reason) }}">
                                </div>
                            </div>
                            <div class="form-group text-right">
                                <p class="text-danger">By generating this report, you are confirming that all orders that were invoiced in the selected period will not be editable.</p>
                                <a class="btn btn-light mr-1" href="{{ route('orderModule.reports.salesTax.index') }}">Cancel</a>
                                <button class="btn btn-primary" type="submit">Generate Report</button>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col -->
        </div>
        <!-- end row -->
    </div>
    <!-- container -->
@endsection
