<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  @include('shared.print.default-print-styles')
</head>
<body>
<!-- end row -->
<div class="mb-sm">
  <div class="col-sm-12">
    <table class="table table-bordered text-dark">
      <thead class="thead-light">
      <tr>
        <th colspan="10" style="padding: 30px 0; border: solid 2.5px #555;">
          <div class="text-right" style="font-weight: normal; padding-right: 5px;">Printed on {{ $report->printed_at->format('m/d/Y') }}</div>
          <div style="font-size: 30px; margin-top: -20px">Our Cost Sales Tax Report for {{ $report->period }}</div>
        </th>
      </tr>
      <tr>
        <th style="border-left: solid 2.5px #555; border-bottom: solid 2.5px #555;">Final Date</th>
        <th style="border-bottom: solid 2.5px #555;">Order#</th>
        <th class="text-right" style="border-bottom: solid 2.5px #555;">Qty</th>
        <th style="border-bottom: solid 2.5px #555;">Description</th>
        <th style="border-bottom: solid 2.5px #555;">Part#</th>
        <th class="text-right" style="border-bottom: solid 2.5px #555;">Price</th>
        <th class="text-right" style="border-bottom: solid 2.5px #555;">Total</th>
        <th class="text-right" style="border-bottom: solid 2.5px #555;">Cost</th>
        <th class="text-right" style="border-bottom: solid 2.5px #555;">Tax</th>
        <th style="border-right: solid 2.5px #555; border-bottom: solid 2.5px #555;">Invoice#</th>
      </tr>
      </thead>
      <tbody>
      @php $grandTax = 0; $grandCost = 0; $grandTotal = 0; @endphp
      @foreach($orderGroups as $customerId => $customerOrders)
        @php $totalTax = 0; $grandTotal += ($customerTotalAmount = $customerOrders->sum('total_amount') - $customerOrders->sum('service_cost')); @endphp
        @foreach($customerOrders as $order)
          @foreach($order->items as $item)
            @php $totalTax += $item->salesTax($report->tax_rate); @endphp
            @if($loop->first)
              <tr>
                <td>{{ $order->final_date }}</td>
                <td>{{ $order->full_number }}</td>
                <td class="text-right">{{ round($item->real_quantity) }}</td>
                <td>{{ $item->description }}</td>
                <td>{{ $item->code }}</td>
                <td class="text-right">{{ number_format($item->price, 2) }}</td>
                <td class="text-right">{{ number_format($item->amount, 2) }}</td>
                <td class="text-right">{{ number_format($item->total_cost, 2) }}</td>
                <td class="text-right">
                  {{ number_format($item->salesTax($report->tax_rate), 2) }}
                </td>
                <td>{{ $order->number }}</td>
              </tr>
            @else
              @if (! empty($item->cost))
                <tr>
                  <td></td>
                  <td></td>
                  <td class="text-right">{{ round($item->real_quantity) }}</td>
                  <td>{{ $item->description }}</td>
                  <td>{{ $item->code }}</td>
                  <td class="text-right">{{ number_format($item->price, 2) }} </td>
                  <td class="text-right">{{ number_format($item->total_sale_price, 2) }}</td>
                  <td class="text-right">{{ number_format($item->total_cost, 2) }}</td>
                  <td class="text-right">
                    {{ number_format($item->salesTax($report->tax_rate), 2) }}
                  </td>
                  <td></td>
                </tr>
              @endif
            @endif
          @endforeach
        @endforeach
        <tr>
          <td
            colspan="7"
            class="text-right font-weight-bold"
            style="border-top: solid 2px #555; border-bottom: solid 2px #555;">
            <span
              class="d-inline-block mr-2">
              Totals for {{ $customerOrders->first()->customer->name }}
            </span>
            {{ number_format($customerTotalAmount, 2) }}
          </td>
          <td class="text-right font-weight-bold"
              style="border-top: solid 2px #555; border-bottom: solid 2px #555;">
            {{ number_format($customerOrders->sum('total_cost'), 2) }}</td>
          <td class="text-right font-weight-bold" style="border-top: solid 2px #555; border-bottom: solid 2px #555;">
            {{ number_format($totalTax, 2) }}
            @php $grandTax += $totalTax; $grandCost += $customerOrders->sum('total_cost'); @endphp
          </td>
          <td style="border-top: solid 2px #555; border-bottom: solid 2px #555;"></td>
        </tr>
      @endforeach
      <tr>
        <td class="py-1" colspan="10" style="border-bottom: solid 2px #555;"></td>
      </tr>
      <tr>
        <td colspan="7" class="text-right font-weight-bold" style="border-top: solid 3px #555;">
          <span class="d-inline-block mr-2">Report Totals</span> {{ number_format($grandTotal, 2) }}
        </td>
        <td class="text-right font-weight-bold"
            style="border-top: solid 3px #555;">{{ number_format($grandCost, 2) }}</td>
        <td class="text-right font-weight-bold"
            style="border-top: solid 3px #555;">{{ number_format($grandTax, 2) }}</td>
        <td style="border-top: solid 3px #555;"></td>
      </tr>
      </tbody>
    </table>
  </div>
</div>
<!-- container -->
<script type="text/php">
    $text = "Page {PAGE_NUM} of {PAGE_COUNT}";
    $size = 10;
    $font = $fontMetrics->getFont("Verdana");
    $width = $fontMetrics->get_text_width($text, $font, $size) / 2;
    $x = ($pdf->get_width() - $width) + 2;
    $y = 82;
    $pdf->page_text($x, $y, $text, $font, $size, array(0,0,0));

</script>
</body>
</html>
