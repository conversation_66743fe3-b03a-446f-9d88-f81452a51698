@extends('layouts.master')

@section('title', 'Sales Tax Report')

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item active">Sales Tax Report</li>
            </ol>
          </div>
          <h4 class="page-title">Sales Tax Report</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row justify-content-center">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row mb-2">
              <div class="col-sm-12 text-right">
                <a href="{{ route('orderModule.reports.salesTax.showSummaryReport', $report) }}"
                   class="btn btn-light mb-2">
                  <i class="mdi mdi-eye-outline mr-1"></i>
                  View Summary Report
                </a>
                <a href="{{ route('orderModule.reports.salesTax.printSalesReport', $report) }}"
                   class="btn btn-light mb-2">
                  <i class="mdi mdi-file-pdf mr-1"></i>
                  Print Our Cost Report
                </a>
                @if (empty($report->printed_at))
                  <a href="{{ route('orderModule.reports.salesTax.edit', $report) }}"
                     class="btn btn-warning mb-2">
                    <i class="mdi mdi-pen mr-1"></i>
                    Edit before Printing
                  </a>
                @endif
              </div><!-- end col-->
            </div>

            <div class="table-responsive">
              <table class="table table-centered table-sm dt-responsive nowrap">
                <thead class="thead-light">
                <tr>
                  <th class="text-center py-3 bg-white font-18" colspan="10">
                    Our Cost Sales Tax Report for {{ $report->period }}
                  </th>
                </tr>
                <tr>
                  <th class="bg-white">Final Date</th>
                  <th class="bg-white">Order#</th>
                  <th class="text-right bg-white">Qty</th>
                  <th class="bg-white">Description</th>
                  <th class="bg-white">Part#</th>
                  <th class="text-right bg-white">Price</th>
                  <th class="text-right bg-white">Total</th>
                  <th class="text-right bg-white">Cost</th>
                  <th class="text-right bg-white">Tax</th>
                  <th class="bg-white">Invoice#</th>
                </tr>
                </thead>
                <tbody>
                @php $grandTax = 0; $grandCost = 0; $grandTotal = 0; @endphp
                @foreach($orderGroups as $customerId => $customerOrders)

                  @php
                    $totalTax = 0;
                    $grandTotal += ($customerTotalAmount = $customerOrders->sum('total_amount') - $customerOrders->sum('service_cost'));
                  @endphp
                  @foreach($customerOrders as $order)
                    @foreach($order->items as $item)
                      @php $totalTax += $item->salesTax($report->tax_rate); @endphp
                      @if($loop->first)
                        <tr>
                          <td>{{ $order->final_date }}</td>
                          <td>{{ $order->full_number }}</td>
                          <td class="text-right">{{ round($item->real_quantity, 2) }}</td>
                          <td>{{ $item->description }}</td>
                          <td>{{ $item->code }}</td>
                          <td class="text-right">{{ number_format($item->price, 2) }}</td>
                          <td class="text-right">{{ number_format($item->amount, 2) }}</td>
                          <td class="text-right">{{ number_format($item->total_cost, 2) }}</td>
                          <td class="text-right">
                            {{ number_format($item->salesTax($report->tax_rate), 2) }}
                          </td>
                          <td>{{ $order->number }}</td>
                        </tr>
                      @else
                        @if (! empty($item->cost))
                          <tr>
                            <td></td>
                            <td></td>
                            <td class="text-right">{{ round($item->real_quantity) }}</td>
                            <td>{{ $item->description }}</td>
                            <td>{{ $item->code }}</td>
                            <td class="text-right">{{ number_format($item->price, 2) }} </td>
                            <td class="text-right">{{ number_format($item->amount, 2) }}</td>
                            <td class="text-right">{{ number_format($item->total_cost, 2) }}</td>
                            <td class="text-right">
                              {{ number_format($item->salesTax($report->tax_rate), 2) }}
                            </td>
                            <td></td>
                          </tr>
                        @endif
                      @endif
                    @endforeach
                  @endforeach
                  <tr>
                    <td colspan="7" class="text-right font-weight-bold"
                        style="border-top: solid 2px #555; border-bottom: solid 2px #555;">
                      <span
                        class="d-inline-block mr-2">Totals for {{ $customerOrders->first()->customer->name }}</span> {{ number_format($customerTotalAmount, 2) }}
                    </td>
                    <td class="text-right font-weight-bold"
                        style="border-top: solid 2px #555; border-bottom: solid 2px #555;">
                      {{ number_format($customerOrders->sum('total_cost'), 2) }}</td>
                    <td class="text-right font-weight-bold"
                        style="border-top: solid 2px #555; border-bottom: solid 2px #555;">
                      {{ number_format($totalTax, 2) }}
                      @php $grandTax += $totalTax; $grandCost += $customerOrders->sum('total_cost'); @endphp
                    </td>
                    <td style="border-top: solid 2px #555; border-bottom: solid 2px #555;"></td>
                  </tr>
                @endforeach
                <tr>
                  <td class="py-1" colspan="10" style="border-bottom: solid 2px #555;"></td>
                </tr>
                <tr>
                  <td colspan="7" class="text-right font-weight-bold" style="border-top: solid 3px #555;">
                    <span class="d-inline-block mr-2">Report Totals</span> {{ number_format($grandTotal, 2) }}
                  </td>
                  <td class="text-right font-weight-bold"
                      style="border-top: solid 3px #555;">{{ number_format($grandCost, 2) }}</td>
                  <td class="text-right font-weight-bold"
                      style="border-top: solid 3px #555;">{{ number_format($grandTax, 2) }}</td>
                  <td style="border-top: solid 3px #555;"></td>
                </tr>
                </tbody>
              </table>
            </div>
          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>
  <!-- container -->
@endsection
