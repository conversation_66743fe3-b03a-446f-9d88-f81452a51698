@extends('layouts.master')

@section('title', 'Open Invoices')

@push('styles')
  <style>
    .page { width: 100%; height: 1500px; padding: 20px; position: relative; }
    .page .content .column { text-align:justify; font-size: 10pt; }
    .page .content .column blockquote{ border-left: 2px solid #999999; background: #DEDEDE; padding: 10px; margin: 4px 20px; clear: both; }
    .page .content .column img{ float: left; margin: 10px; }
    .page .content .column p{ padding: 0 10px; margin: 10px 0; }
    .page .content .column h1{ padding: 0 10px; }
    .page .header {

    }
    .page .header .title,
    .page .header .header-meta div {
      display: inline-block;
      width: 49.8%;
    }

    .page .header .title {
      border-bottom: 3px solid;
      padding-right: 20px;
    }

    .page .header span,
    .page .page-invoice span {
      display: inline-block;
      width: 24.3%;
    }
    .page .header hr, .page .footer hr{ width: 400px; }
    .page .first {
      padding-right: 20px;
      border-right: 1px solid;
    }
    .page .last {
      padding-left: 20px;
    }
    .page .footer{ text-align: center; }
    .page .footer span{ position: absolute; bottom: 10px; right: 10px; }
    #page_template{ display: none; }

    .enclosure {border:1px dashed black}
    .customer-total {
      overflow: auto;
    }
    .customer-total span:first-child {
      border-top: 1px solid #999999;
    }
    .customer-total span {
      display: inline-block;
      border-bottom:1px dashed #000;
      width: 15%;
      clear: both;
      float: right;
      margin-bottom: 2px;
    }
  </style>
@endpush
@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">Open Invoices</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Open Invoices</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center text-right d-print-none">
                        <form class="w-50" action="">
                          <div class="row">
                            <div class="col-5">
                              <input type="date" name="start_date" class="form-control" value="{{ old('start_date', $startDate) }}">
                            </div>
                            <div class="col-5">
                              <input type="date" name="end_date" class="form-control" value="{{ old('end_date', $endDate) }}">
                            </div>
                            <div class="col-2">
                              <button type="submit" class="btn btn-sm btn-outline-secondary"><i class="mdi mdi-filter"></i> Filter</button>
                            </div>
                          </div>
                        </form>
                        <a class="btn btn-light px-3" href="#" onclick="window.print()"><i class="mdi mdi-printer"></i> Print</a>
                    </div>
                    <div class="card-body">
                      @include('orders::reports.open-invoices.partials.orders-list')
                      <div class="print-wrapper"></div>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col -->
        </div>
        <!-- end row -->
    </div>

    <!-- container -->
@endsection

@push('js')
  <script src="{{ asset('vendor/columnizer/jquery.columnizer.js') }}"></script>
  <script>
    (function () {
      // the height of the content, discluding the header/footer
      const content_height = 1340; /* For a commom A4 page, the height will be something next to this */
      // the beginning page number to show in the footer
      let page = 1;
      const mainContent = $('#newsletterContent');

      function buildNewsletter() {
        if (mainContent.contents().length > 0) {
          // when we need to add a new page, use a jq object for a template
          // or use a long HTML string, whatever your preference
          $page = $("#page_template").clone().addClass("page").css("display", "block");

          // append the page number to the footer
          $page.find(".page-number").append('Page#' + page);
          $('.print-wrapper').append($page);
          page++;

          // here is the columnizer magic
          mainContent.columnize({
            columns: 2,
            target: ".page:last .content",
            overflow: {
              height: content_height,
              id: "#newsletterContent",
              doneFunc: function() {
                buildNewsletter();
              }
            }
          });
        }
      }
      setTimeout(buildNewsletter, 500);
    }());
  </script>
@endpush
