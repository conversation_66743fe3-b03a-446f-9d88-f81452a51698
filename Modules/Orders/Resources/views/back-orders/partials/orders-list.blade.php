<div class="table-responsive">
  <table class="table table-centered w-100 dt-responsive nowrap datatable search-autofocus">
    <thead class="thead-light">
    <tr>
      <th class="text-right">Order #</th>
      <th class="text-right">Account #</th>
      <th>Customer</th>
      <th class="text-right">Amount</th>
      <th>Created By</th>
      <th>Date</th>
      <th>Status</th>
      <th>Invoiced</th>
      <th>From</th>
      <th class="text-right" style="min-width: 85px;">Action</th>
    </tr>
    </thead>
    <tbody>
    @foreach ($orders as $order)
      <tr class="{{ $order->canceled() ? 'bg-danger-lighten text-dark' : '' }}">
        <td class="text-right">
          <a href="{{ route('orderModule.orders.show', $order) }}">
            @if ($order->itemsToBuild()->count()) <i class="mdi mdi-settings-outline text-muted" data-toggle="tooltip"
                                                     data-placement="top" title=""
                                                     data-original-title="Order has items to build"></i> @endif
            {{ $order->createdBy->staff->initials . $order->number }}
          </a>
        </td>
        <td class="text-right">{{ $order->customer->account_number }}</td>
        <td><a href="{{ route('users.customers.show', $order->customer) }}">{{ $order->customer->name }}</a></td>
        <td class="text-right">{{ number_format($order->total_amount, 2) }}</td>
        <td><a href="{{ route('users.staff.show', $order->createdBy) }}">{{ $order->createdBy->name }}</a></td>
        <td>{{ $order->created_at->format('M d, Y') }}</td>
        <td>{{ ucfirst($order->formatted_status) }}</td>
        <td>{{ $order->invoice_status }}</td>
        <td><a href="{{ route('orderModule.orders.show', $order->parent) }}">{{ $order->parent->formatted_number }}</a>
        </td>
        <td class="table-action text-right">
          <a href="{{ route('orderModule.orders.show', $order) }}" class="mr-1 d-inline-block">
            <i class="mdi mdi-eye"></i>
            View
          </a>
          @if($order->notInvoiced())
            @can('delete order')
              <a href="#"
                 data-target="#deleteItem"
                 data-toggle="modal"
                 class="delete-item-btn text-muted"
                 data-url="{{ route('orderModule.orders.destroy', $order) }}">
                <i class="mdi mdi-delete"></i>
                Delete
              </a>
            @endcan
          @endif
        </td>
      </tr>
    @endforeach
    </tbody>
  </table>
</div>
