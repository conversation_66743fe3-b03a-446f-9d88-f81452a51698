@extends('layouts.master')

@section('title', 'Edit Payment #' . $payment->number)

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('orderModule.orders.index') }}">Orders</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('orderModule.orders.show', $payment->payable) }}">Order #{{ $payment->payable->formatted_number }}</a></li>
                            <li class="breadcrumb-item active">Edit Payment #{{ $payment->number }}</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Payment</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-sm-8">
                <div class="card">
                    <div class="card-body px-5 py-4">

                        <form action="{{ route('orderModule.orders.payments.update', ['order' => $payment->payable, 'payment' => $payment]) }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            @method('put')
                            @include('orders::orders.payments.partials.form')
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection
