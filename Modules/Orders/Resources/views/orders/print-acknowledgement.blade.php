<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ config('app.name') }} Acknowledgement for Order #{{ $order->formatted_number }}</title>
  @include('shared.print.default-print-styles')
</head>
<body>

<div class="mb-sm">
  @include('shared.print.pacific_logo_address')
  <div class="text-center" style="width: 49%; display: inline-block; float: right;">
    <h3 style="margin: 0 0 8px; color: #888;">Acknowledgement</h3>

    <table class="table text-center">
      <tr class="bg-gray">
        <th class="border-right-0 border-bottom-0">Order No.</th>
        <th class="border-right-0 border-bottom-0">Order Date</th>
        <th class="border-bottom-0">Account No.</th>
      </tr>
      <tr class="text-dark">
        <td class="border-right-0">{{ $order->full_number }}</td>
        <td class="border-right-0">{{ $order->created_at->format('m/d/y') }}</td>
        <td class="">{{ $order->customer->account_number }}</td>
      </tr>
    </table>
  </div>
  <div style="clear: both;"></div>
</div>
<div class="mb-sm" style="overflow: auto;">
  <div style="width: 49%; display: inline-block; float: left;">
    <table class="table">
      <tr class="bg-gray">
        <th class="text-left border-bottom-0">Sold To:</th>
      </tr>
      <tr>
        <td>
          <div>{{ optional($order->customer->accountAddress)->name }}</div>
          <div style="white-space: pre-wrap;">{!! optional($order->customer->accountAddress)->full_address !!}</div>
        </td>
      </tr>
    </table>
    Fax: {{ $order->customer->fax ?? '-' }}
  </div> <!-- end col-->
  <div style="width: 49%; display: inline-block; float: right">
    <table class="table">
      <tr class="bg-gray">
        <th class="text-left border-bottom-0">Ship To:</th>
      </tr>
      <tr>
        <td>
          <div>{{ $order->shipping_name }}</div>
          <div style="white-space: pre-wrap;">{!! $order->shipping_address !!}</div>
        </td>
      </tr>
    </table>
  </div> <!-- end col-->
  <div style="clear: both;"></div>
</div>
<!-- end row -->
<div class="mb-sm">
  <table class="table table-bordered text-dark text-center">
    <tr class="bg-gray">
      <th class="border-bottom-0 border-right-0">PO Number</th>
      <th class="border-bottom-0 border-right-0">Order Date</th>
      <th class="border-bottom-0 border-right-0">Ship Date</th>
      <th class="border-bottom-0 border-right-0">Ship Via</th>
      <th class="border-bottom-0 border-right-0">Salesperson</th>
      <th class="border-bottom-0">Payment Terms</th>
    </tr>
    <tr>
      <td class="border-right-0">{{ $order->purchase_order_number }}</td>
      <td class="border-right-0">{{ $order->created_at->format('m/d/y') }}</td>
      <td class="border-right-0">{{ $order->formatted_shipping_date }}</td>
      <td class="border-right-0">{{ $order->shipping_via }}</td>
      <td class="border-right-0">{{ $order->createdBy->name }}</td>
      <td>{{ $order->customer->payment_terms }}</td>
    </tr>
  </table>
</div>
<div class="mb-sm">
  <div class="col-sm-12">
    <table class="table table-bordered text-dark mb-1">
      <thead>
      <tr class="bg-gray text-uppercase">
        <th class="text-right p-1 border-bottom-0 border-right-0">Qty</th>
        <th class=" border-bottom-0 border-right-0">Part No.</th>
        <th class=" border-bottom-0 border-right-0">Description</th>
        <th class="p-1 border-bottom-0 border-right-0" style="text-align: right">Price</th>
        <th class="p-1 border-bottom-0" style="text-align: right">Total</th>
      </tr>
      </thead>
      <tbody>
      @foreach($order->items as $item)
        <tr>
          <td class="border-bottom-0 border-right-0" style="text-align: right">{{ $item->real_quantity }}</td>
          <td class="border-bottom-0 border-right-0">{{ $item->code }}</td>
          <td class="border-bottom-0 border-right-0"
            style="{{ str_multiline($item->description) ? 'white-space: pre-wrap; ' : 'word-break: break-all; ' }}">{{ $item->description }}</td>
          <td class="border-bottom-0 border-right-0" style="text-align: right">{{ number_format($item->price, 2)  }}</td>
          <td class="border-bottom-0" style="text-align: right">${{ number_format($item->real_quantity * $item->price, 2) }}</td>
        </tr>
      @endforeach

      @if($order->isService())
        <tr>
          <td class="border-bottom-0 border-right-0" style="text-align: right">{{ $order->service_hours }}</td>
          <td class="border-bottom-0 border-right-0"></td>
          <td class="border-bottom-0 border-right-0">Labor</td>
          <td class="border-bottom-0 border-right-0" style="text-align: right">${{ number_format($order->service_rate, 2) }}</td>
          <td class="border-bottom-0" style="text-align: right">${{ number_format($order->service_rate * $order->service_hours, 2) }}</td>
        </tr>
        <tr>
          <td class="border-bottom-0 border-right-0" style="text-align: right">1</td>
          <td class="border-bottom-0 border-right-0"></td>
          <td class="border-bottom-0 border-right-0">Service Call</td>
          <td class="border-bottom-0 border-right-0" style="text-align: right">${{ number_format($order->service_call, 2) }}</td>
          <td class="border-bottom-0" style="text-align: right">${{ number_format($order->service_call, 2) }}</td>
        </tr>
      @endif
      <tr>
        <td colspan="5" class="border-bottom-0" style="padding-top: 10px"></td>
      </tr>
      </tbody>
      <tfoot class="border-0">
      <tr class="border-0">
        <td colspan="3" class="border-right-0"></td>
        <td class="border-0" colspan="2" style="padding: 0">
          <table class="table" style="border: none;">
            <tr>
              <td class="text-right font-weight-bold border-bottom-0 border-right-0">Sub Total</td>
              <td class="font-weight-bold text-right border-bottom-0">
                ${{ number_format($order->total, 2) }}</td>
            </tr>
            <tr>
              <td class="text-right font-weight-bold border-bottom-0 border-right-0">Tax</td>
              <td class="font-weight-bold text-right border-bottom-0">${{ number_format($order->tax_amount, 2) }}</td>
            </tr>
            <tr>
              <td class="text-right font-weight-bold border-bottom-0 border-right-0">Shipping</td>
              <td class="font-weight-bold text-right border-bottom-0">
                @if (! is_null($order->shipping_cost))
                ${{ number_format($order->shipping_cost, 2) }}
                @endif
              </td>
            </tr>
            <tr>
              <td class="text-right font-weight-bold font-18 bg-gray border-right-0"
                  style="font-weight: bold; border-bottom: 0;">Order Total
              </td>
              <td class="font-18 font-weight-bold text-right"
                  style="font-weight: bold;">
                ${{ number_format($order->total_amount, 2) }}</td>
            </tr>
          </table>
        </td>
      </tr>
      </tfoot>
    </table>
    <p><strong>Unless stated above, Order Total does not include Shipping Charges or Sales Tax.</strong></p>
  </div> <!-- end col -->
</div>
<!-- end row -->
<!-- container -->
</body>
</html>
