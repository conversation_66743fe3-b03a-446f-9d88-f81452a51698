<div
  id="emailInvoiceModal"
  class="modal fade"
  tabindex="-1"
  role="dialog"
  style="display: none;"
  aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-alert h1 mr-2"></i>
          <h4 class="mt-2">Confirm Action!</h4>
        </div>
        <div class="mt-3">
          <p class="text-muted">This will send an invoice to the customer's email below. Are you sure about this?</p>
          <form action="{{ route('orderModule.orders.sendInvoice', $order) }}" method="post">
            @csrf
            <div class="form-group">
              <label for="invoice-emails">Recipients <small class="text-muted">- Separate multiple emails with a comma.</small></label>
              <input
                class="form-control"
                type="text"
                name="recipients"
                id="invoice-emails"
                value="{{ old('recipients', $order->customer->invoice_recipients) }}"
                required>
            </div>
            <div class="form-group">
              <label>Message <small>- customize the message to send along with invoice.</small></label>
              <textarea
                class="form-control"
                name="body"
                rows="5"
                required>{{ settings('global.emails.invoices.message', '') }}</textarea>
              <small>This text will be replaced: <strong>$customer</strong> by Customer Name, <strong>$order</strong> by Order Number</small>
            </div>
            <div class="d-flex justify-content-end">
              <button type="button" class="btn btn-light my-2 mr-2 px-3" data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-primary my-2 px-3">Yes, Send Email <i class="mdi mdi-send ml-2"></i></button>
            </div>
          </form>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
