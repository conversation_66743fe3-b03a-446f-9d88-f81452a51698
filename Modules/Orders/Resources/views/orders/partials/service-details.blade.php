<div class="card d-print-none">
  <div class="card-body">
    <div class="row">
      <div class="col-sm-6">
        <h4 class="header-title">Service Cost</h4>
      </div>
      <div class="col-sm-6 text-right">
        <button class="btn btn-outline-secondary btn-sm" data-toggle="modal" data-target="#updateServiceDetails">Edit Service Details</button>
      </div>
    </div>
    <div class="d-flex justify-content-start">
      <div class="card mr-2">
        <div class="card-body px-4 py-3 text-center">
          <h6 class="h3">{{ $order->service_hours }}</h6>
          <span>Service Hours</span>
        </div>
      </div>
      <div class="card mr-2">
        <div class="card-body px-4 py-3 text-center">
          <h6 class="h3">${{ number_format($order->service_rate, 2) }}</h6>
          <span>Service Rate</span>
        </div>
      </div>
      <div class="card mr-2">
        <div class="card-body px-4 py-3 text-center">
          <h6 class="h3">${{ number_format($order->service_call, 2) }}</h6>
          <span>Service Call</span>
        </div>
      </div>
      <div class="card mr-2">
        <div class="card-body px-4 py-3 text-center">
          <h6 class="h3">${{ number_format($order->service_cost, 2) }}</h6>
          <span>Service Cost</span>
        </div>
      </div>
    </div>

  </div>
</div>
<div class="card d-print-none">
  <div class="card-body">
    <div class="row mb-3">
      <div class="col-md-6">
        <h4 class="header-title mb-3">Service Images</h4>
      </div>
      <div class="col-md-6 text-right">
        <a href="#" data-toggle="modal" data-target="#uploadServiceImage" class="btn btn-sm btn-outline-secondary">Upload Service Image</a>
      </div>
    </div>

    <div class="mb-3">
      <h6>Before Service</h6>
      <div class="row">
        @forelse($order->getMedia('beforeService') as $image)
          <div class="col-md-4 col-sm-6">
            <img src="{{ $image->getUrl('service') }}" alt="" class="img-fluid mb-1">
            <form method="post" action="{{ route('orderModule.orders.images.destroy', [$order, $image]) }}">
              @csrf
              @method('delete')
              <button class="btn btn-sm btn-outline-danger" type="submit">
                <i class="mdi mdi-delete"></i>
              </button>
            </form>
          </div>
        @empty
          <div class="col-sm-12">
            <p>Image before service not uploaded</p>
          </div>
        @endforelse
      </div>
    </div>

    <div class="mb-3">
      <h6>After Service</h6>
      <div class="row justify-content-start">
        @forelse($order->getMedia('afterService') as $image)
          <div class="col-md-4 col-sm-6">
            <img src="{{ $image->getUrl('service') }}" alt="" class="img-fluid mb-1">
            <form method="post" action="{{ route('orderModule.orders.images.destroy', [$order, $image]) }}">
              @csrf
              @method('delete')
              <button class="btn btn-sm btn-outline-danger" type="submit">
                <i class="mdi mdi-delete"></i>
              </button>
            </form>
          </div>
        @empty
          <div class="col-sm-12">
            <p>Image after service not uploaded</p>
          </div>
        @endforelse
      </div>
    </div>
  </div>
</div>
