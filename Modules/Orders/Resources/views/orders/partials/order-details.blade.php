@if($order->notCanceled())
  <div class="row mb-3 d-print-none">
    @include('orders::orders.partials.requirements-notice')
    <div class="col-sm-12 col-md-6 offset-6 text-right">
      <div class="mb-1">
        @include('orders::orders.partials.order-details-menu')
      </div>
    </div>
  </div>
@endif
<div class="row mb-2 justify-content-center d-print-none">
  @if($order->canceled())
    <div class="col-sm-12">
      <div class="card bg-danger">
        <div class="card-body text-center text-white">
          <h3 class="text-uppercase">Canceled</h3>
          <small><span class="text-white-50">By:</span> {{ optional($order->canceled_by)->name }} <span
              class="text-white-50">At:</span> {{ $order->formatted_canceled_at }}</small>
        </div>
      </div>
    </div>
  @else
    @include('orders::orders.partials.order-status')
  @endif
</div>

<div class="row">
  <!-- start Real time editing action -->

  <div id="order-editing-vue-wrapper" class="col-12">
    <order-editing-action user="{{ auth()->user() }}"/>
  </div>

  <!-- end Real time editing action -->

  <div class="col-sm-12">
    <div class="card">
      <div class="card-body">
        @include('orders::orders.partials.forms.order-form')
      </div> <!-- end card-body-->
    </div> <!-- end card -->
  </div> <!-- end col-->
</div>

<div class="row">
  <div class="col-md-12">
    @include('orders::orders.partials.assignments')
    @include('orders::orders.partials.status')

    @if($order->isService())
      @include('orders::orders.partials.service-details')
    @endif

    @include('orders::orders.partials.attachments')
  </div>
</div>
