@extends('layouts.master')

@section('title', 'Invoice for Order #' . $order->id)

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('orderModule.orders.index') }}">Orders</a></li>
              <li class="breadcrumb-item"><a
                  href="{{ route('orderModule.orders.show', $order) }}">Order {{ $order->full_number }}</a></li>
              <li class="breadcrumb-item active">Invoice</li>
            </ol>
          </div>
          <h4 class="page-title"><a
              href="{{ route('users.customers.show', $order->customer) }}">{{ $order->customer->name }}'s</a> <span
              class="text-muted"> | </span> Invoice for Order {{ $order->createdBy->staff->initials . $order->number }}
          </h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row">
      @include('orders::orders.partials.requirements-notice')
      <div class="col-sm-12">
        <div class="row mb-3">
          <div class="col-sm-12">
            <div class="text-right">
              <div class="mb-1">
                @can('invoice order')
                  @if($pricingCompleted)
                    <a href="{{ route('orderModule.orders.downloadInvoice', $order) }}"
                       class="btn btn-secondary mr-1"
                       title="Opens printable invoice in new window">
                      <i class="mdi mdi-printer"></i>
                      Printable Invoice
                    </a>
                    <a
                      class="btn btn-primary mr-1"
                      href="#"
                      data-target="#emailInvoiceModal"
                      data-toggle="modal">
                      <i class="mdi mdi-send"></i> Send Invoice
                    </a>
                  @endif
                  @if($order->editable)
                    <div class="btn-group">
                      <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown"
                              aria-haspopup="true" aria-expanded="false">
                        <i class="mdi mdi mdi-dots-vertical"></i> Actions
                      </button>
                      <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end"
                           style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(285px, 37px, 0px);">
                        <a href="#"
                           data-toggle="modal"
                           data-target="#invoiceNoteModal"
                           class="dropdown-item">
                          <i class="mdi mdi-pencil mr-1"></i>
                          Edit Note
                        </a>
                      </div>
                    </div>
                  @endif
                @endcan
              </div>
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-body">
            <div class="row mb-2">
              <div class="col-md-6">
                <img class="mb-0" src="{{ asset('images/logo.png') }}" alt="{{ config('app.name') }} logo">
                <span class="d-block pl-5" style="margin-top: -10px">
                  200 W. Haven Ave. <br>
                  Salt Lake City, UT 84115 <br>
                  Phone (*************
                </span>
              </div>
              <div class="col-md-6 text-center">
                <h3 class="text-muted">Sales Invoice</h3>

                <table class="table table-bordered">
                  <tr class="text-uppercase text-dark bg-secondary-lighten">
                    <th class="p-1">Invoice No.</th>
                    <th class="p-1">Invoice Date</th>
                    <th class="p-1">Account No.</th>
                  </tr>
                  <tr class="text-dark">
                    <td>{{ $order->number }}</td>
                    <td>{{ $order->formatted_invoice_date }}</td>
                    <td>{{ $order->customer->account_number }}</td>
                  </tr>
                </table>
              </div>
            </div>

            <div class="row mb-2">
              <div class="col-sm-6">
                <table class="table table-bordered">
                  <tr class="text-uppercase text-dark bg-secondary-lighten">
                    <th class="p-1">Bill To:</th>
                  </tr>
                  <tr>
                    <td>
                      @if(! \Illuminate\Support\Str::contains($order->billing_address, $order->billing_name))
                        <div>{{ $order->billing_name }}</div>
                      @endif
                      <div style="white-space: pre-wrap;">{{ $order->billing_address }}</div>
                    </td>
                  </tr>
                </table>
              </div> <!-- end col-->

              <div class="col-sm-6">
                <table class="table table-bordered">
                  <tr class="text-uppercase text-dark bg-secondary-lighten">
                    <th class="p-1">Ship To:</th>
                  </tr>
                  <tr>
                    <td>
                      <div>{{ $order->shipping_name }}</div>
                      <div style="white-space: pre-wrap;">{{ $order->shipping_address }}</div>
                    </td>
                  </tr>
                </table>
              </div> <!-- end col-->
            </div>
            <!-- end row -->
            <div class="row mb-3">
              <div class="col-sm-12">
                <table class="table table-bordered text-center">
                  <tr class="text-uppercase text-dark bg-secondary-lighten">
                    <th class="p-1">PO Number</th>
                    <th class="p-1">Order Date</th>
                    <th class="p-1">Date Shipped</th>
                    <th class="p-1">Ship Via</th>
                    <th class="p-1">Salesperson</th>
                    <th class="p-1">Payment Terms</th>
                  </tr>
                  <tr>
                    <td>{{ $order->purchase_order_number }}</td>
                    <td>{{ $order->created_at->format('m/d/y') }}</td>
                    <td>{{ $order->formatted_shipping_date }}</td>
                    <td>{{ $order->shipping_via }}</td>
                    <td>{{ $order->salesperson }}</td>
                    <td>{{ $order->payment_terms }}</td>
                  </tr>
                </table>
              </div>
            </div>
            <div class="row mb-5">
              <div class="col-sm-12">
                <table class="table table-bordered mb-1">
                  <thead>
                  <tr class="bg-secondary-lighten text-uppercase text-dark">
                    <th class="text-right p-1">Qty</th>
                    <th class="p-1">Part No.</th>
                    <th class="p-1">Description</th>
                    <th class="text-right p-1">Price</th>
                    <th class="text-right p-1">Total</th>
                  </tr>
                  </thead>
                  <tbody>
                  @foreach($order->items as $item)
                    <tr>
                      <td class="text-right">{{ $itemQty = $item->real_quantity }}</td>
                      <td>{{ $item->code }}</td>
                      <td>
                        <div style="white-space: pre-wrap;">{{ $item->description }}</div>
                      </td>
                      <td class="text-right">${{ number_format($item->price, 2) }}</td>
                      <td class="text-right">${{ number_format($itemQty * $item->price, 2) }}</td>
                    </tr>
                  @endforeach

                  @if($order->isService())
                    <tr>
                      <td class="text-right">{{ $order->service_hours }}</td>
                      <td></td>
                      <td>Labor</td>
                      <td class="text-right">${{ number_format($order->service_rate, 2) }}</td>
                      <td class="text-right">${{ number_format($order->service_rate * $order->service_hours, 2) }}</td>
                    </tr>
                    <tr>
                      <td class="text-right">1</td>
                      <td></td>
                      <td>Service Call</td>
                      <td class="text-right">${{ number_format($order->service_call, 2) }}</td>
                      <td class="text-right">${{ number_format($order->service_call, 2) }}</td>
                    </tr>
                  @endif
                  </tbody>
                  <tfoot class="border-0">
                  <tr class="border-0">
                    <td colspan="3" class="font-16">
                      Sales Order No: {{ $order->full_number }}
                    </td>
                    <td colspan="2" class="p-0">
                      <table class="table border-0 mb-0">
                        <tr>
                          <td class="text-right font-weight-bold w-50">Sub Total</td>
                          <td class="font-weight-bold text-right">${{ number_format($order->total, 2) }}</td>
                        </tr>

                        <tr>
                          <td class="text-right font-weight-bold">Tax</td>
                          <td class="font-weight-bold text-right">${{ number_format($order->tax_amount, 2) }}</td>
                        </tr>
                        <tr>
                          <td class="text-right font-weight-bold">Shipping</td>
                          <td class="font-weight-bold text-right">${{ number_format($order->shipping_cost, 2) }}</td>
                        </tr>
                        <tr>
                          <td class="text-right font-weight-bold font-18 bg-secondary-lighten text-uppercase text-dark">
                            Invoice Total
                          </td>
                          <td class="font-18 font-weight-bold text-right">
                            ${{ number_format($order->total_amount, 2) }}</td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  </tfoot>
                </table>
                <p class="text-right text-uppercase font-weight-bold">A $25.00 fee will be assessed on all returned
                  checks.</p>
              </div> <!-- end col -->
            </div>
            <!-- end row -->
            <hr>
            <div class="row">
              <div class="col-md-12">
                <h4>Invoice Note</h4>
                <div>
                  {{ $order->invoice_notes  }}
                </div>
              </div>
            </div>
          </div> <!-- end card-body-->
        </div> <!-- end card -->
      </div> <!-- end col-->
    </div>
    <!-- end row -->
  </div>


  <div id="invoiceNoteModal" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-wrong h1"></i>
            <h4 class="mt-2">Edit Invoice Note</h4>
            <p class="mt-3">Edit the invoice note here below:</p>
            <form action="{{ route('orderModule.orders.updateInvoice', $order) }}" method="post"
                  id="update-invoice-note">
              @csrf
              @method('put')
              <div class="form-group">
                <textarea
                  class="form-control"
                  name="invoice_notes">{{ old('invoice-note', $order->invoice_note) }}</textarea>
              </div>
              <button type="button" class="btn btn-light my-2 mr-1" data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-success my-2">Save Changes!</button>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>

  @include('orders::orders.partials.modals.send-invoice-modal')

@endsection
