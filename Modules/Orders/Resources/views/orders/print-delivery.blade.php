<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ config('app.name') }} Delivery Receipt for Order # {{ $order->full_number }}</title>
  @include('shared.print.default-print-styles')
  <style>
    body, .report-container {
      font-family: 'Helvetica', serif;
      font-size: 17px;
      line-height: 1.2;
    }
    .print-page-title {
      font-size: 25px;
    }
  </style>
</head>
<body>

<div class="mb-sm">
  @php
    $path = public_path('/images/logo.png');
    $type = pathinfo($path, PATHINFO_EXTENSION);
    $data = file_get_contents($path);

    $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
  @endphp

  <div style="width: 49%; float: left;">
    <img style="display: block;" src="{{ $base64 }}" alt="{{ config('app.name') }} logo">
    <span class="d-block" style="display: block; padding-left: 70px; margin-top: -10px;">
      200 W. Haven Ave. <br>Salt Lake City, UT 84115 <br>Phone (*************
    </span>
  </div>
  <div class="text-center" style="width: 49%; float: right;">
    <h3 style="margin: 0 0 8px; color: #888; font-size: 18px;">Delivery Receipt</h3>

    <table class="table text-center">
      <tr class="bg-gray text-uppercase">
        <th class="border-bottom-0 border-right-0">Order No.</th>
        <th class="border-bottom-0 border-right-0">Order Date</th>
        <th class="border-bottom-0">Account No.</th>
      </tr>
      <tr class="text-dark">
        <td class="border-right-0">{{ $order->number }}</td>
        <td class="border-right-0">{{ $order->formatted_date }}</td>
        <td>{{ $order->customer->account_number }}</td>
      </tr>
    </table>
  </div>
  <div style="clear: both;"></div>
</div>

<div class="mb-sm" style="overflow: auto;">
  <div style="width: 49%; float: left;">
    <table class="table">
      <tr class="bg-gray">
        <th class="text-left border-bottom-0">Sold To:</th>
      </tr>
      <tr>
        <td>
          <div>{{ optional($order->customer->accountAddress)->name }}</div>
          <div style="white-space: pre-wrap;">{!! optional($order->customer->accountAddress)->full_address !!}</div>
        </td>
      </tr>
    </table>
  </div> <!-- end col-->
  <div style="width: 49%; float: right">
    <table class="table">
      <tr class="bg-gray">
        <th class="text-left border-bottom-0">Ship To:</th>
      </tr>
      <tr>
        <td>
          <div>{{ $order->shipping_name }}</div>
          <div style="white-space: pre-wrap;">{!! $order->shipping_address !!}</div>
        </td>
      </tr>
    </table>
  </div> <!-- end col-->
  <div style="clear: both;"></div>
</div>
<!-- end row -->
<div class="mb-sm">
  <table class="table table-bordered text-dark text-center">
    <tr class="bg-gray">
      <th class="border-bottom-0 border-right-0">PO Number</th>
      <th class="border-bottom-0 border-right-0">Order Date</th>
      <th class="border-bottom-0 border-right-0">Date Shipped</th>
      <th class="border-bottom-0 border-right-0">Ship Via</th>
      <th class="border-bottom-0 border-right-0">Salesperson</th>
      <th class="border-bottom-0">Payment Terms</th>
    </tr>
    <tr>
      <td class="border-right-0">{{ $order->purchase_order_number }}</td>
      <td class="border-right-0">{{ $order->formatted_date }}</td>
      <td class="border-right-0">{{ $order->formatted_shipping_date }}</td>
      <td class="border-right-0">{{ $order->shipping_via }}</td>
      <td class="border-right-0">{{ $order->salesperson }}</td>
      <td>{{ $order->payment_terms }}</td>
    </tr>
  </table>
</div>
<div class="mb-sm">
  <div class="col-sm-12">
    <table class="table table-bordered text-dark mb-1">
      <thead>
      <tr class="bg-gray text-uppercase">
        <th class="text-right p-1 border-bottom-0 border-right-0">Ordered</th>
        <th class="text-right border-bottom-0 border-right-0">Shipped</th>
        <th class="text-right border-bottom-0 border-right-0">B/O</th>
        <th class="border-bottom-0 border-right-0">Part No.</th>
        <th class="border-bottom-0 {{ (bool) $price ? 'border-right-0' : '' }}">Description</th>
        @if((bool) $price)
          <th class="p-1 border-bottom-0 border-right-0" style="text-align: right">Price</th>
          <th class="p-1 border-bottom-0" style="text-align: right">Total</th>
        @endif
      </tr>
      </thead>
      <tbody>
      @foreach($order->items as $item)
        <tr>
          <td class="text-right border-right-0 {{ ! $loop->last ? 'border-bottom-0' : '' }}">{{ $item->ordered_quantity }}</td>
          <td class="text-right border-right-0 {{ ! $loop->last ? 'border-bottom-0' : '' }}">{{ $item->real_quantity }}</td>
          <td class="text-right border-right-0 {{ ! $loop->last ? 'border-bottom-0' : '' }}">{{ $item->back_order_quantity }}</td>
          <td class="text-right border-right-0 {{ ! $loop->last ? 'border-bottom-0' : '' }}">{{ $item->code }}</td>
          <td class="{{ ! $loop->last ? 'border-bottom-0' : '' }} {{ (bool) $price ? 'border-right-0' : '' }}"
            style="{{ str_multiline($item->description) ? 'white-space: pre-wrap; ' : 'word-break: break-all; ' }}">{{ $item->description }}</td>
          @if((bool) $price)
            <td class="border-right-0 {{ ! $loop->last ? 'border-bottom-0' : '' }}" style="text-align: right">{{ number_format($item->price, 2)  }}</td>
            <td class="{{ ! $loop->last ? 'border-bottom-0' : '' }}" style="text-align: right">${{ number_format($item->real_quantity * $item->price, 2) }}</td>
          @endif
        </tr>
      @endforeach

      @if($order->isService())
        <tr>
          <td class="border-bottom-0 border-right-0" style="text-align: right">{{ $order->service_hours }}</td>
          <td class="border-bottom-0 border-right-0"></td>
          <td class="border-bottom-0 border-right-0"></td>
          <td class="border-bottom-0 border-right-0"></td>
          <td class="border-bottom-0 {{ (bool) $price ? 'border-right-0' : '' }}">Labor</td>
          @if((bool) $price)
            <td class="border-bottom-0 border-right-0" style="text-align: right">${{ number_format($order->service_rate, 2) }}</td>
            <td class="border-bottom-0" style="text-align: right">${{ number_format($order->service_rate * $order->service_hours, 2) }}</td>
          @endif
        </tr>
        <tr>
          <td class="border-bottom-0 border-right-0" style="text-align: right">1</td>
          <td class="border-bottom-0 border-right-0"></td>
          <td class="border-bottom-0 border-right-0"></td>
          <td class="border-bottom-0 border-right-0"></td>
          <td class="border-bottom-0 {{ (bool) $price ? 'border-right-0' : '' }}">Service Call</td>
          @if((bool) $price)
            <td class="border-bottom-0 border-right-0" style="text-align: right">${{ number_format($order->service_call, 2) }}</td>
            <td class="border-bottom-0" style="text-align: right">${{ number_format($order->service_call, 2) }}</td>
          @endif
        </tr>
      @endif
      </tbody>
      @if((bool) $price) 
      <tfoot class="border-0">
      <tr class="border-0">
        <td colspan="5" class="border-top-0 border-right-0"></td>
        <td class="border-0" colspan="2" style="padding: 0">
          <table class="table" style="border: none;">
            <tr>
              <td class="text-right font-weight-bold border-top-0 border-bottom-0 border-right-0">Sub Total</td>
              <td class="font-weight-bold text-right border-top-0 border-bottom-0">
                ${{ number_format($order->total, 2) }}</td>
            </tr>
            <tr>
              <td class="text-right font-weight-bold border-bottom-0 border-right-0">Tax</td>
              <td class="font-weight-bold text-right border-bottom-0">${{ number_format($order->tax_amount, 2) }}</td>
            </tr>
            <tr>
              <td class="text-right font-weight-bold border-bottom-0 border-right-0">Shipping</td>
              <td class="font-weight-bold text-right border-bottom-0">
                ${{ number_format($order->shipping_cost, 2) }}</td>
            </tr>
            <tr>
              <td class="text-right font-weight-bold font-18 bg-gray border-bottom-0 border-right-0">Delivery Total
              </td>
              <td class="font-18 font-weight-bold text-right">
                ${{ number_format($order->total_amount, 2) }}</td>
            </tr>
          </table>
        </td>
      </tr>
      </tfoot>
      @endif
    </table>
  </div> <!-- end col -->
</div>
<!-- end row -->
<div class="mb-sm">
  <div class="col-sm-12">
    <p class="mb-1" style="margin-bottom: 10px">{{ $order->customer->name }} {{ $order->customer->fax }}</p>
    <table class="table table-bordered text-dark">
      <tr class="bg-gray font-weight-bold">
        <th class="border-bottom-0 text-left">Received in Good Condition By:</th>
      </tr>
      <tr>
        <td>
          <div style="padding-top: 60px;">
            <div style="border-bottom: 1px solid #999;">
              <span style="display: inline-block; width: 49%; float: left;">x</span>
              <span class="text-right" style="display: inline-block; width: 49%; float: right;">{{ $order->formatted_delivery_date }}</span>
              <div style="clear: both;"></div>
            </div>
          </div>
          <div class="d-flex justify-content-between">
            <span style="display: inline-block; width: 49%; float: left;">{{ $order->shipping_pickup_by  }}</span>
            <span class="text-right" style="display: inline-block; width: 49%; float: right;">Date</span>
            <div style="clear: both; margin-bottom: 20px"></div>
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>
<!-- container -->
</body>
</html>
