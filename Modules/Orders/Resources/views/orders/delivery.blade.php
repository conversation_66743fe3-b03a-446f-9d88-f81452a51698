@extends('layouts.master')

@section('title', 'Delivery Receipt for Order #' . $order->full_number)

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('orderModule.orders.index') }}">Orders</a></li>
              <li class="breadcrumb-item"><a
                  href="{{ route('orderModule.orders.show', $order) }}">Order {{ $order->full_number }}</a></li>
              <li class="breadcrumb-item active">Delivery Receipt</li>
            </ol>
          </div>
          <h4 class="page-title"><a
              href="{{ route('users.customers.show', $order->customer) }}">{{ $order->customer->name }}'s</a> <span
              class="text-muted"> | </span> Delivery Receipt for Order {{ $order->number }}</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row text-dark">
      @include('orders::orders.partials.requirements-notice')
      <div class="col-sm-12">
        <div class="d-print-none mb-4">

          <div class="text-right">
            <div class="mb-1">
              <div class="btn-group mr-1">

                <button type="button"
                        class="btn btn-outline-secondary dropdown-toggle"
                        data-toggle="dropdown"
                        aria-haspopup="true"
                        aria-expanded="false">
                  Printable Receipt
                </button>
                <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end"
                     style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(285px, 37px, 0px);">
                  @if($order->customer->is_cash_customer)
                    @if ($pricingCompleted)
                      <a class="dropdown-item confirm-payment-terms-btn"
                         href="#"
                         data-toggle="modal"
                         data-target="#confirmPaymentMethodModal"
                         data-url="{{ route('orderModule.orders.downloadDeliveryReceipt', ['order' => $order, 'price' => true]) }}">
                        <i class="mdi mdi-printer"></i>
                        Print with Price
                      </a>
                    @endif
                    <a class="dropdown-item confirm-payment-terms-btn"
                       href="#"
                       data-toggle="modal"
                       data-target="#confirmPaymentMethodModal"
                       data-url="{{ route('orderModule.orders.downloadDeliveryReceipt', ['order' => $order, 'price' => false]) }}">
                      <i class="mdi mdi-printer"></i>
                      Print without Price
                    </a>
                  @else
                    @if ($pricingCompleted)
                      <a class="dropdown-item"
                         href="{{ route('orderModule.orders.downloadDeliveryReceipt', ['order' => $order, 'price' => true]) }}">
                        <i class="mdi mdi-printer"></i>
                        Print with Price
                      </a>
                    @endif
                    <a class="dropdown-item"
                       href="{{ route('orderModule.orders.downloadDeliveryReceipt', ['order' => $order, 'price' => false]) }}">
                      <i class="mdi mdi-printer"></i>
                      Print without Price
                    </a>
                  @endif
                </div>
              </div>
              <a
                href="#"
                data-target="#emailDeliveryModal"
                data-toggle="modal"
                class="btn btn-primary">
                <i class="mdi mdi-send"></i>
                Email to Customer
              </a>
            </div>
          </div>
        </div>
        <div class="card print-container">
          <div class="card-body">
            <div class="row mb-2">
              <div class="col-md-6">
                <img class="mb-0" src="{{ asset('images/logo.png') }}" alt="{{ config('app.name') }} logo">
                <span class="d-block pl-5" style="margin-top: -10px">
                                    200 W. Haven Ave. <br>Salt Lake City, UT 84115 <br>Phone (*************
                                </span>
              </div>
              <div class="col-md-6 text-center">
                <h3 class="text-muted">Delivery Receipt</h3>

                <table class="table table-bordered">
                  <tr class="text-uppercase text-dark bg-secondary-lighten">
                    <th class="p-1">Order No.</th>
                    <th class="p-1">Order Date</th>
                    <th class="p-1">Account No.</th>
                  </tr>
                  <tr class="text-dark">
                    <td>{{ $order->full_number }}</td>
                    <td>{{ $order->formatted_date }}</td>
                    <td>{{ $order->customer->account_number }}</td>
                  </tr>
                </table>
              </div>
            </div>

            <div class="row mb-2">
              <div class="col-sm-6">
                <table class="table table-bordered text-dark">
                  <tr class="text-uppercase bg-secondary-lighten">
                    <th class="p-1">Sold To:</th>
                  </tr>
                  <tr>
                    <td>
                      <div>{{ optional($order->customer->accountAddress)->name }}</div>
                      <div style="white-space: pre-wrap;">{!! optional($order->customer->accountAddress)->full_address !!}</div>
                    </td>
                  </tr>
                </table>
              </div> <!-- end col-->

              <div class="col-sm-6">
                <table class="table table-bordered text-dark">
                  <tr class="text-uppercase bg-secondary-lighten">
                    <th class="p-1">Ship To:</th>
                  </tr>
                  <tr>
                    <td>
                      <div>{{ $order->shipping_name }}</div>
                      <div style="white-space: pre-wrap;">{{ $order->shipping_address }}</div>
                    </td>
                  </tr>
                </table>
              </div> <!-- end col-->
            </div>
            <!-- end row -->
            <div class="row mb-3">
              <div class="col-sm-12">
                <table class="table table-bordered text-dark text-center">
                  <tr class="text-uppercase  bg-secondary-lighten">
                    <th class="p-1">PO Number</th>
                    <th class="p-1">Order Date</th>
                    <th class="p-1">Date Shipped</th>
                    <th class="p-1">Ship Via</th>
                    <th class="p-1">Salesperson</th>
                    <th class="p-1">Payment Terms</th>
                  </tr>
                  <tr>
                    <td>{{ $order->purchase_order_number }}</td>
                    <td>{{ $order->formatted_date }}</td>
                    <td>{{ $order->formatted_shipping_date }}</td>
                    <td>{{ $order->shipping_via }}</td>
                    <td>{{ $order->salesperson }}</td>
                    <td>{{ $order->payment_terms }}</td>
                  </tr>
                </table>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-12">
                <table class="table table-bordered text-dark mb-1">
                  <thead>
                  <tr class="bg-secondary-lighten text-uppercase">
                    <th class="text-right p-1">Ordered</th>
                    <th class="p-1">Shipped</th>
                    <th class="p-1">B/O</th>
                    <th class="p-1">Part No.</th>
                    <th class="p-1">Description</th>
                    <th class="p-1 text-right">Price</th>
                    <th class="p-1 text-right">Total</th>
                  </tr>
                  </thead>
                  <tbody>
                  @foreach($order->items as $item)
                    <tr>
                      <td class="text-right">{{ $item->ordered_quantity }}</td>
                      <td class="text-right">{{ $item->real_quantity }}</td>
                      <td class="text-right">{{ $item->back_order_quantity }}</td>
                      <td>{{ $item->code }}</td>
                      <td>{!! nl2br($item->description) !!}</td>
                      <td class="text-right">{{ number_format($item->price, 2) }}</td>
                      <td class="text-right">${{ number_format($item->real_quantity * $item->price, 2) }}</td>
                    </tr>
                  @endforeach
                  @if($order->isService())
                    <tr>
                      <td class="text-right">{{ $order->service_hours }}</td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td>Labor</td>
                      <td class="text-right">${{ number_format($order->service_rate, 2) }}</td>
                      <td class="text-right">${{ number_format($order->service_rate * $order->service_hours, 2) }}</td>
                    </tr>
                    <tr>
                      <td class="text-right">1</td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td>Service Call</td>
                      <td class="text-right">${{ number_format($order->service_call, 2) }}</td>
                      <td class="text-right">${{ number_format($order->service_call, 2) }}</td>
                    </tr>
                  @endif
                  </tbody>
                  <tfoot class="border-0">
                  <tr class="border-0">
                    <td colspan="5" class="font-16"></td>
                    <td colspan="2" class="p-0">
                      <table class="table border-0 mb-0">
                        <tr>
                          <td class="text-right font-weight-bold">Sub Total</td>
                          <td class="font-weight-bold text-right">${{ number_format($order->total, 2) }}</td>
                        </tr>

                        <tr>
                          <td class="text-right font-weight-bold">Tax</td>
                          <td class="font-weight-bold text-right">${{ number_format($order->tax_amount, 2) }}</td>
                        </tr>
                        <tr>
                          <td class="text-right font-weight-bold">Shipping</td>
                          <td class="font-weight-bold text-right">${{ number_format($order->shipping_cost, 2) }}</td>
                        </tr>
                        <tr>
                          <td class="text-right font-weight-bold font-18 bg-secondary-lighten text-uppercase text-dark">
                            Delivery <br> Total
                          </td>
                          <td class="font-18 font-weight-bold text-right">
                            ${{ number_format($order->total_amount, 2) }}</td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  </tfoot>
                </table>
              </div> <!-- end col -->
            </div>
            <!-- end row -->
            <div class="row">
              <div class="col-sm-12">
                <p class="mb-1">{{ $order->customer->name }} {{ $order->customer->fax }}</p>
                <table class="table table-bordered text-dark">
                  <tr class="bg-secondary-lighten font-weight-bold">
                    <th class="p-1">Received in Good Condition By:</th>
                  </tr>
                  <tr>
                    <td>
                      <div class="pt-5">
                        <div class="d-flex justify-content-between border-bottom">
                          <span>x</span>
                          <span>{{ $order->formatted_delivery_date }}</span>
                        </div>
                      </div>
                      <div class="d-flex justify-content-between">
                        <span>{{ $order->shipping_pickup_by  }}</span>
                        <span>Date</span>
                      </div>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div> <!-- end card-body-->
        </div> <!-- end card -->
      </div> <!-- end col-->
    </div>
    <!-- end row -->
  </div>

  <div
    id="confirmPaymentMethodModal"
    class="modal fade"
    tabindex="-1"
    role="dialog"
    style="display: none;"
    aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-body p-4">
          <div class="d-flex justify-content-center align-items-center">
            <i class="mdi mdi-alert h1 mr-2"></i>
            <h4 class="mt-2">Confirm Action!</h4>
          </div>
          <div class="mt-3">
            <p class="text-muted">The payment terms for <strong>{{ $order->customer->name }}</strong> is <strong>{{ $order->customer->payment_terms }}</strong>. Have you obtained authorization to allow the customer to sign for this order?</p>

              <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-light my-2 mr-2 px-3" data-dismiss="modal"><i class="mdi mdi-cancel ml-2"></i> No</button>
                <a href="#" class="btn btn-primary my-2 px-3 print-confirmed-delivery-btn"><i class="mdi mdi-check ml-2"></i> Yes</a>
              </div>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>


  @include('orders::orders.partials.modals.send-delivery-modal')
  <!-- container -->
@endsection

@push('js')
  <script>
    (function () {
      $(document).on('click', '.confirm-payment-terms-btn', function () {
        $('.print-confirmed-delivery-btn').attr('href', $(this).data('url'));
      });
    }());
  </script>
@endpush
