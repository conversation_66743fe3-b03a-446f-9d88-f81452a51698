<div id="editItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content ">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-document-edit h1 d-inline-block"></i>
          <h4 class="mt-2 d-inline-block">Update Item Details!</h4>
        </div>
        <form action="#" method="post" id="edit-item-form">
          @csrf
          @method('put')
          <div class="form-group row">
            <div class="col-md-6">
              <label for="item-code">Part #</label>
              <input type="text" class="form-control" name="code" id="item-code" value="">
            </div>
            <div class="col-md-6">
              <label for="item-number">Code</label>
              <input type="text" class="form-control" name="number" id="item-number" value="">
            </div>
          </div>
          <div class="form-group row">
            <div class="col-md-4">
              <label for="item-quantity">Quantity</label>
              <input type="number" class="form-control" name="quantity" id="item-quantity" value="1">
            </div>
            <div class="col-md-4">
              <label for="item-price">Price</label>
              <input type="number" class="form-control" name="price" id="item-price" step="0.01" min="0">
            </div>
            <div class="col-md-4">
              <label for="item-cost">Cost</label>
              <input type="number" class="form-control" name="cost" id="item-cost" step="0.01" min="0">
            </div>
          </div>
          <div class="form-group">
            <label for="item-description">Description</label>
            <input class="form-control" type="text" name="description" id="item-description">
          </div>
          <div class="form-group">
            <label for="item-build-instructions">Notes</label>
            <textarea class="form-control"
                      name="build_instructions"
                      rows="7"
                      id="item-build-instructions"></textarea>
          </div>
          <div class="d-flex justify-content-between">
            <button type="button"
                    class="btn btn-outline-secondary my-2"
                    data-dismiss="modal">
              Cancel
            </button>
            <button type="submit"
                    class="btn btn-primary my-2">
              Save Changes
            </button>
          </div>
        </form>

      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>

@push('js')
  <script>
    $('.edit-item-btn').on('click', function (e) {
      let form = $('#edit-item-form');
      let btnParent = $(this).parents('tr').first();
      form.attr('action', $(this).data('url'));
      form.find('#item-code').val(btnParent.find('.item-code').text());
      form.find('#item-number').val(btnParent.find('.item-number').text());
      form.find('#item-quantity').val(btnParent.find('.item-quantity-input').val());
      form.find('#item-price').val(btnParent.find('.price-override-input').val());
      form.find('#item-cost').val(btnParent.find('.cost-override-input').val());
      form.find('#item-description').val(btnParent.find('.item-description').text());
      form.find('#item-build-instructions').val(btnParent.find('.item-build-instructions').text());
    })
  </script>
@endpush
