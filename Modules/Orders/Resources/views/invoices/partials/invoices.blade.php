<div class="table-responsive">
  <table class="table table-centered w-100 dt-responsive nowrap datatable search-autofocus">
    <thead class="thead-light">
    <tr>
      <th class="text-right">Acct#</th>
      <th>Customer</th>
      <th>PO No</th>
      <th>Descrip.</th>
      <th>Ref#</th>
      <th class="text-right">Balance <br>Due</th>
      <th>Date</th>
      <th class="text-right">Debit</th>
      <th class="text-right">Credit</th>
      <th>Payment <br>Method</th>
      <th>Payment <br>Ref#</th>
      <th>Payment <br>Date</th>
      <th class="text-right">Amount Paid <br>to Date</th>
      <th class="text-right">Min Age <br> (Days)</th>
      <th>Terms</th>
      <th>Notes</th>
      <th>Status</th>
    </tr>
    </thead>
    <tbody>
    @foreach ($receivables as $receivable)
      <tr>
        <td class="text-right">{{ $receivable->customer_account_number }}</td>
        <td><a href="{{ route('users.customers.show', $receivable->customer_id) }}">{{ $receivable->customer_name }}</a>
        </td>
        <td class="text-right">{{ $receivable->purchase_order_number }}</td>
        <td>{{ $receivable->type }}</td>
        <td class="text-right">
          @if (strtolower($receivable->type) === 'invoice')
            <a href="{{ route('orderModule.orders.showInvoice', $receivable->record_id) }}">
              {{ $receivable->reference_number }}
            </a>
          @else
            <a href="{{ route('users.customers.payments.edit', [
              'customer' => $receivable->customer_id,
              'payment' => $receivable->record_id
            ]) }}">
              {{ $receivable->reference_number }}
            </a>
          @endif
        </td>
        <td class="text-right">
          {{ $receivable->balance_due }}
        </td>
        <td>
          {{ $receivable->date }}
        </td>
        <td class="text-right">{{ $receivable->debit }}</td>
        <td class="text-right">{{ $receivable->credit }}</td>
        <td>{{ $receivable->payment_method }}</td>
        <td>{{ $receivable->reference }}</td>
        <td class="text-right">
          {{ $receivable->date }}
          <div class="btn-group">
            <button type="button"
                    class="btn-sm btn-link dropdown-toggle border-0"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false">
            </button>

            <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end"
                 style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(285px, 37px, 0px);">
              <div class=" dropdown-header noti-title">
                <h6 class="text-overflow m-0">Edit Log</h6>
              </div>
              @foreach($receivable->date_log as $logStatement)
                <div class="dropdown-item">
                  {!! $logStatement !!}
                </div>
              @endforeach
            </div>
          </div>
        </td>
        <td class="text-right">{{ $receivable->amount_paid }}</td>
        <td class="text-right">{{ $receivable->age }}</td>
        <td>{{ $receivable->payment_terms }}</td>
        <td style="min-width: 80px">
          @if (strtolower($receivable->type) === 'invoice')
            <a class="btn btn-sm text-info edit-invoice-note-btn" href="#"
               data-toggle="modal"
               data-target="#invoiceNoteModal"
               data-url="{{ route('orderModule.orders.updateInvoice', $receivable->record_id) }}">
              <i class="mdi mdi-pencil"></i>
            </a>
          @else
            <a class="btn btn-sm text-info edit-invoice-note-btn" href="#"
               data-toggle="modal"
               data-target="#invoiceNoteModal"
               data-url="{{ route('users.customers.payments.update', [
                            'customer' => $receivable->customer_id,
                            'payment' => $receivable->record_id]) }}">
              <i class="mdi mdi-pencil"></i>
            </a>
          @endif
          <span class="invoice-note-content">{{ $receivable->notes }}</span>
        </td>
        <td>{{ $receivable->status }}</td>
      </tr>

    @endforeach
    </tbody>
    <tfoot>
    <tr class="bg-secondary-lighten text-dark font-weight-bold font-16">
      <td colspan="5">Totals</td>
      <td class="text-right"></td>
      <td></td>
      <td class="text-right"></td>
      <td class="text-right"></td>
      <td colspan="8"></td>
    </tr>
    </tfoot>
  </table>
</div>

<div id="invoiceNoteModal" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-wrong h1"></i>
          <h4 class="mt-2">Edit Note</h4>
          <p class="mt-3">Edit the note here below:</p>
          <form action="" method="post" id="update-note-form">
            @csrf
            @method('put')
            <div class="form-group">
              <textarea class="form-control" name="notes" id="invoice-note-element" rows="6"></textarea>
            </div>
            <button type="button" class="btn btn-light my-2 mr-1" data-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-success my-2">Save Changes!</button>
          </form>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>

@push('js')
  <script>
    let updateInvoiceNoteForm = $('#update-note-form');
    let invoiceNoteTextArea = $('#invoice-note-element');
    let invoiceNoteRowElement;

    $('body').on('click', '.edit-invoice-note-btn', function () {
      updateInvoiceNoteForm.attr('action', $(this).data('url'));
      invoiceNoteRowElement = $(this).next();
      invoiceNoteTextArea.val(invoiceNoteRowElement.text());
      invoiceNoteTextArea.removeClass('border', 'border-danger');
    });

    updateInvoiceNoteForm.on('submit', function () {
      let form = $(this);

      axios.put(form.attr('action'), {
        invoice_notes: invoiceNoteTextArea.val()
      }).then(function (response) {
        invoiceNoteRowElement.text(invoiceNoteTextArea.val());
        $('#invoiceNoteModal').modal('hide');
        invoiceNoteTextArea.removeClass('border', 'border-danger');
      }).catch(function (error) {
        invoiceNoteTextArea.addClass('border', 'border-danger');
      });

      return false;
    });
  </script>
@endpush
