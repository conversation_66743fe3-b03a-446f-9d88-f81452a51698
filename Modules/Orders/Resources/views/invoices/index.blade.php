@extends('layouts.master')

@section('title', 'Invoices')

@prepend('styles')
  @livewireStyles
  <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <!-- third party css end -->
  <style>
    .lds-ellipsis {
      display: block;
      position: relative;
      width: 80px;
      height: 60px;
      margin: 0 auto;
    }
    .lds-ellipsis div {
      position: absolute;
      top: 20px;
      width: 13px;
      height: 13px;
      border-radius: 50%;
      background: #003E58;
      animation-timing-function: cubic-bezier(0, 1, 1, 0);
    }
    .lds-ellipsis div:nth-child(1) {
      left: 8px;
      animation: lds-ellipsis1 0.6s infinite;
    }
    .lds-ellipsis div:nth-child(2) {
      left: 8px;
      animation: lds-ellipsis2 0.6s infinite;
    }
    .lds-ellipsis div:nth-child(3) {
      left: 32px;
      animation: lds-ellipsis2 0.6s infinite;
    }
    .lds-ellipsis div:nth-child(4) {
      left: 56px;
      animation: lds-ellipsis3 0.6s infinite;
    }
    @keyframes lds-ellipsis1 {
      0% {
        transform: scale(0);
      }
      100% {
        transform: scale(1);
      }
    }
    @keyframes lds-ellipsis3 {
      0% {
        transform: scale(1);
      }
      100% {
        transform: scale(0);
      }
    }
    @keyframes lds-ellipsis2 {
      0% {
        transform: translate(0, 0);
      }
      100% {
        transform: translate(24px, 0);
      }
    }
  </style>
@endprepend

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item active">Invoices</li>
            </ol>
          </div>
          <h4 class="page-title">All Invoices and Payments</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row">
      <div class="col-12">
        @livewire('receivables.invoice-payments')
      </div> <!-- end col -->
    </div>

    <div id="invoiceNoteModal" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-body p-4">
            <div class="text-center">
              <i class="dripicons-wrong h1"></i>
              <h4 class="mt-2">Edit Note</h4>
              <p class="mt-3">Edit the note here below:</p>
              <form action="" method="post" id="update-note-form">
                @csrf
                @method('put')
                <div class="form-group">
                  <textarea class="form-control" name="notes" id="invoice-note-element" rows="6"></textarea>
                </div>
                <button type="button" class="btn btn-light my-2 mr-1" data-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-success my-2">Save Changes!</button>
              </form>
            </div>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>
    <!-- end row -->
  </div>
  <!-- container -->
@endsection

@push('js')
  @livewireScripts

  <script>
    let updateInvoiceNoteForm = $('#update-note-form');
    let invoiceNoteTextArea = $('#invoice-note-element');
    let invoiceNoteRowElement;

    $('body').on('click', '.edit-invoice-note-btn', function () {
      updateInvoiceNoteForm.attr('action', $(this).data('url'));
      invoiceNoteRowElement = $(this).next();
      invoiceNoteTextArea.val(invoiceNoteRowElement.text());
      //invoiceNoteTextArea.removeClass('border', 'border-danger');
    });

    updateInvoiceNoteForm.on('submit', function () {
      let form = $(this);

      axios.put(form.attr('action'), {
        notes: invoiceNoteTextArea.val()
      }).then(function (response) {
        invoiceNoteRowElement.text(invoiceNoteTextArea.val());
        $('#invoiceNoteModal').modal('hide');
        //invoiceNoteTextArea.removeClass('border', 'border-danger');
      }).catch(function (error) {
        //invoiceNoteTextArea.addClass('border', 'border-danger');
      });

      return false;
    });
  </script>
@endpush
