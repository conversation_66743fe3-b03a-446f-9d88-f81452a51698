@extends('layouts.master')

@section('title', 'Invoices past 30 days')

@prepend('styles')
  <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
@endprepend

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item active">Invoices past 30 days</li>
            </ol>
          </div>
          <h4 class="page-title">Invoices past 30 days</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-centered w-100 datatable search-autofocus">
                <thead class="thead-light">
                <tr>
                  <th class="text-right">Acct#</th>
                  <th>Customer</th>
                  <th>Invoice#</th>
                  <th class="text-right">Total Amount</th>
                  <th class="text-right">Amount Paid</th>
                  <th class="text-right">Balance</th>
                  <th>Invoice Date</th>
                </tr>
                </thead>
                <tbody>
                @forelse($invoices as $invoice)
                  <tr>
                    <td>{{ $invoice->account_number }}</td>
                    <td>{{ $invoice->customer_name }}</td>
                    <td><a href="{{ route('orderModule.orders.show', $invoice->record_id) }}">{{ $invoice->reference_number }}</a></td>
                    <td class="text-right">{{ number_format($invoice->debit_amount, 2) }}</td>
                    <td class="text-right">{{ number_format($invoice->amount_paid, 2) }}</td>
                    <td class="text-right">{{ number_format($invoice->balance, 2) }}</td>
                    <td>{{ $invoice->record_date }}</td>
                  </tr>
                @empty
                  <tr>
                    <td colspan="9">No invoices orders found matching the current filter.</td>
                  </tr>
                @endforelse
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>
  <!-- container -->
@endsection

@push('js')
  <!-- third party js -->
  <script src="{{ asset('js/vendor/jquery.dataTables.min.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.bootstrap4.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.responsive.min.js') }}"></script>
  <script src="{{ asset('js/vendor/responsive.bootstrap4.min.js') }}"></script>
  <!-- third party js ends -->

  <!-- demo app -->
  <script src="{{ asset('js/pages/demo.products.js') }}"></script>
@endpush
