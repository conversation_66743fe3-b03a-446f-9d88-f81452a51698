@extends('layouts.master')

@section('title', 'Customer Quotation')

@prepend('styles')
  <!-- third party css -->
  <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <!-- third party css end -->
@endprepend

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item"><a href="{{ route('orderModule.quotations.index') }}">Quotations</a></li>
              <li class="breadcrumb-item active">Quotation # {{ $quotation->full_number }}</li>
            </ol>
          </div>
          <h4 class="page-title"><a
              href="{{ route('users.customers.show', $quotation->customer) }}">{{ $quotation->customer->name }}'s</a>
            <span class="text-muted"> | </span> Quotation #{{ $quotation->full_number }}</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->
    <div class="row mb-3">
      @include('orders::quotations.partials.requirements-notice')
      <div class="col-sm-12 text-right">
        @can('convert quote')
          @if(! $quotation->converted)
          <button class="btn btn-primary mr-1"
                  type="button"
                  data-target="#convertToOrder"
                  data-toggle="modal">
            <i class="mdi mdi-rotate-right"></i>
            Convert to Order
          </button>
          @endif
        @endcan
        @can('duplicate quote')
          <button class="btn btn-dark mr-1"
                  type="button"
                  data-target="#duplicateQuotation"
                  data-toggle="modal">
            <i class="mdi mdi-content-copy"></i>
            Duplicate Quotation
          </button>
        @endcan
        @canany(['print quote', 'email quote', 'delete quote'])
          <div class="btn-group">
            <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown" aria-haspopup="true"
                    aria-expanded="false">
              <i class="mdi mdi mdi-dots-vertical"></i> Actions
            </button>
            <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end"
                 style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(285px, 37px, 0px);">
              @if($pricingCompleted)
                @can('print quote')
                  <a class="dropdown-item auto-save-form"
                     target="_blank"
                     href="{{ route('orderModule.quotations.print', $quotation) }}">
                    <i class="mdi mdi-printer mr-1"></i>
                    Print Quotation
                  </a>
                @endcan
                @can('email quote')
                  <a
                    class="dropdown-item"
                    href="#"
                    data-target="#emailQuotationModal"
                    data-toggle="modal">
                    <i class="mdi mdi-eye mr-1"></i>
                    Email Quotation
                  </a>
                @endcan
              @endif
              @can('update quote')
                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#uploadAttachment">
                  <i class="mdi mdi-upload-multiple mr-1"></i>
                  Attach Files
                </a>
              @endcan
              @can('delete quote')
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#deleteQuotation">
                  <i class="mdi mdi-delete mr-1"></i>
                  Delete Quotation
                </a>
              @endcan
            </div>
          </div>
        @endcanany
      </div>
    </div>
  @include('orders::quotations.partials.quotation-details')
  <!-- end row -->
  </div>
  @include('orders::quotations.partials.modals')
  @include('orders::shared.modals.edit-item')
  <!-- container -->
@endsection

@push('js')
  <script>
    $(document).on('click', '.auto-save-form', function () {
      let url = $(this).attr('href');
      let form = $('#quotation-form');
      let formUrl = form.attr('action');
      let redirectUrl = formUrl + '?redirect_url=' + url;
      form.attr('action', redirectUrl);
      form.trigger('submit');

      return false;
    });
  </script>
@endpush
