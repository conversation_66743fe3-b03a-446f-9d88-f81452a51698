<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ config('app.name') }} Quotation for Quotation # {{ $quotation->full_number }}</title>
  @include('shared.print.default-print-styles')
</head>
<body>
<div class="mb-sm">
  @include('shared.print.pacific_logo_address')
  <div class="text-center" style="width: 49%; float: right;">
    <h3 style="margin: 0 0 8px; color: #888; text-transform: uppercase;">Quotation</h3>

    <table class="table text-center">
      <tr class="table-header">
        <th style="border-right: none; border-bottom: none;">Quotation No.</th>
        <th style="border-right: none; border-bottom: none;">Quotation Date</th>
        <th style="border-bottom: none;">Account No.</th>
      </tr>
      <tr class="text-dark">
        <td style="border-right: none;">{{ $quotation->full_number }}</td>
        <td style="border-right: none;">{{ $quotation->created_at->format('m/d/y') }}</td>
        <td>{{ $quotation->customer->account_number }}</td>
      </tr>
    </table>
  </div>
  <div style="clear: both;"></div>
</div>

<div class="mb-sm" style="overflow: auto;">
  <div style="width: 49%; float: left;">
    <table class="table">
      <tr class="text-uppercase table-header">
        <th style="border-bottom: none;">Bill To:</th>
      </tr>
      <tr>
        <td>
          <div>{{ $quotation->billing_name }}</div>
          <div style="white-space: pre-wrap;">{!! $quotation->billing_address !!}</div>
        </td>
      </tr>
    </table>
  </div> <!-- end col-->
  <div style="width: 49%; float: right">
    <table class="table">
      <tr class="text-uppercase table-header">
        <th style="border-bottom: none;">Ship To:</th>
      </tr>
      <tr>
        <td>
          <div>{{ $quotation->shipping_name }}</div>
          <div style="white-space: pre-wrap;">{!! $quotation->shipping_address !!}</div>
        </td>
      </tr>
    </table>
  </div> <!-- end col-->
  <div style="clear: both;"></div>
</div>
<!-- end row -->
<div class="mb-sm">
  <table class="table  text-dark text-center">
    <tr class="text-uppercase table-header">
      <th style="border-bottom: none; border-right: none;">Job Name</th>
      <th style="border-bottom: none; border-right: none;">Lead Time</th>
      <th style="border-bottom: none; border-right: none;">Expiration Date</th>
      <th style="border-bottom: none; border-right: none;">Salesperson</th>
      <th style="border-bottom: none;">Payment Terms</th>
    </tr>
    <tr>
      <td style="border-right: none;">{{ $quotation->purchase_order_number }}</td>
      <td style="border-right: none;">{{ $quotation->lead_time }}</td>
      <td style="border-right: none;">{{ optional($quotation->expires_at)->format('m/d/y') }}</td>
      <td style="border-right: none;">{{ $quotation->salesperson }}</td>
      <td>{{ $quotation->payment_terms }}</td>
    </tr>
  </table>
</div>

<table class="table mb-sm" style="border: none;">
  <thead>
    <tr class="table-header">
      <th class="text-right p-1" style="border-right: none;">Qty</th>
      <th class="p-1" style="border-right: none;">Part No.</th>
      <th class="p-1" style="border-right: none;">Description</th>
      <th class="text-right p-1" style="border-right: none;">Price</th>
      <th class="text-right p-1" style="">Total</th>
    </tr>
  </thead>
  <tbody>
  @foreach($quotation->items as $item)
    <tr>
      <td class="text-right" style="border-right: none;border-top: none;">{{ $item->quantity }}</td>
      <td style="border-right: none;border-top: none;">{{ $item->code }}</td>
      <td style="border-right: none;border-top: none;">{!! nl2br($item->description) !!}</td>
      <td class="text-right" style="border-right: none;border-top: none;">${{ number_format($item->price, 2) }}</td>
      <td class="text-right" style="border-top: none;">${{ number_format($item->amount, 2) }}</td>
    </tr>
  @endforeach
  @if ($quotation->is_service)
    <tr>
      <td class="text-right" style="border-top: none; border-right: none;">{{ $quotation->service_hours ?? 0 }}</td>
      <td style="border-top: none; border-right: none;"></td>
      <td style="border-top: none; border-right: none;">Labor</td>
      <td class="text-right" style="border-top: none; border-right: none;">${{ number_format($quotation->service_rate, 2)  }}</td>
      <td class="text-right" style="border-top: none;">${{ number_format(money_multiply($quotation->service_rate ?? 0, $quotation->service_hours ?? 0), 2)  }}</td>
    </tr>
    <tr>
      <td class="text-right" style="border-right: none; border-top: none;">1</td>
      <td style="border-right: none; border-top: none;"></td>
      <td style="border-right: none; border-top: none;">Service Call</td>
      <td class="text-right" style="border-right: none; border-top: none;">${{ number_format($quotation->service_call, 2)  }}</td>
      <td class="text-right" style="border-top: none;">${{ number_format($quotation->service_call, 2)  }}</td>
    </tr>
  @endif

  </tbody>
  @if ($quotation->show_total || $quotation->add_tax)
    <tfoot class="border-0" style="border: none;">
      <tr class="border-0">
        <td colspan="3" class="font-16" style="border-right: none;"></td>
        <td colspan="2" style="padding: 0;">
          <table class="table" style="border: none;">
            @if ($quotation->show_total)
              <tr>
                <td class="text-right font-weight-bold" style="border: none; width: 50%">Sub Total</td>
                <td class="font-weight-bold text-right" style="border: none; border-left: 1px solid gray;">
                  ${{ number_format($quotation->total, 2) }}</td>
              </tr>
            @endif
            @if($quotation->add_tax)
              <tr>
                <td class="text-right font-weight-bold" style="border: none; border-top: 1px solid gray; width: 50%;">Tax</td>
                <td class="font-weight-bold text-right" style="border: none; border-left: 1px solid gray; border-top: 1px solid gray;">
                  ${{ number_format($quotation->tax, 2) }}</td>
              </tr>
              
            @endif
            @if($quotation->show_total)
              <tr>
                <td class="text-right font-weight-bold font-18 table-header"
                    style="font-weight: bold; border: none; border-right: 1px solid gray; border-top: 1px solid gray; width: 50%;">Quotation Total
                </td>
                <td class="font-18 font-weight-bold text-right"
                    style="font-weight: bold; border: none; border-top: 1px solid gray;">
                  ${{ number_format($quotation->total_amount, 2) }}</td>
              </tr>
            @endif
          </table>
        </td>
      </tr>
      @if(! $quotation->add_freight || ! $quotation->add_tax)
      <tr style="border: none;">
          <td colspan="3" style="border: none; border-bottom: 1px solid gray; border-left: 1px solid gray; border-right: 1px solid gray;">
            @if(! $quotation->add_freight)
              Quotation total does NOT include destination freight charges
            @endif
          </td>
        
        
          <td colspan="2" style="text-align: right; border: none; border-bottom: 1px solid gray; border-right: 1px solid gray;">
            @if(! $quotation->add_tax)
            Total does NOT include Sales Tax
            @endif
          </td>
        

      </tr>
      @endif
    </tfoot>
  @endif
</table>
</body>
</html>
