@extends('layouts.master')

@section('title', 'Quotations')

@prepend('styles')
  <!-- third party css -->
  <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css"/>
  <!-- third party css end -->
@endprepend

@section('content')
  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="row">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
              <li class="breadcrumb-item active">Quotations</li>
            </ol>
          </div>
          <h4 class="page-title">Quotations</h4>
        </div>
      </div>
    </div>
    <!-- end page title -->

    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            @include('shared.advanced-search')
            <div class="table-responsive">
              <table class="table table-centered table-sm w-100 dt-responsive nowrap datatable search-autofocus">
                <thead class="thead-light">
                <tr>
                  <th>Date</th>
                  <th class="text-right">Quotation #</th>
                  <th class="text-right">Account #</th>
                  <th>Customer</th>
                  <th>Purchase Order #</th>
                  <th class="text-right">Qty</th>
                  <th>Description</th>
                  <th>Part#</th>
                  <th class="text-right">Price</th>
                  <th class="text-right">Total</th>
                  <th>Created By</th>

                  <th>Shipping Name</th>
                  <th>Status</th>
                  <th class="text-right" style="min-width: 85px;">Action</th>
                </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>

          </div> <!-- end card-body-->
        </div> <!-- end card-->
      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>

  <div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content modal-filled bg-danger">
        <div class="modal-body p-4">
          <div class="text-center">
            <i class="dripicons-wrong h1"></i>
            <h4 class="mt-2">Confirm Action!</h4>
            <p class="mt-3">This will delete the quote with related data.</p>
            <form action="#" method="post" id="delete-item-form">
              @csrf
              @method('delete')
              <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
            </form>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>
  <!-- container -->
@endsection

@push('js')
  <!-- third party js -->
  <script src="{{ asset('js/vendor/jquery.dataTables.min.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.bootstrap4.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.responsive.min.js') }}"></script>
  <script src="{{ asset('js/vendor/responsive.bootstrap4.min.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.rowGroup.min.js') }}"></script>
  <script src="{{ asset('js/vendor/dataTables.checkboxes.min.js') }}"></script>
  <!-- third party js ends -->

  <script>
    (function () {
      let defaultRoute = "{{ route('orderModule.quotations.show', 1) }}";
      let defaultCustomerRoute = "{{ route('users.customers.show', 1) }}";
      let searchParams = @json($searchParams);
      $('.datatable').DataTable({
        pageLength: 50,
        processing: true,
        serverSide: true,
        responsive: false,
        initComplete: function () {
          if ($('.datatable').hasClass('search-autofocus')) {
            $('input[type="search"]').trigger('focus');
          }
        },
        ajax: {
          url: "{{ route('api.orderModule.quotations.index') }}",
          data: searchParams
        },
        columns: [
          {data: 'created_at', name: 'quotations.created_at'},
          {
            data: 'full_number',
            name: 'quotations.full_number',
            render: function (data, type, row) {
              let showItemRoute = defaultRoute.replace('1', row.id);

              return `<a href="${showItemRoute}">${data}</a>`;
            }
          },
          {data: 'customer_account_number', name: 'customers.account_number'},
          {
            data: 'customer_name',
            name: 'users.name',
            render: function (data, type, row) {
              let showRoute = defaultCustomerRoute.replace('1', row.customer_id);

              return `<a href="${showRoute}">${data}</a>`;
            }
          },
          {
            data: 'purchase_order_number', name: 'quotations.purchase_order_number'
          },
          {
            data: 'quantity',
            name: 'quotation_line.quantity',
            className: 'text-right'
          },
          {data: 'description', name: 'quotation_line.description'},
          {data: 'part_number', name: 'quotation_line.code'},
          {data: 'price', name: 'quotation_line.price', className: 'text-right'},
          {data: 'item_total', className: 'text-right'},
          {data: 'created_by', name: 'quotations.created_by'},

          {data: 'shipping_name', name: 'quotations.shipping_name'},
          {data: 'status', name: 'quotations.status'},
          {
            data: 'view_quotation',
            render: function (data, type, row) {
              return `<a href="${data}"><i class="mdi mdi-eye"></i> View</a>`;
            },
            className: 'text-right'
          }
        ],
        rowGroup: {
          startRender: null,
          endRender: function ( rows, group) {
            let orderTotal = rows.data().pluck('item_total').reduce(function (a, b) {
              return a + b.replace(',', '') * 1.0;
            }, 0);

            orderTotal = $.fn.dataTable.render.number(',', '.', 2, '$').display( orderTotal );

            return $('<tr/>')
              .append( `<td colspan="9"></td>` )
              .append( `<td class="text-right font-weight-bold">${orderTotal}</td>` )
              .append( `<td colspan="5"></td>` );
          },
          dataSrc: 'full_number'
        }
      })
    }());
  </script>
@endpush
