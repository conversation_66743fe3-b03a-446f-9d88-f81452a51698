@extends('layouts.master')

@section('title', 'Manage Preferences Quotation# ' . $quotation->id)

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.index') }}">Customers</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.customers.show', $quotation->customer) }}">{{ $quotation->customer->name }}</a></li>
                            <li class="breadcrumb-item active">Manage Preferences for Quotation# {{ $quotation->id }}</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Manage Preferences <a href="{{ route('orderModule.quotations.show', $quotation) }}">Quotation# {{ $quotation->id }}</a> for {{ $quotation->customer->name }}</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <form action="{{ route('users.quotations.preferences.store', $quotation) }}" method="post">
                            @csrf
                            <fieldset class="mb-4">
                                <legend class="h5 text-muted mb-3">Update Existing Preferences</legend>
                                @foreach($quotation->preferences as $preference)
                                    <div class="form-row mb-3">
                                        <label class="col-md-3 col-form-label" for="preference-{{ $preference['id'] }}"> {{ $preference['name'] }}</label>
                                        <div class="col-md-9">
                                            <input type="hidden" name="preference_id[]" value="{{ $preference['id'] }}">
                                            <input type="text"
                                                   name="preference_value[]"
                                                   class="form-control"
                                                   id="preference-{{ $preference['id'] }}"
                                                   value="{{ $preference['value'] }}">
                                        </div>
                                    </div>
                                @endforeach
                            </fieldset>

                            <fieldset>
                                <legend class="h5 text-muted mb-3">Additional Preferences</legend>
                                <div class="form-row duplicate">
                                    <div class="col-md-3">
                                        <label>Name</label>
                                        <select
                                            name="preference_id[]"
                                            class="form-control select2"
                                            data-toggle="select2">
                                            <option value="">Select Preference</option>
                                            @foreach($preferenceList as $prefId => $prefName)
                                                <option value="{{ $prefId }}">{{ $prefName }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-7">
                                        <label>Value</label>
                                        <input class="form-control" type="text" name="preference_value[]" required>
                                    </div>
{{--                                    <div class="col-md-2">--}}
{{--                                        <label class="d-block">Print on Quotation?</label>--}}
{{--                                        <input class="switch-input" type="checkbox" id="switch0" data-switch="none" value="1" name="preference_print_on_order">--}}
{{--                                        <label class="switch-label" for="switch0" data-on-label="Yes" data-off-label="No"></label>--}}
{{--                                    </div>--}}
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="d-block">&nbsp;</label>
                                            <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none"><i class="mdi mdi-close"></i> Remove </button>
                                            <button type="button" class="btn btn-link duplicate-btn" data-id="duplicate0"><i class="mdi mdi-plus"></i> Add</button>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                            <div class="form-group d-flex justify-content-end mt-4">
                                <a class="btn btn-light mr-2" href="{{ route('orderModule.quotations.show', $quotation) }}">Cancel</a>
                                <button type="submit" class="btn btn-primary"><i class="mdi mdi-plus"></i> Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('inventory::shared.forms.duplicate')
