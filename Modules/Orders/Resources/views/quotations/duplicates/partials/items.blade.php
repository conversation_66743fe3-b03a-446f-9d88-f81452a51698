<fieldset>
    <p>Fill the form below to add items to this order. You can attach multiple items by clicking the <code>+ Add</code> button.</p>
    <fieldset>
        @foreach($quotation->items as $item)
            <div class="form-row duplicate">
                <div class="col-sm-12 col-md-5">
                    <label>Item</label>
                    <select
                        name="item_id[]"
                        class="form-control">
                        <option value="{{ $item->id }}">{{ $item->code }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="product_quantity">Quantity</label>
                    <input type="number" class="form-control" name="quantity[]" value="{{ $item->pivot->quantity }}">
                </div>
                <div class="col-md-3">
                    <label>Price Override</label>
                    <input type="number" class="form-control" name="price[]" step="0.01" min="0" value="{{ $item->pivot->price }}">
                </div>


                <div class="col-md-2">
                    <div class="form-group">
                        <label class="d-block">&nbsp;</label>
                        <button type="button" class="btn btn-link remove-duplicate-btn text-danger"><i class="mdi mdi-close"></i> Remove </button>
                    </div>
                </div>
            </div>
        @endforeach
        <div class="form-row duplicate">
            <div class="col-sm-12 col-md-5">
                <label>Item</label>
                <select
                    name="item_id[]"
                    class="form-control select2"
                    data-toggle="select2">
                    <option value="">Select Item</option>
                    @foreach($itemList as $itemId => $itemName)
                        <option value="{{ $itemId }}">{{ $itemName }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label for="product_quantity">Quantity</label>
                <input type="number" class="form-control" name="quantity[]" value="1">
            </div>
            <div class="col-md-3">
                <label>Price Override</label>
                <input type="number" class="form-control" name="price[]" step="0.01" min="0">
            </div>


            <div class="col-md-2">
                <div class="form-group">
                    <label class="d-block">&nbsp;</label>
                    <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none"><i class="mdi mdi-close"></i> Remove </button>
                    <button type="button" class="btn btn-link duplicate-btn" data-id="duplicate0"><i class="mdi mdi-plus"></i> Add</button>
                </div>
            </div>
        </div>
    </fieldset>
</fieldset>
