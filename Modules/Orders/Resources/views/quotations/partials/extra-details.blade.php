<div class="form-group row mb-3">
  <div class="col-md-6">
    <label for="contact">Contact</label>
    <input type="text" class="form-control show-change" id="contact" name="contact"
           value="{{ old('contact', $quotation->contact) }}">
  </div>
  <div class="col-md-6">
    <label for="job_name">Purchase Order#</label>
    <input class="form-control show-change" id="purchase_order_number" name="purchase_order_number"
           value="{{ old('purchase_order_number', $quotation->purchase_order_number) }}">
  </div>
</div>

<div class="form-group row">
  <div class="col-md-4">
    <label for="quotation_number">Quotation #</label>
    <input class="form-control disabled" type="text" name="number" id="quotation_number"
           value="{{ $quotation->full_number ?? ($nextQuotationNumber ?? '') }}" disabled>
  </div>
  <div class="col-md-4">
    <label for="quotation_date">Quotation Date</label>
    <input class="form-control show-change" type="date" name="quotation_date" id="quotation_date"
           value="{{ old('quotation_date', optional($quotation->created_at)->toDateString() ?? now()->toDateString()) }}">
  </div>
  <div class="col-md-4">
    <label for="expiry_date">Exp. Date</label>
    <input class="form-control show-change" type="date" id="expiry_date" name="expiry_date"
           value="{{ old('expiry_date', optional($quotation->expires_at)->toDateString() ?? now()->addMonths(3)->toDateString()) }}">
  </div>
</div>
<div class="form-group row mb-3">
  <div class="col-md-4">
    <label for="salesperson">Salesperson <span class="text-danger">*</span></label>
    <select class="form-control show-change"
            name="salesperson"
            id="salesperson" data-salesperson="{{ $quotation->salesperson }}"
            required>
      <option value="">Select Salesperson</option>
      @foreach($staffList as $staffId => $staffName)
        <option
          value="{{ $staffName }}" {{ $staffName === old('salesperson', $quotation->salesperson) ? 'selected' : '' }}>{{ $staffName }}</option>
      @endforeach
    </select>
  </div>
  <div class="col-md-4">
    <label for="lead_time">Lead Time</label>
    <input type="text"
           name="lead_time"
           class="form-control show-change"
           id="lead_time"
           value="{{ old('lead_time', $quotation->lead_time) }}">
  </div>
  <div class="col-md-4">
    <label for="terms">Terms</label>
    <input type="text"
           class="form-control"
           id="terms"
           disabled
           value="{{ optional($quotation->customer)->payment_terms ?? $customer->payment_terms }}">
  </div>

</div>

<div class="form-group mb-4 row">
  <div class="col-md-6">
    <label for="shop_notes">Shop Notes</label>
    <textarea class="form-control show-change" id="shop_notes"
              name="shop_notes">{{ old('shop_notes', $quotation->shop_notes) }}</textarea>
  </div>
  <div class="col-md-6">
    <label for="private_notes">Private Notes</label>
    <textarea class="form-control show-change" id="private_notes"
              name="private_notes">{{ old('private_notes', $quotation->private_notes) }}</textarea>
  </div>
</div>

<div class="form-group mb-3 row">
  <div class="col-md-12 d-flex">
    <div class="custom-control custom-switch mr-3">
      <input type="checkbox"
             name="add_tax"
             class="custom-control-input"
             id="order-add-tax"
             value="1"
        {{ (bool) old('add_tax', $quotation->add_tax) === true ? 'checked' : '' }} {{ $quotation->force_no_tax ? 'disabled' : '' }}>
      <label class="custom-control-label"
             for="order-add-tax">Add Tax to Quote</label>
    </div>
    <div class="custom-control custom-switch mr-3">
      <input type="checkbox"
             name="no_tax"
             class="custom-control-input"
             id="no-tax-forced"
             value="1" {{ (bool) old('no_tax', $quotation->force_no_tax) === true ? 'checked' : '' }}>
      <label class="custom-control-label"
             for="no-tax-forced">Force No Tax.</label>
    </div>
    <div class="custom-control custom-switch mr-2 mb-1">
      <input type="checkbox"
             name="yes_tax"
             class="custom-control-input"
             id="tax-forced"
             value="1" {{ (bool) old('yes_tax', $quotation->force_tax) === true ? 'checked' : '' }}>
      <label class="custom-control-label"
             for="tax-forced">Force Tax</label>
    </div>
  </div>
</div>
