<table class="table table-fit mb-5">
  <thead>
  <tr>
    <th>Qty</th>
    <th>Part No.</th>
    <th>Code</th>
    <th>Location</th>
    <th>Description</th>
    <th>Notes</th>
    <th class="text-right">Price</th>
    <th class="text-right">Total</th>
    <th class="text-right">&nbsp;</th>
  </tr>
  </thead>
  <tbody id="items-table-body">
  @foreach($quotation->items as $item)
    <tr id="item-{{ $item->id }}">
      <td>
        <input class="item-quantity-input text-center" type="number" name="quantity[]"
               value="{{ $item->pivot->quantity  }}" style="width: 50px">
      </td>
      <td>
        {{ $item->code }}
        <input type="hidden" name="item_id[]" value="{{ $item->id }}">
      </td>
      <td>{{ $item->number }}</td>
      <td>{{ optional($item->location)->name }}</td>
      <td>{{ $item->description }}</td>
      <td style="white-space: pre-wrap;">{{ $item->build_description }}</td>
      <td class="text-right">
        <input class="price-override-input text-center" type="text" name="price[]" value="{{ $item->cost }}"
               step="0.01" style="width: 80px">
      </td>
      <td class="text-right item-total">{{ $item->cost * $item->pivot->quantity }}</td>
      <td class="text-right"><a href="#" class="delete-item-btn text-danger"><i class="mdi mdi-delete"></i></a></td>
    </tr>
  @endforeach
  </tbody>
</table>


<fieldset>
  <legend class="h4 text-muted">Add Items</legend>
  <fieldset class="mb-4">
    <div class="form-row" id="item-field-wrapper">
      <div class="col-md-2">
        <label for="add-item-qty-input">Quantity</label>
        <input type="number" class="form-control quantity-input" id="add-item-qty-input" value="1">
      </div>
      <div class="col-sm-12 col-md-6">
        <label>Item</label>
        <input class="form-control data-list-input" id="add-item-code-input" list="itemList">
      </div>


      <div class="col-md-2">
        <div class="form-group">
          <label class="d-block">&nbsp;</label>
          <button type="button" class="btn btn-link" id="add-item-btn" data-id="duplicate0"><i class="mdi mdi-plus"></i>
            Add
          </button>
        </div>
      </div>
    </div>
  </fieldset>
</fieldset>

@include('users::customers.shared.item-list')

@push('js')
  @include('users::customers.orders.partials.add-item-script')
@endpush
