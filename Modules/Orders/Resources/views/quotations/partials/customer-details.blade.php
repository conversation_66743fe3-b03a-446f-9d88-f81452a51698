<div class="form-row mb-3">
  <div class="col-md-6 border-right border-light pr-2">
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label for="account_number">Bill To</label>
          <input class="form-control disabled not-editable"
                 type="text"
                 value="{{ $customer->account_number }}"
                 data-customer-id="{{ $customer->user_id }}"
                 data-taxable="{{ $customer->taxable ? 'true' : 'false' }}"
                 id="account_number"
                 disabled>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <label for="telephone">Telephone</label>
          <input type="text"
                 name="telephone"
                 class="form-control show-change"
                 id="telephone"
                 value="{{ old('telephone', $quotation->billing_phone ?? optional($customer)->phone) }}">
        </div>
      </div>
    </div>
    <div class="form-group">
      <label for="billing_company">Name</label>
      <input
        class="form-control show-change"
        type="text"
        id="billing_company"
        name="billing_company"
        value="{{ old('billing_company', $quotation->billing_name ?? optional($customer->accountAddress)->name ?? $customer->name) }}">
    </div>
    <div class="form-group">
      <label for="billing_address">Address</label>
      <textarea class="form-control show-change mb-1"
                id="billing_address"
                data-state="{{ optional($customer->accountAddress)->state }}"
                rows="6"
                name="billing_address">{{ old('billing_address', $quotation->billing_address ?? optional($customer->accountAddress)->full_address) }}</textarea>
    </div>
  </div>
  <div class="col-md-6 pl-2">
    @if(! \Illuminate\Support\Str::contains(url()->current(), 'duplicates'))
      <div class="form-group">
        <label for="shipping-address-select-input">Shipping Address</label>
        <select class="form-control show-change" id="shipping-address-select-input">
          <option value="">Select Shipping Address</option>
          @foreach($customer->shippingAddresses as $shippingAddress)
            <option value="{{ $shippingAddress->id }}"
                    data-name="{{ $shippingAddress->name }}"
                    data-state="{{ $shippingAddress->state }}"
                    data-address="{{ $shippingAddress->full_address }}"
              {{ $shippingAddress->id === optional($defaultShippingAddress)->id ? 'selected' : '' }}>
              {{ $shippingAddress->full_address }}
            </option>
          @endforeach
        </select>
      </div>
    @endif
    <div class="form-group">
      <label for="shipping_phone">Ship Phone</label>
      <input
        class="form-control show-change"
        type="text"
        id="shipping_phone"
        name="shipping_phone"
        value="{{ old('shipping_phone', $quotation->shipping_phone ?? optional($customer ?? $quotation->customer)->phone) }}"
        >
    </div>
    <div class="form-group">
      <label for="shipping_company">Name</label>
      <input
        class="form-control show-change"
        type="text"
        id="shipping_company"
        name="shipping_company"
        value="{{ old('shipping_company', $quotation->shipping_name ?? optional($defaultShippingAddress)->name) ?? $customer->name }}" >
    </div>
    <div class="form-group">
      <label for="shipping_address">Address</label>
      <textarea class="form-control show-change mb-1"
                id="shipping_address"
                data-state="{{ optional($defaultShippingAddress)->state }}"
                rows="6"
                name="shipping_address" {{ ! auth()->user()->can('update quote shipping address only') ? 'disabled' : '' }}>{{ old('shipping_address', $quotation->shipping_address ?? optional($defaultShippingAddress)->full_address) }}</textarea>
    </div>
  </div>
</div>

<button class="d-none" type="button" data-toggle="modal" data-target="#shipOutOfStateModal"
        id="out-of-state-confirmation-modal">Toggle No Tax
</button>
<div id="shipOutOfStateModal" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-sm">
    <div class="modal-content modal-filled bg-danger">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="mdi mdi-send h1"></i>
          <h4 class="mt-2">Confirm Action!</h4>
          <p class="mt-3">This order appears to be shipping out of state, do you want sales tax on this order?</p>

          <button type="button" class="btn btn-outline-light my-2 px-3" id="confirm-tax-out-of-state-btn">Yes!</button>
          <button type="button" class="btn btn-light my-2 px-3" id="confirm-no-tax-out-of-state-btn">No!</button>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
@push('js')
  @include('orders::shared.scripts.customer-details-script')
@endpush
