@foreach($quotation->items as $item)
  <tr
    id="item-{{ $item->item_id }}"
    original-id="{{ $item->id }}"
    class="existing-item-row"
    data-item-id="{{ $item->id }}"
    data-url="{{ route('orderModule.quotations.items.update', [$quotation, $item]) }}">
    <td
      class="d-flex flex-nowrap align-items-center position-relative">
      <i class="mdi mdi-sort mr-1"></i>
      <input
        class="item-quantity-input text-center form-control existing-item-input show-change"
        type="number"
        step="0.01"
        value="{{ old('quantity.' . $loop->index, $item->quantity)  }}"
        style="min-width: 30px; padding: 3px;" {{ ! auth()->user()->hasAnyPermission(['update quote', 'update quote item quantity only']) ? 'disabled': '' }}>
    </td>
    <td>
      <div class="form-group">
        <input class="form-control item-code-input show-change existing-item-input"
               type="text"
               value="{{ old('code.' . $loop->index, $item->code) }}">
      </div>
    </td>
    <td>
      @if($item->isInventoryItem())
        <div class="form-group">
          <input
            class="form-control item-number-input show-change existing-item-input"
            type="text"
            value="{{ old('number.' . $loop->index, $item->number) }}">
        </div>
      @else
        <input type="hidden" value="{{ old('number.' . $loop->index, $item->number) }}">
      @endif
    </td>
    <td>
      @if($item->isInventoryItem())
        <div class="form-group">
          <input
            class="form-control item-location-input existing-item-input show-change"
            type="text"
            value="{{ old('location.' . $loop->index, $item->location) }}"
            {{ $inputDisabled }}>
        </div>
      @else
        <input type="hidden" value="{{ old('location.' . $loop->index, $item->location) }}">
      @endif
    </td>
    <td>
      <div class="form-group">
        <textarea
          class="form-control item-description-input existing-item-input show-change"
                    {{ ! auth()->user()->can('update quote item description only') ? 'disabled' : '' }}
          style="min-width: 120px">{{ old('description.' . $loop->index, $item->description) }}</textarea>
        <input type="hidden"
               step="0.001"
               min="0"
               value="{{ $item->weight }}">
      </div>
    </td>
    <td>
      <div class="form-group">
        <textarea
          class="form-control item-build-instructions-input existing-item-input
          show-change" style="min-width: 120px">{{ old
          ('build_instructions.' . $loop->index, $item->build_instructions) }}</textarea>
      </div>
    </td>
    <td class="text-right hide-price {{ $hidePrices ? 'd-none' : '' }}">
      <input
        class="price-override-input text-center form-control existing-item-input show-change d-inline-block "
        type="text"
        value="{{ old('price.' . $loop->index, $item->price) }}"
        step="0.01"
        data-price="{{ number_format($item->price, 2, '.', '') }}"
        {{ ! auth()->user()->can('update quote item price only') ? 'disabled' : '' }}>
    </td>
    <td class="text-right hide-price {{ $hidePrices ? 'd-none' : '' }} item-total">
      {{ number_format($item->price * $item->quantity, 2) }}
    </td>
    <td class="extra-detail d-none">
      {{ optional($item->item)->formatted_cost_date }}
    </td>
    <td class="text-right extra-detail d-none">
      <div class="form-group">
        <input
          class="cost-override-input text-center form-control existing-item-input show-change "
          type="text"
          value="{{ number_format(old('cost.' . $loop->index, $item->cost), 2, '.', '') }}"
          data-cost="{{ number_format(old('cost.' . $loop->index, $item->cost), 2, '.', '') }}"
          step="0.01">
      </div>
    </td>
    <td class="text-right extra-detail d-none item-total-cost">
      {{ number_format($item->total_cost, 2) }}
    </td>
    <td class="text-right extra-detail d-none item-markup">
      {{ number_format($item->markup, 3) }}
    </td>
    <td class="extra-detail d-none">
      <select
        class="form-control existing-item-input commission_type"
        style="min-width: 80px">
        <option value="">Select option</option>
        @foreach($commissionTypes as $commissionCode)
          <option value="{{ $commissionCode }}" {{ old('commission_type.' . $loop->index,
          $item->commission_type) === $commissionCode ? 'selected' : '' }}>
            {{ $commissionCode }}
          </option>
        @endforeach
      </select>
    </td>
    <td class="extra-detail d-none text-right">{{ optional($item->item)->quantity }}</td>
    @if(auth()->user()->hasAnyPermission(['update quote', 'delete quote item']))
      <td class="text-right">
        <div class="d-flex flex-nowrap">
          <a
            href="#"
            class="text-muted request-delete-item-btn"
            data-target="#deleteExistingItem"
            data-toggle="modal"
            data-url="{{ route('orderModule.quotations.items.destroy', [$quotation, $item]) }}">
            <i class="mdi mdi-delete"></i>
          </a>
        </div>
      </td>
    @endif
  </tr>
@endforeach
