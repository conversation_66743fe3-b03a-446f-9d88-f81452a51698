<div id="convertToOrder" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-wrong h1"></i>
          <h4 class="mt-2">Confirm Action!</h4>
          <p class="mt-3">This will convert the quotation into an order. You can still make changes after the
            quotation is converted into an order</p>
          <form action="{{ route('orderModule.quotations.makeOrder', $quotation) }}" method="post">
            @csrf

            <div class="form-group d-flex justify-content-between">
              <button type="button" class="btn btn-outline-light text-dark my-2 mr-2" data-dismiss="modal">Cancel
              </button>
              <button type="submit" class="btn btn-success my-2">Yes, Convert to Order!</button>
            </div>
          </form>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
<div id="duplicateQuotation" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-wrong h1 mb-2"></i>
          <h4 class="mb-3">Confirm Action!</h4>
          <p class="mb-4">How do you want to duplicate Quotation #{{ $quotation->id }}.</p>

          <div class="">
            <button type="button"
                    class="btn btn-outline-light text-dark mr-1"
                    data-dismiss="modal">
              Cancel
            </button>
            <a href="{{ route('orderModule.quotations.duplicates.create', $quotation) }}"
               class="btn btn-dark mr-1">
              For another Customer...
            </a>
            <form class="d-inline-block"
                  action="{{ route('orderModule.quotations.duplicates.store', $quotation) }}"
                  method="post">
              @csrf
              <button type="submit" class="btn btn-info font-weight-bold">
                For this Customer
              </button>
            </form>

          </div>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
<div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-sm">
    <div class="modal-content modal-filled bg-danger">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-wrong h1"></i>
          <h4 class="mt-2">Confirm Action!</h4>
          <p class="mt-3">This will delete the item from quotation.</p>
          <form action="#" method="post" id="delete-item-form">
            @csrf
            @method('delete')
            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
          </form>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>

<div id="deleteExistingItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-sm">
    <div class="modal-content modal-filled bg-danger">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-wrong h1"></i>
          <h4 class="mt-2">Confirm Action!</h4>
          <p class="mt-3">Are you sure you want to delete this record. This cannot be undone.</p>
          <button type="button" class="cancel-delete-existing-item-modal btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
          <button type="button" class="delete-existing-item-btn btn btn-light my-2">Yes, Delete!</button>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>

<div id="deleteQuotation" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-sm">
    <div class="modal-content modal-filled bg-danger">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-wrong h1"></i>
          <h4 class="mt-2">Confirm Action!</h4>
          <p class="mt-3">This will delete the quotation from the same.</p>
          <form action="{{ route('orderModule.quotations.destroy', $quotation) }}" method="post"
                id="delete-quotation-form">
            @csrf
            @method('delete')
            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
          </form>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>

<div
  id="emailQuotationModal"
  class="modal fade"
  tabindex="-1"
  role="dialog"
  style="display: none;"
  aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-alert h1 mr-2"></i>
          <h4 class="mt-2">Confirm Action!</h4>
        </div>
        <div class="mt-3">
          <p class="text-muted">This will send <strong>Quotation# {{ $quotation->full_number }}</strong> to the customer's email(s) below. Are you sure about this?</p>
          <form action="{{ route('orderModule.quotations.send', $quotation) }}" method="post">
            @csrf
            <div class="form-group">
              <label for="invoice-emails">Recipients <small class="text-muted">- Separate multiple emails with a comma.</small></label>
              <input
                class="form-control"
                type="text"
                name="recipients"
                id="invoice-emails"
                value="{{ old('recipients', $quotation->customer->invoice_recipients) }}"
                required>
            </div>
            <div class="form-group">
              <label>Message <small>- customize the message to send along with the quotation.</small></label>
              <textarea
                class="form-control"
                name="body"
                rows="5"
                required>{{ settings('global.emails.quotations.message', '') }}</textarea>
              <small>This text will be replaced: <strong>$customer</strong> by Customer Name, <strong>$quotation</strong> by Quotation Number</small>
            </div>
            <div class="d-flex justify-content-end">
              <button type="button" class="btn btn-light my-2 mr-2 px-3" data-dismiss="modal">Cancel</button>
              <button type="submit" class="btn btn-primary my-2 px-3">Yes, Send Email <i class="mdi mdi-send ml-2"></i></button>
            </div>
          </form>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>

<div id="uploadAttachment" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-upload text-muted h1"></i>
          <h4 class="mt-2">Upload Attachment</h4>
        </div>
        <form action="{{ route('orderModule.quotations.attachments.store', $quotation) }}"
              method="post"
              id="order-attachment-form"
              enctype="multipart/form-data">
          @csrf

          <div class="form-group">
            <input type="file" name="attachments[]" class="form-control" multiple required>
          </div>
          <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-outline-secondary my-2" data-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-primary my-2">Save Changes!</button>
          </div>
        </form>

      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>


