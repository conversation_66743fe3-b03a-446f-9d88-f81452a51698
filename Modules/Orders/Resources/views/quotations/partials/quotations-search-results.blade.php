<div class="table-responsive">
  <table class="table table-centered w-100 dt-responsive nowrap datatable search-autofocus">
    <thead class="thead-light">
    <tr>
      <th class="text-right">Quotation #</th>
      <th class="text-right">Account #</th>
      <th>Customer</th>
      <th class="text-right">Total Amount</th>
      <th>Created By</th>
      <th>Date</th>
      <th>Status</th>
      <th class="text-right" style="min-width: 85px;">Action</th>
    </tr>
    </thead>
    <tbody>
    @foreach ($quotations as $quotation)
      <tr>
        <td class="text-right">
          <a href="{{ route('orderModule.quotations.show', $quotation->id) }}">
            {{ $quotation->full_number }}
          </a>
        </td>
        <td class="text-right">{{ $quotation->customer_account_number }}</td>
        <td><a href="{{ route('users.customers.show', $quotation->customer_id) }}">{{ $quotation->customer_name }}</a>
        </td>
        <td class="text-right">{{ number_format($quotation->total_amount, 2) }}</td>
        <td><a href="{{ route('users.staff.show', $quotation->created_by_id) }}">{{ $quotation->created_by_name }}</a>
        </td>
        <td>{{ $quotation->created_at }}</td>
        <td>{{ ucfirst($quotation->status) }}</td>
        <td class="table-action text-right">
          <a href="{{ route('orderModule.quotations.show', $quotation->id) }}" class="mr-1 d-inline-block">
            <i class="mdi mdi-eye"></i>
            View
          </a>
          @can('delete quote')
            <a href="#"
               data-target="#deleteItem"
               data-toggle="modal"
               class="delete-item-btn text-muted"
               data-url="{{ route('orderModule.quotations.destroy', $quotation->id) }}">
              <i class="mdi mdi-delete"></i>
              Delete
            </a>
          @endcan
        </td>
      </tr>
    @endforeach
    </tbody>
  </table>
</div>
