@extends('layouts.master')

@section('title', 'Commissions')

@prepend('styles')
    <!-- third party css -->
    <link href="{{ asset('css/vendor/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('css/vendor/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
    <!-- third party css end -->
@endprepend

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">Decommissions</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Decommissioned Items</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <div class="row mb-3">
            <div class="col-sm-12 text-right">
                @can('update commission')
                <a href="{{ route('orderModule.decommissions.create') }}"
                   class="btn btn-outline-secondary mr-1">
                    <i class="mdi mdi-plus"></i>
                    Add Item
                </a>
                @endcan
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-12">
                <div class="card">
                    <div class="card-body pt-0">
                        <div id="basicwizard">
                            <ul class="nav nav-pills nav-justified form-wizard-header mb-4">
                                @foreach($decommissionQuarters as $quarter)
                                    <li class="nav-item">
                                        <a href="#quarter{{ $quarter }}" data-toggle="tab" class="nav-link rounded-0 {{ $quarter == $currentQuarter ? 'active' : '' }} d-flex align-items-center justify-content-center">
                                            Q<span class="d-none d-sm-inline">uarter</span><i class="mdi mdi-numeric-{{ $quarter }} mdi-24px"></i>
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                            <div class="tab-content b-0 mb-0">
                                @foreach($decommissions as $key => $items)
                                <div class="tab-pane {{ $key == $currentQuarter ? 'active' : '' }}" id="quarter{{ $key }}">
                                    <div class="table-responsive">
                                        <table class="table table-borderless datatable search-autofocus">
                                            <thead>
                                                <tr>
                                                    <th>Item</th>
                                                    @can('update commission')
                                                        <th class="text-right">Action</th>
                                                    @endcan
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($items as $item)
                                                    <tr>
                                                        <td>
                                                            <a class="text-info" href="{{ route('inventory.items.show', $item) }}">
                                                                {{ $item->code }}
                                                                <small class="d-block text-body">{{ $item->name }}</small>
                                                            </a>
                                                        </td>
                                                        @can('update commission')
                                                        <td class="text-right">
                                                            <button type="button" class="btn btn-link btn-sm pr-0 text-danger delete-item-btn" data-toggle="modal" data-target="#deleteItem" data-url="{{ route('orderModule.decommissions.destroy', $item->id) }}" data-quarter="{{ $key }}"><i class="mdi mdi-delete"></i> Delete</button>
                                                        </td>
                                                        @endcan
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col -->
        </div>
        <!-- end row -->
    </div>

    <div id="deleteItem" class="modal fade" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content modal-filled bg-danger">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="dripicons-wrong h1"></i>
                        <h4 class="mt-2">Confirm Action!</h4>
                        <p class="mt-3">This will remove the item from the list of decommissioned items and users will start earning commission on the item. Are you about this?</p>
                        <form action="#" method="post" id="delete-item-form">
                            @csrf
                            @method('delete')
                            <input class="quarter" type="hidden" name="quarter" value="">
                            <button type="button" class="btn btn-outline-danger text-white my-2" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-light my-2">Yes, Delete!</button>
                        </form>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- container -->
@endsection

@push('js')
    <!-- third party js -->
    <script src="{{ asset('js/vendor/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/vendor/dataTables.bootstrap4.js') }}"></script>
    <script src="{{ asset('js/vendor/dataTables.responsive.min.js') }}"></script>
    <script src="{{ asset('js/vendor/responsive.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/vendor/dataTables.checkboxes.min.js') }}"></script>
    <!-- third party js ends -->

    <!-- demo app -->
    <script src="{{ asset('js/pages/demo.products.js') }}"></script>

    <script>
        $('.delete-item-btn').click(function (e) {
            let form = $('#delete-item-form');
            form.attr('action', $(this).attr('data-url'));
            form.find('.quarter').attr('value', $(this).attr('data-quarter'));
        });
    </script>
@endpush
