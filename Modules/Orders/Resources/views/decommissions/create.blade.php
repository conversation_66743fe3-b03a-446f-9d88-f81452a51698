@extends('layouts.master')

@section('title', 'Add Decommissioned Item')

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('orderModule.decommissions.index') }}">Decommissions</a></li>
                            <li class="breadcrumb-item active">Add Decommissioned Item</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Add Decommissioned Item</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10 col-sm-12">
                <div class="card">
                    <div class="card-body px-5 py-4">
                        <p>Commissioined will not be earned from the items that are decommissioned.</p>
                        <form action="{{ route('orderModule.decommissions.store') }}"
                              method="post"
                              enctype="multipart/form-data">
                            @csrf
                            <fieldset class="mb-4">
                            @if (old('item_id'))
                                @foreach(old('item_id') as $oldItemId)
                                    <div class="form-row duplicate mb-2">
                                        <div class="col-md-6">
                                            <label for="item_id{{ $loop->index }}">Name</label>
                                            <select
                                                name="item_id[]"
                                                class="form-control select2 @error('item_id.' . $loop->index) is-invalid @enderror"
                                                id="item_id{{ $loop->index }}"
                                                data-toggle="select2">
                                                <option value="">Select Item</option>
                                                @foreach($itemList as $itemId => $itemName)
                                                    <option value="{{ $itemId }}" {{ $oldItemId == $itemId ? 'selected' : '' }}>{{ $itemName }}</option>
                                                @endforeach
                                            </select>
                                            @error('item_id.' . $loop->index)
                                                <div class="invalid-feedback">
                                                    This field has a duplicate value
                                                </div>
                                            @enderror
                                        </div>
                                        <div class="col-md-3">
                                            <label for="quarter{{ $loop->index }}">Quarter</label>
                                            <input class="form-control @error('quarter.' . $loop->index) is-invalid @enderror"
                                                   type="number"
                                                   id="quarter{{ $loop->index }}"
                                                   name="quarter[]"
                                                   min="1"
                                                   max="4"
                                                   value="{{ old('quarter.' . $loop->index) }}">
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="d-block">&nbsp;</label>
                                                <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="{{ $loop->last ? 'display:none;' : '' }}"><i class="mdi mdi-close"></i> Remove </button>
                                                <button type="button" class="btn btn-link duplicate-btn" data-id="duplicate{{ $loop->index }}"  style="{{ $loop->last ? '' : 'display: none;' }}"><i class="mdi mdi-plus"></i> Add</button>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="form-row duplicate">
                                    <div class="col-md-6">
                                        <label>Name</label>
                                        <select
                                            name="item_id[]"
                                            class="form-control select2"
                                            data-toggle="select2">
                                            <option value="">Select Item</option>
                                            @foreach($itemList as $itemId => $itemName)
                                                <option value="{{ $itemId }}">{{ $itemName }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label>Quarter</label>
                                        <input class="form-control"
                                               type="number"
                                               name="quarter[]"
                                               min="1"
                                               max="4"
                                               value="{{ now()->quarter }}">
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="d-block">&nbsp;</label>
                                            <button type="button" class="btn btn-link remove-duplicate-btn text-danger" style="display:none"><i class="mdi mdi-close"></i> Remove </button>
                                            <button type="button" class="btn btn-link duplicate-btn" data-id="duplicate0"><i class="mdi mdi-plus"></i> Add</button>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            </fieldset>
                            <div class="form-row">
                                <div class="col-sm-12 d-flex justify-content-end">
                                    <a class="btn btn-light mr-2" href="{{ route('orderModule.decommissions.index') }}"><i class="mdi mdi-cancel mr-1"></i> Cancel</a>
                                    <button type="submit" class="btn btn-primary"><i class="mdi mdi-check mr-1"></i> Save Changes</button>
                                </div>
                            </div>
                        </form>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </div> <!-- end col-->
        </div>
        <!-- end row-->

    </div>
    <!-- container -->
@endsection


@include('inventory::shared.forms.duplicate')

