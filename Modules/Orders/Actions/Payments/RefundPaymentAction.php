<?php


namespace Modules\Orders\Actions\Payments;


use Illuminate\Support\Arr;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Payment;
use Modules\Users\Entities\Customer;

class RefundPaymentAction
{
  public function execute(Customer $customer, array $refundDetails = []): bool
  {
    $existingInvoice = Order::findOrFail($refundDetails['invoice_id']);
    $negativeAmountRefunded = $refundDetails['amount'] * -1;
    $record = Arr::except($refundDetails, ['invoice_id', 'payment_method']);
    $record['number'] = (new Payment)->nextPaymentNumber();
    $record['amount'] = $negativeAmountRefunded;
    $record['created_by'] = auth()->id();
    $record['refund'] = true;
    $payment = $customer->payments()->create($record);

    $paymentLog = 'Payment# ' . $payment->number . ' $' . number_format($negativeAmountRefunded, 2);

    if (! empty($existingPaymentLog = $existingInvoice->payment_log)) {
      $paymentLog .= "\n" . $existingPaymentLog;
    }

    $newBalance = money_subtract(abs($existingInvoice->balance), $refundDetails['amount']) * -1; // Negate amount so we can still refund it if not zeroed out.
    $invoiceFullyRefunded = money_equals($newBalance, 0) ? 1 : 0;
    $existingInvoice->amount_paid = $negativeAmountRefunded;
    $existingInvoice->balance = $newBalance;
    $existingInvoice->payment_log = $paymentLog;
    $existingInvoice->negative_balance_utilized = $invoiceFullyRefunded;
    $existingInvoice->paid = $invoiceFullyRefunded;
    $existingInvoice->paid_at = $invoiceFullyRefunded ? now()->toDateTimeString() : null;
    $existingInvoice->last_payment_date = now()->toDateTimeString();
    $existingInvoice->save();


    $payment->invoices()->attach([
      $existingInvoice->id => [
        'amount' => $negativeAmountRefunded
      ]
    ]);

    return true;
  }
}
