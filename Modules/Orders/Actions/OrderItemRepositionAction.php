<?php

namespace Modules\Orders\Actions;

use Modules\Orders\Entities\Order;

class OrderItemRepositionAction
{
  public function execute(Order $order, array $inputs)
  {
    $inputtedItems = $inputs['items'];

    foreach ($inputtedItems as $inputtedItem) {
      $orderItem = $order->items()
        ->find($inputtedItem['item_id']);

      if (!$orderItem) continue;

      $orderItem->position = $inputtedItem['item_position'];
      $orderItem->save();
    }
  }
}
