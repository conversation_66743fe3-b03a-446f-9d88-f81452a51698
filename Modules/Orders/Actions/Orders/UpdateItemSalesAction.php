<?php


namespace Modules\Orders\Actions\Orders;


use Modules\Inventory\Entities\Item;
use Modules\Orders\Entities\CategorySale;
use Modules\Orders\Entities\ItemSale;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;

class UpdateItemSalesAction
{
  protected GetItemSalesAction $action;

  public function __construct(GetItemSalesAction $action)
  {
    $this->action = $action;
  }

  /**
   * @param Order $order
   * @return boolean
   */
  public function execute(Order $order): bool
  {
    $itemSales = $this->action->execute($order);

    $salesDate = $order->created_at->format('Y-m-d');

    foreach($itemSales as $itemId => $sales) {
      $itemSale = ItemSale::where('sales_date', $salesDate)
        ->where('item_id', $itemId)
        ->first();

      if (empty($itemSale)) {
        if ($order->notCanceled()) {
          ItemSale::create([
            'item_id' => $itemId,
            'quantity' => $sales,
            'sales_date' => $salesDate,
          ]);
        }
      } else {
        if ($order->canceled()) {
          if ($sales < $itemSale->quantity) {
            $itemSale->decrement('quantity', $sales);
          } else {
            $itemSale->update(['quantity' => 0]);
          }
        } else {
          $itemSale->increment('quantity', $sales);
        }
      }
    }

    return true;
  }
}
