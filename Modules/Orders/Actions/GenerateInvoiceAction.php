<?php


namespace Modules\Orders\Actions;


use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Support\Facades\Storage;
use Modules\Orders\Entities\Order;

class GenerateInvoiceAction
{
  public string $filename = '';
  protected string $view = 'orders::orders.print-invoice';

  public function execute(Order $order)
  {
    $pdf = app()->make('snappy.pdf.wrapper')
      ->loadView($this->view, compact('order'));

    if (empty($this->filename)) {
      $this->filename($order->number);
    }

    $pdf->save($this->filename, true);
  }

  public function filename(string $name): self
  {
    $this->filename = storage_path('app/invoiced-orders/customers/') . $name . '.pdf';

    return $this;
  }
}
