<?php


namespace Modules\Orders\Actions\Quotations;


use App\Exceptions\Orders\QuotationAlreadyConverted;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\Quotation;
use Modules\Orders\Entities\QuotationLine;
use Modules\Orders\Events\Orders\OrderCreated;
use Modules\Orders\Traits\HandlesQuotation;
use Modules\Orders\Traits\UpdatesQuotationAmounts;

class ConvertToOrderAction
{
  /**
   * @throws QuotationAlreadyConverted
   */
  public function execute(Quotation $quotation)
  {
    if ($quotation->converted) {
      throw new QuotationAlreadyConverted();
    }

    $number = (new Order)->nextOrderNumber();
    $record = Arr::only($quotation->toArray(), $this->getOrderProperties());
    $record['number'] = $number;
    $record['full_number'] = auth()->user()->staff->initials . $number;
    $record['type'] = Order::TYPE_ORDER; // Todo: No longer required after separation of quotations from orders
    $record['created_by'] = auth()->id();
    $record['options'] = $this->getOptions();
    $record['created_at'] = now();
    $record['out_of_state'] = 0;
    $record['customer_taxable'] = $quotation->customer->taxable;

    if (! empty($billingAddress = $quotation->customer->billingAddress)) {
      $record['billing_name'] = $billingAddress->name;
      $record['billing_address'] = $billingAddress->address;
    }

    $order = Order::create($record);

    $lineItems = $quotation->items->map(function (QuotationLine $item) {
      $item = Arr::except($item->toArray(), ['id', 'quotation_id']);
      $item['item_id'] = $item['item_id'] ?? '';

      return $item;
    })->all();

    $order->items()->createMany($lineItems);

    $quotation->order_id = $order->id;
    $quotation->converted = true;
    $quotation->converted_at = now();
    $quotation->save();

    event(new OrderCreated($order));

    return $order;
  }

  /**
   * Get options for the order
   *
   * @return array
   */
  protected function getOptions(): array
  {
    return [
      'statuses' => [
        'created' => [
          'name' => 'created',
          'created_at' => now()->format('m-d-y H:i:s'),
          'created_by' => auth()->id(),
          'previous' => null,
          'note' => ''
        ]
      ]
    ];
  }

  protected function getOrderProperties(): array
  {
    return [
      'customer_id',
      'purchase_order_number',
      'amount',
      'tax_amount',
      'total_amount',
      'shipping_name',
      'shipping_address',
      'shipping_phone',
      'billing_name',
      'billing_address',
      'billing_phone',
      'out_of_state',
      'force_no_tax',
      'force_tax',
      'salesperson',
      'shop_notes',
      'private_notes',
      'is_service',
      'service_hours',
      'service_rate',
      'service_call'
    ];
  }
}
