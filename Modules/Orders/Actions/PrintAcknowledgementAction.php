<?php

namespace Modules\Orders\Actions;

use App\Services\PdfGeneratorService;
use Illuminate\Support\Facades\Storage;
use Modules\Orders\Entities\Order;

class PrintAcknowledgementAction
{
  protected $pdfService;

  protected string $view = 'orders::orders.print-acknowledgement';

  /**
   * @var string
   */
  public string $filename;

  public function __construct(PdfGeneratorService $pdfGeneratorService)
  {
    $this->pdfService = $pdfGeneratorService;
  }

  public function execute(Order $order, string $streamFilename = '')
  {
    if (empty($this->filename)) {
      $this->filename($order->number);
    }

    if (! $order->acknowledged) {
      $order->acknowledged = true;
      $order->acknowledged_at = now();
      $order->acknowledged_by = optional(auth()->user())->name;
      $order->save();
    }

    $pdf = $this->pdfService
      ->view($this->view)
      ->content(['order' => $order]);

    if (! empty($streamFilename)) {
      return $pdf->stream($streamFilename);
    }

    return $pdf->save($this->filename);
  }

  public function filename(string $name): self
  {
    Storage::disk('local')->makeDirectory('generated/acknowledgements');

    $this->filename = storage_path('app/generated/acknowledgements') . "/{$name}.pdf";

    return $this;
  }
}
