<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexOnActivityLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
      Schema::table('activity_logs', function (Blueprint $table) {
        $table->index(['loggable_type', 'loggable_id']);
      });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
      Schema::table('activity_logs', function (Blueprint $table) {
        $table->dropIndex(['loggable_type', 'loggable_id']);
      });
    }
}
