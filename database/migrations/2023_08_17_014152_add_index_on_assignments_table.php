<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexOnAssignmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
      Schema::table('assignments', function (Blueprint $table) {
        $table->index(['assignable_type', 'assignable_id']);
      });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
      Schema::table('assignments', function (Blueprint $table) {
        $table->dropIndex(['assignable_type', 'assignable_id']);
      });
    }
}
