<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateImportInvoicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('import_invoices', function (Blueprint $table) {
            $table->id();
            $table->integer('parent_id');
            $table->string('record_id')->nullable();
            $table->string('quantity')->nullable();
            $table->string('tax')->nullable();
            $table->string('code')->nullable();
            $table->string('cost')->nullable();
            $table->string('date')->nullable();
            $table->string('line')->nullable();
            $table->string('temp')->nullable();
            $table->string('time')->nullable();
            $table->string('unit')->nullable();
            $table->string('dummy')->nullable();
            $table->text('notes')->nullable();
            $table->string('purchase_order_number')->nullable();
            $table->string('part_number')->nullable();
            $table->string('price')->nullable();
            $table->string('total')->nullable();
            $table->string('expand')->nullable();
            $table->string('order_number')->nullable();
            $table->string('quote_number')->nullable();
            $table->string('volume')->nullable();
            $table->string('weight')->nullable();
            $table->string('ordered')->nullable();
            $table->string('package')->nullable();
            $table->text('printed')->nullable();
            $table->string('product_location')->nullable();
            $table->text('product')->nullable();
            $table->string('taxable')->nullable();
            $table->string('category')->nullable();
            $table->string('customer_code')->nullable();
            $table->string('customer')->nullable();
            $table->string('invoice_number')->nullable();
            $table->string('location')->nullable();
            $table->string('modified')->nullable();
            $table->string('ready_quantity')->nullable();
            $table->string('shipping')->nullable();
            $table->string('sold_for')->nullable();
            $table->string('sync_time')->nullable();
            $table->string('tracking')->nullable();
            $table->string('available')->nullable();
            $table->string('backorder')->nullable();
            $table->string('ordered_by')->nullable();
            $table->string('ship_date')->nullable();
            $table->string('commission')->nullable();
            $table->string('final_date')->nullable();
            $table->string('finished_by')->nullable();
            $table->string('new_pricing')->nullable();
            $table->string('picked_by')->nullable();
            $table->string('quote_date')->nullable();
            $table->string('ready_total')->nullable();
            $table->string('search_tag')->nullable();
            $table->string('ship_phone')->nullable();
            $table->text('shop_notes')->nullable();
            $table->string('technician')->nullable();
            $table->string('date_shipped')->nullable();
            $table->text('private_notes')->nullable();
            $table->string('salesperson')->nullable();
            $table->string('ship_to')->nullable();
            $table->string('ship_address')->nullable();
            $table->string('ship_company')->nullable();
            $table->string('actual_freight')->nullable();
            $table->string('numeric_part_number')->nullable();
            $table->string('unit_location')->nullable();
            $table->string('billing_address')->nullable();
            $table->text('ship_date_notes')->nullable();
            $table->string('back_order_created')->nullable();
            $table->string('commissioned_item')->nullable();
            $table->boolean('imported')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('import_invoices');
    }
}
