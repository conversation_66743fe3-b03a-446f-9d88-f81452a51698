services:
  traefik:
    ports:
    - "80:80"
    - "443:443"
    networks:
      development:
    volumes:
      # Mount the Docker socket as read-only so <PERSON><PERSON><PERSON><PERSON> can listen to events
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./.infrastructure/conf/traefik/dev/traefik.yml:/traefik.yml:ro
      - ./.infrastructure/conf/traefik/dev/traefik-certs.yml:/traefik-certs.yml
      - ./.infrastructure/conf/traefik/dev/certificates/:/certificates

  php:
    build:
      target: development
      args:
        USER_ID: ${SPIN_USER_ID}
        GROUP_ID: ${SPIN_GROUP_ID}
    volumes:
      - .:/var/www/html/
    networks:
      - development
    depends_on:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.laravel.rule=HostRegexp(`localhost`)"
      - "traefik.http.routers.laravel.entrypoints=web"
      - "traefik.http.services.laravel.loadbalancer.server.port=8080"
      - "traefik.http.services.laravel.loadbalancer.server.scheme=http"
  
  node:
    image: node:20
    volumes:
      - .:/usr/src/app/
    working_dir: /usr/src/app/
    networks: 
      - development

  mailpit:
      image: axllent/mailpit
      networks:
        - development
      ports:
        - "8025:8025"

networks:
  development: