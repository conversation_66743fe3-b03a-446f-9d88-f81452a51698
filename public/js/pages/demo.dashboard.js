!function (c) {
    "use strict";
    var r = function () {
        this.$body = c("body"), this.charts = []
    };
    r.prototype.respChart = function (t, a, e, o) {
        var n = Chart.controllers.line.prototype.draw;
        Chart.controllers.line.prototype.draw = function () {
            n.apply(this, arguments);
            var r = this.chart.chart.ctx, t = r.stroke;
            r.stroke = function () {
                r.save(), r.shadowColor = "rgba(0,0,0,0.01)", r.shadowBlur = 20, r.shadowOffsetX = 0, r.shadowOffsetY = 5, t.apply(this, arguments), r.restore()
            }
        };
        var s = Chart.controllers.doughnut.prototype.draw;
        Chart.controllers.doughnut = Chart.controllers.doughnut.extend({
            draw: function () {
                s.apply(this, arguments);
                var r = this.chart.chart.ctx, t = r.fill;
                r.fill = function () {
                    r.save(), r.shadowColor = "rgba(0,0,0,0.03)", r.shadowBlur = 4, r.shadowOffsetX = 0, r.shadowOffsetY = 3, t.apply(this, arguments), r.restore()
                }
            }
        });
        var l = Chart.controllers.bar.prototype.draw;
        Chart.controllers.bar = Chart.controllers.bar.extend({
            draw: function () {
                l.apply(this, arguments);
                var r = this.chart.chart.ctx, t = r.fill;
                r.fill = function () {
                    r.save(), r.shadowColor = "rgba(0,0,0,0.01)", r.shadowBlur = 20, r.shadowOffsetX = 4, r.shadowOffsetY = 5, t.apply(this, arguments), r.restore()
                }
            }
        }), Chart.defaults.global.defaultFontColor = "#8391a2", Chart.defaults.scale.gridLines.color = "#8391a2";
        var i = t.get(0).getContext("2d"), d = c(t).parent();
        return function () {
            var r;
            switch (t.attr("width", c(d).width()), a) {
                case"Line":
                    r = new Chart(i, {type: "line", data: e, options: o});
                    break;
                case"Doughnut":
                    r = new Chart(i, {type: "doughnut", data: e, options: o});
                    break;
                case"Pie":
                    r = new Chart(i, {type: "pie", data: e, options: o});
                    break;
                case"Bar":
                    r = new Chart(i, {type: "bar", data: e, options: o});
                    break;
                case"Radar":
                    r = new Chart(i, {type: "radar", data: e, options: o});
                    break;
                case"PolarArea":
                    r = new Chart(i, {data: e, type: "polarArea", options: o})
            }
            return r
        }()
    }, r.prototype.initCharts = function () {
        var r = [];
        if (0 < c("#revenue-chart").length) {
            r.push(this.respChart(c("#revenue-chart"), "Line", {
                labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                datasets: [{
                    label: "Current Week",
                    backgroundColor: "transparent",
                    borderColor: "#727cf5",
                    data: [32, 42, 42, 62, 52, 75, 62]
                }, {
                    label: "Previous Week",
                    fill: !0,
                    backgroundColor: "transparent",
                    borderColor: "#0acf97",
                    data: [42, 58, 66, 93, 82, 105, 92]
                }]
            }, {
                maintainAspectRatio: !1,
                legend: {display: !1},
                tooltips: {
                    backgroundColor: "#727cf5",
                    titleFontColor: "#fff",
                    bodyFontColor: "#fff",
                    bodyFontSize: 14,
                    displayColors: !1
                },
                hover: {intersect: !0},
                plugins: {filler: {propagate: !1}},
                scales: {
                    xAxes: [{reverse: !0, gridLines: {color: "rgba(0,0,0,0.05)"}}],
                    yAxes: [{
                        ticks: {stepSize: 20},
                        display: !0,
                        borderDash: [5, 5],
                        gridLines: {color: "rgba(0,0,0,0.01)", fontColor: "#fff"}
                    }]
                }
            }))
        }

        return r
    }, r.prototype.init = function () {

    }, c.Dashboard = new r, c.Dashboard.Constructor = r
}(window.jQuery), function (r) {
    "use strict";
    window.jQuery.Dashboard.init()
}();
