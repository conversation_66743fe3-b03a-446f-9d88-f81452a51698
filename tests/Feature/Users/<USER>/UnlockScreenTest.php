<?php


namespace Tests\Feature\Users\Http;


use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;

class UnlockScreenTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_unlocks_screen_when_user_provides_correct_password()
  {
    $response = $this->actingAs($this->admin)
      ->post(route('auth.unlock'), [
        'password' => 'password'
      ]);

    $user = $this->admin->fresh();
    $response->assertJson([], true);
    $this->assertFalse($user->screen_locked);
  }

  public function test_locks_screen_when_user_provides_wrong_password()
  {
    $response = $this->actingAs($this->admin)
      ->post(route('auth.unlock'), [
        'password' => '123456'
      ]);

    $user = $this->admin->fresh();
    $response->assertJson(['message' => 'User is not authorized'], true);
    $this->assertTrue($user->screen_locked);
  }

}
