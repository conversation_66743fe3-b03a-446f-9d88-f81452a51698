<?php

namespace Tests\Feature\Users\Http\Customers;

use JMac\Testing\LaravelMatchers;
use Modules\Users\Entities\Customer;
use Modules\Users\Entities\User;
use Modules\Users\Http\Requests\CreateCustomerRequest;
use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Traits\CreatesTestUser;

class CustomerControllerTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_can_view_customer_listing_page()
  {
    $response = $this->actingAs($this->admin)
      ->get(route('users.customers.index'));

    $response->assertOk()
      ->assertViewIs('users::customers.index')
      ->assertSeeTextInOrder(['Orders', 'Add Customer']);
  }
  // TODO: test that user cannot view customer listing without permission
  public function test_can_view_customer_create_page()
  {
    $response = $this->actingAs($this->admin)
      ->get(route('users.customers.create'));

    $response->assertOk()
      ->assertViewIs('users::customers.create')
      ->assertViewHasAll(['priceLevels', 'preferenceList', 'customerTypeList', 'customerPaymentTermsList', 'customer', 'commissionTypeNames'])
      ->assertSeeTextInOrder(['Add Customer', 'Account', 'Addresses', 'Contacts']);
  }

  public function test_cannot_view_customer_create_page_without_permission()
  {
    $response = $this->actingAs($this->createStaffWithoutPermissions())
      ->get(route('users.customers.create'));

    $response->assertForbidden();
  }
  // TODO: Test that customer can be created with some preferences.
  public function test_can_create_new_customer()
  {
    $customerName = $this->faker->name;
    $customerRecord = $this->makeCustomerFormRequestData();

    $customerRecord['name'] = $customerName;

    $response = $this->actingAs($this->admin)
      ->post(route('users.customers.store'), $customerRecord);

    $user = User::whereName($customerName)->first();
    $customer = $user->customer;

    $response->assertRedirect(route('users.customers.index'))
      ->assertSessionHasNoErrors();
    $this->assertRouteUsesFormRequest('users.customers.store', CreateCustomerRequest::class);
    LaravelMatchers::isModel($user);
    LaravelMatchers::isModel($customer);
    LaravelMatchers::isEloquentCollection($customer->addresses);
    LaravelMatchers::isEloquentCollection($customer->contacts);
    $this->assertDatabaseHas('users', ['id' => $user->id, 'name' => $customerName]);
    $this->assertDatabaseHas('customers', ['user_id' => $user->id]);
    $this->assertDatabaseHas('addresses', ['addressable_id' => $customer->user_id, 'addressable_type' => Customer::class]);
    $this->assertDatabaseHas('contacts', ['contactable_id' => $customer->user_id, 'contactable_type' => Customer::class]);
    $this->assertNotEmpty($user->customer->account_number);
  }

  public function test_cannot_create_new_customer_without_permission()
  {
    $customerName = $this->faker->name;
    $customerRecord = factory(Customer::class)->make()->getAttributes();
    $customerRecord['name'] = $customerName;

    $response = $this->actingAs($this->createStaffWithoutPermissions())
      ->post(route('users.customers.store'), $customerRecord);
    $user = User::whereName($customerName)->first();

    $response->assertForbidden();
    $this->assertEmpty($user);
    $this->assertDatabaseMissing('users', ['name' => $customerName]);
  }

  public function test_can_view_customer_details_page()
  {
    $customer = factory(User::class)->state('customer')->create()->customer;

    $response = $this->actingAs($this->createAdmin())
      ->get(route('users.customers.show', $customer));

    $response->assertOk()
      ->assertViewIs('users::customers.show')
      ->assertViewHasAll(['customer', 'customerReceivables']);
  }

  public function test_cannot_view_customer_details_page_without_permission()
  {
    $response = $this->actingAs($this->createStaffWithoutPermissions())
      ->get(route('users.customers.show', $this->createCustomer()));

    $response->assertForbidden();
  }

  public function test_can_view_customer_edit_page()
  {
    $customer = $this->createCustomer();
    $response = $this->actingAs($this->admin)
      ->get(route('users.customers.edit', $customer));

    $response->assertOk()
      ->assertViewIs('users::customers.edit')
      ->assertViewHasAll([
        'customer',
        'priceLevels',
        'preferenceList',
        'customerTypeList',
        'accountAddress',
        'billingAddress',
        'shippingAddress',
        'commissionTypeNames',
        'customerPaymentTermsList',
        'showOtherPaymentTermsInput'
      ])
      ->assertSee($customer->name)
      ->assertSee($customer->account_number);
  }

  public function test_cannot_view_customer_edit_page_without_permissions()
  {
    $response = $this->actingAs($this->createStaffWithoutPermissions())
      ->get(route('users.customers.edit', $this->createCustomer()));

    $response->assertForbidden();
  }

  public function test_can_update_customer_details()
  {
    $customer = $this->createCustomer();
    $user = $customer->user;
    $customerRecord = $customer->getAttributes();
    $customerRecord['name'] = 'John Doe';
    $customerRecord['price_level'] = '2080';

    // Upload tax certificate
//    Storage::fake('media');
//    config()->set('medialibrary.disk_name', 'media');
//    $taxCertificateFile = new UploadedFile(base_path('tests/Files/tax_certificate.pdf'), 'tax_certificate.pdf', 'application/pdf', null, true);
//    $customerRecord['tax_certificate'] = $taxCertificateFile;

    $response = $this->actingAs($this->admin)
      ->put(route('users.customers.update', $customer), $customerRecord);

    $response->assertRedirect(route('users.customers.show', $customer))
      ->assertSessionHasNoErrors();
    $this->assertDatabaseHas($user->getTable(), ['name' => 'John Doe']);
    $this->assertDatabaseHas($customer->getTable(), ['user_id' => $customer->user_id, 'price_level' => '2080']);
//    $this->assertNotEmpty($customer->tax_certificate_url);
//    $this->assertFileExists($customer->getFirstMedia('certificates')->getPath());
  }

  public function test_cannot_update_customer_details_without_permissions()
  {
    $customer = $this->createCustomer();
    $user = $customer->user;
    $customerRecord = $customer->getAttributes();
    $customerRecord['name'] = 'John Doe';
    $customerRecord['price_level'] = '2080';

    $response = $this->actingAs($this->createStaffWithoutPermissions())
      ->put(route('users.customers.update', $customer), $customerRecord);

    $response->assertForbidden();
    $this->assertDatabaseMissing($user->getTable(), ['name' => 'John Doe']);
    $this->assertDatabaseMissing($customer->getTable(), ['user_id' => $customer->user_id, 'price_level' => '2080']);
  }

  public function test_can_soft_delete_customer()
  {
    $customer = $this->createCustomer();

    $response = $this->actingAs($this->admin)
      ->delete(route('users.customers.destroy', $customer));

    $response->assertRedirect(route('users.customers.index'));
    $this->assertSoftDeleted('users', ['id' => $customer->user_id]);
    $this->assertSoftDeleted('customers', ['user_id' => $customer->user_id]);
  }

  public function test_cannot_soft_delete_without_permission()
  {
    $customer = $this->createCustomer();

    $response = $this->actingAs($this->createStaffWithoutPermissions())
      ->delete(route('users.customers.destroy', $customer));

    $response->assertForbidden();
    $this->assertNotSoftDeleted($customer);
    $this->assertNotSoftDeleted($customer->user);
  }
}
