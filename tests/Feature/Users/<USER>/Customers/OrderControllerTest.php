<?php


namespace Tests\Feature\Users\Http\Customers;


use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\Users\Entities\User;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class OrderControllerTest extends TestCase
{
  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, WithFaker, RefreshDatabase;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_can_view_order_create_page()
  {
    $customer = $this->createCustomer();
    $response = $this->actingAs($this->admin)->get(route('users.customers.orders.create', $customer));

    $response->assertViewHasAll([
      'order',
      'customer',
      'itemList',
      'staffList',
      'shippingModeList',
      'defaultShippingAddress',
      'nextOrderNumber',
      'orderRepresentatives',
      'pickupRepresentatives',
      'shippingDateOptions',
      'serialNumberPart',
      'commissionTypes'
    ]);
  }

  public function test_can_create_order()
  {
    $user = factory(User::class)->state('customer')->create();

    $response = $this->actingAs($this->admin)
      ->post(
        route('users.customers.orders.store', $user->customer),
        $this->makeOrderRequest($user->customer)
      );
    $orders = $user->customer->orders;
    $firstOrder = $orders->first->fresh();

    $response->assertSessionHasNoErrors();
    $this->assertEquals(1, $orders->count());
    $this->assertEquals(1, $firstOrder->items->count());

    $showOrderResponse = $this->actingAs($this->admin)
      ->get(route('orderModule.orders.show', $firstOrder->id));
    $showOrderResponse->assertViewIs('orders::orders.show');
    $showOrderResponse->assertSeeText('Order ' . $firstOrder->full_number);
  }
}
