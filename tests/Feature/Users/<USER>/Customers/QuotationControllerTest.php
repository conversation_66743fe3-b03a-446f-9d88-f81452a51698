<?php


namespace Tests\Feature\Users\Http\Customers;


use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\Users\Entities\User;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class QuotationControllerTest extends TestCase
{
  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r, WithFaker, RefreshDatabase;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_can_view_page_to_create_quotation()
  {
    $customer = $this->createCustomer();
    $response = $this->actingAs($this->admin)->get(route('users.customers.quotations.create', $customer));

    $response->assertViewHasAll([
      'quotation',
      'customer',
      'itemList',
      'staffList',
      'nextQuotationNumber',
      'defaultShippingAddress',
      'serialNumberPart'
    ]);
  }

  public function test_can_create_quotation()
  {
    $user = factory(User::class)->state('customer')->create();

    $response = $this->actingAs($this->admin)
      ->post(
        route('users.customers.quotations.store', $user->customer),
        $this->makeQuotationRequest($user->customer)
      );
    $quotations = $user->customer->quotations;
    $firstQuotation = $quotations->first->fresh();

    $response->assertSessionHasNoErrors();
    $this->assertEquals(1, $quotations->count());
    $this->assertEquals(1, $firstQuotation->items->count());

    $showOrderResponse = $this->actingAs($this->admin)
      ->get(route('orderModule.quotations.show', $firstQuotation->id));
    $showOrderResponse->assertViewIs('orders::quotations.show');
    $showOrderResponse->assertSeeText('Quotation #' . $firstQuotation->full_number);
  }
}
