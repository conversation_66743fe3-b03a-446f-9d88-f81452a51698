<?php


namespace Tests\Feature\General\Http\PurchaseOrders;


use App\Http\Livewire\PurchaseOrders\CreatePurchaseOrderComponent;
use App\Http\Livewire\PurchaseOrders\ShowPurchaseOrderComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Entities\PurchaseOrderRequest;
use Tests\Traits\CreatesTestUser;

class PurchaseOrderControllerTest extends \Tests\TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_view_purchase_order_list()
  {
    $this->actingAs($this->admin)
      ->get(route('general.purchase-orders.index'))
      ->assertViewIs('general::purchase-orders.index')
      ->assertViewHasAll(['searchFields', 'searchConditions', 'searchParams'])
      ->assertSee(['Purchase Orders'])
      ->assertStatus(Response::HTTP_OK);
  }

  public function test_cannot_view_create_purchase_order_page_without_specifying_vendor()
  {
    $this->actingAs($this->admin)
      ->get(route('general.purchase-orders.create'))
      ->assertRedirect(route('general.vendors.index'))
      ->assertSessionHas('error', 'Please select a vendor to proceed');

    $this->assertEmpty(PurchaseOrder::all());
  }

  public function test_user_redirected_to_select_from_existing_purchase_order_requests()
  {
    $purchaseOrderRequest = factory(PurchaseOrderRequest::class)->create();
    $vendor = $purchaseOrderRequest->vendor;

    $this->actingAs($this->admin)
      ->get(route('general.purchase-orders.create', [
        'vendor_id' => $vendor->id,
        'check_po_requests' => '1'
      ]))
      ->assertRedirect(route('general.purchase-orders.requested-items.create', [
        'vendor' => $vendor->id
      ]));

    $this->assertEmpty(PurchaseOrder::all());
  }

  public function test_view_create_purchase_order_page()
  {
    $vendor = factory(Provider::class)->create();

    $this->actingAs($this->admin)
      ->get(route('general.purchase-orders.create', [
        'vendor_id' => $vendor->id
      ]))
      ->assertViewIs('general::purchase-orders.create')
      ->assertViewHasAll(['vendor', 'itemListOptions'])
      ->assertSeeLivewire(CreatePurchaseOrderComponent::class);
  }

  public function test_view_purchase_order_details()
  {
    $purchaseOrder = factory(PurchaseOrder::class)->create();

    $this->actingAs($this->admin)
      ->get(route('general.purchase-orders.show', $purchaseOrder))
      ->assertViewIs('general::purchase-orders.show')
      ->assertViewHasAll(['purchaseOrder', 'itemListOptions'])
      ->assertSee(['Purchase Order #' . $purchaseOrder->number, 'Add Purchase Order Item', 'Print PO'])
      ->assertSeeLivewire(ShowPurchaseOrderComponent::class);
  }

  public function test_can_delete_purchase_order()
  {
    $purchaseOrder = factory(PurchaseOrder::class)->create();

    $this->actingAs($this->admin)
      ->delete(route('general.purchase-orders.destroy', $purchaseOrder))
      ->assertRedirect(route('general.purchase-orders.index'))
      ->assertSessionHas('success', 'Purchase order deleted successfully');

    $this->assertSoftDeleted($purchaseOrder);

    foreach($purchaseOrder->purchaseOrderItems as $poItem) {
      $this->assertSoftDeleted($poItem);
    }
  }
}
