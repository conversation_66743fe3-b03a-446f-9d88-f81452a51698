<?php


namespace Tests\Feature\General\Http\PurchaseOrders;


use App\Http\Livewire\PurchaseOrders\CreatePurchaseOrderComponent;
use App\Http\Livewire\PurchaseOrders\ShowPurchaseOrderComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Entities\PurchaseOrderRequest;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;

class PrintBackPurchaseOrderTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_prints_back_purchase_order()
  {
    $purchaseOrder = factory(PurchaseOrder::class)->create([
      'purchase_order_id' => factory(PurchaseOrder::class)->create()->id
    ]);

    \PDF::fake();

    $this->actingAs($this->admin)
      ->get(route('general.purchase-orders.print.back-ordered', $purchaseOrder))
      ->assertSuccessful();

    \PDF::assertViewIs('general::purchase-orders.print-back-order');
    \PDF::assertViewHas('purchaseOrder');
  }
}
