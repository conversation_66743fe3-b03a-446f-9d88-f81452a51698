<?php


namespace Tests\Feature\General\Http\PurchaseOrders;


use App\Http\Livewire\PurchaseOrders\CreatePurchaseOrderComponent;
use App\Http\Livewire\PurchaseOrders\ShowPurchaseOrderComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Entities\PurchaseOrderRequest;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;

class CompletePurchaseOrderTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_marks_purchase_order_as_complete()
  {
    $purchaseOrder = factory(PurchaseOrder::class)->create();

    $this->actingAs($this->admin)
      ->from(route('general.purchase-orders.show', $purchaseOrder))
      ->put(route('general.purchase-orders.complete', $purchaseOrder))
      ->assertRedirect(route('general.purchase-orders.show', $purchaseOrder))
      ->assertSessionHas('success', 'Purchase order completed successfully');

    $purchaseOrder->refresh();

    $this->assertEquals(PurchaseOrder::STATUS_COMPLETED, $purchaseOrder->status);
    $this->assertEquals($this->admin->id, $purchaseOrder->completed_by);
  }
}
