<?php


namespace Tests\Feature\General\Http\PurchaseOrders;


use App\Http\Livewire\PurchaseOrders\CreatePurchaseOrderComponent;
use App\Http\Livewire\PurchaseOrders\ShowPurchaseOrderComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Modules\General\Entities\Provider;
use Modules\General\Entities\PurchaseOrder;
use Modules\General\Entities\PurchaseOrderRequest;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;

class AcknowledgementControllerTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_can_attach_acknowledgement_to_purchase_order()
  {
    $purchaseOrder = factory(PurchaseOrder::class)->create();
    Storage::fake('s3');

    $this->actingAs($this->admin)
      ->from(route('general.purchase-orders.show', $purchaseOrder))
      ->post(
        route('general.purchase-orders.acknowledgement.store', $purchaseOrder),
        [
          'acknowledgement_file' => $file = UploadedFile::fake()->image('ack.pdf')
        ]
      )
      ->assertRedirect(route('general.purchase-orders.show', $purchaseOrder))
      ->assertSessionHas('success', 'Acknowledgement file uploaded successfully');

    $purchaseOrder->refresh();

    $this->assertTrue($purchaseOrder->acknowledgement_uploaded);
    $this->assertNotEmpty($purchaseOrder->acknowledgement_uploaded_at);
    $this->assertNotEmpty($purchaseOrder->acknowledgement_uploaded_by);
  }
}
