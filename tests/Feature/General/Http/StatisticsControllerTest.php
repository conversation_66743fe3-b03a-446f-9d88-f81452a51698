<?php


namespace Tests\Feature\General\Http;


use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;

class StatisticsControllerTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_view_statistics_page()
  {
    $this->actingAs($this->admin)
      ->get(route('general.statistics.index'))
      ->assertViewIs('general::statistics.index')
      ->assertViewHas('dashboard');
  }
}
