<?php


namespace Tests\Feature\Orders\Actions\Orders;


use App\Exceptions\PricingNotCompleted;
use App\Exceptions\RequiresShippingCost;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Modules\Orders\Actions\SendInvoiceAction;
use Modules\Orders\Emails\Orders\InvoiceGenerated;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class SendInvoiceActionTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_send_invoice()
  {
    Mail::fake();
    $this->actingAs($this->admin);
    $invoice = $this->createInvoicedOrder();

    app(SendInvoiceAction::class)->execute($invoice, ['recipients' => ['<EMAIL>']]);

    Mail::assertQueued(InvoiceGenerated::class);
    $this->assertDatabaseHas('activity_logs', [
      'loggable_id' => $invoice->id,
      'description' => $this->admin->name . ' emailed invoice',
    ]);
  }

  public function test_throws_error_when_invoice_pricing_not_completed()
  {
    $this->expectException(PricingNotCompleted::class);
    Mail::fake();
    $invoice = $this->createInvoicedOrder();
    $invoice->items()->save(factory(OrderLine::class)->make(['price' => null]));
    $invoice->refresh();

    app(SendInvoiceAction::class)->execute($invoice, ['recipients' => ['<EMAIL>']]);

    Mail::assertNotQueued(InvoiceGenerated::class);
  }

  public function test_throws_error_when_shipping_cost_required_but_not_provided()
  {
    $this->withoutDeprecationHandling();
    $this->expectException(RequiresShippingCost::class);
    Mail::fake();

    $invoice = $this->createInvoicedOrder(['shipping_via' => Order::STATUS_SHIPPED, 'shipping_cost' => null]);

    app(SendInvoiceAction::class)->execute($invoice, ['recipients' => ['<EMAIL>']]);

    Mail::assertNotQueued(InvoiceGenerated::class);
  }
}
