<?php


namespace Tests\Feature\Orders\Actions\Orders;


use App\Exceptions\Orders\OrderCannotBeCanceled;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Arr;
use Modules\Orders\Actions\Orders\CancelOrderAction;
use Modules\Orders\Actions\Orders\DeleteLineItemAction;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class DeleteLineItemActionTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  /**
   * @var mixed
   */
  protected $admin;
  protected DeleteLineItemAction $action;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
    $this->action = app(DeleteLineItemAction::class);
  }

  public function test_deletes_line_item_from_order()
  {
    $order = $this->createOrder();
    $this->actingAs($this->admin);
    $deletedItem = $order->items->first();

    $this->action->execute($deletedItem);
    $order->refresh();

    $this->assertModelMissing($deletedItem);
  }
}
