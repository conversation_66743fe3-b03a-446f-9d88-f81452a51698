<?php


namespace Tests\Feature\Orders\Actions\Orders;


use App\Exceptions\Orders\OrderCannotBeCanceled;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Notification;
use Modules\Orders\Actions\Orders\CancelOrderAction;
use Modules\Orders\Actions\Orders\UpdateOrderStatusAction;
use Modules\Orders\Entities\Order;
use Modules\Orders\Notifications\OrderSpecialInstruction;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class UpdateOrderStatusActionTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  /**
   * @var mixed
   */
  protected $admin;
  protected UpdateOrderStatusAction $action;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
    $this->action = app(UpdateOrderStatusAction::class);
  }

  public function test_updates_order_status()
  {
    $order = $this->createOrder();
    $this->actingAs($this->admin);

    $this->action->execute($order, [
      'status' => Order::STATUS_BUILDING,
      'note' => $this->faker->sentence
    ]);
    $order->refresh();
    $this->assertEquals(Order::STATUS_BUILDING, $order->status);
    $this->assertDatabaseHas('activity_logs', [
      'loggable_id' => $order->id,
      'description' => $this->admin->name . ' changed order status',
    ]);
  }

  public function test_does_not_update_order_status_with_same_status()
  {
    $order = $this->createOrder();
    $this->actingAs($this->admin);

    $result = $this->action->execute($order, [
      'status' => Order::STATUS_CREATED,
      'note' => $this->faker->sentence
    ]);

    $this->assertFalse($result);
    $this->assertDatabaseMissing('activity_logs', [
      'loggable_id' => $order->id,
      'description' => $this->admin->name . ' changed order status',
    ]);
  }

  public function test_notification_sent_for_special_instructions()
  {
    Notification::fake();
    $this->actingAs($this->admin);
    $customer = $this->createCustomer();
    $options = $customer->options;
    $options['special_instructions'] = $this->faker->sentence;
    $customer->options = $options;
    $customer->save();

    $order = $this->createOrder(['customer_id' => $customer->user_id]);

    $this->action->execute($order, [
      'status' => Order::STATUS_SHIPPED,
      'note' => $this->faker->sentence
    ]);

    Notification::assertSentTo([$this->admin], OrderSpecialInstruction::class);
  }
}
