<?php


namespace Tests\Feature\Orders\Actions\Orders;


use App\Exceptions\Orders\OrderCannotBeCanceled;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Modules\Orders\Actions\InvoiceOrderAction;
use Modules\Orders\Actions\Orders\CancelOrderAction;
use Modules\Orders\Events\Orders\OrderInvoiced;
use Modules\Orders\Notifications\InvoicePrintingInstruction;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class InvoiceOrderActionTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  /**
   * @var mixed
   */
  protected $admin;
  protected InvoiceOrderAction $action;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
    $this->action = app(InvoiceOrderAction::class);
  }

  public function test_cannot_invoice_an_order_twice()
  {
    $this->assertFalse($this->action->execute($this->createInvoicedOrder()));
  }

  public function test_can_invoice_an_order()
  {
    $this->actingAs($this->admin);
    Event::fake();
    $order = $this->createOrder();

    $this->action->execute($order);
    $order->refresh();

    $this->assertTrue($order->invoiced);
    $this->assertNotNull($order->invoiced_at);
    $this->assertNotNull($order->invoiced_by);
    $this->assertGreaterThan(0, $order->customer->invoice_balance);
    $this->assertDatabaseHas('activity_logs', [
      'loggable_id' => $order->id,
      'description' => $this->admin->name . ' ' . 'invoiced this order'
    ]);
    Event::assertDispatched(OrderInvoiced::class);
  }

  public function test_notification_is_sent_when_invoiced_customer_has_printing_instructions()
  {
    $this->actingAs($this->admin);
    Notification::fake();

    $customer = $this->createCustomer();
    $options = $customer->options;

    $options['invoice_printing_instructions'] = $this->faker->sentence;
    $customer->options = $options;
    $customer->save();
    $order = $this->createOrder(['customer_id' => $customer->user_id]);

    $this->action->execute($order);

    Notification::assertSentTo([$this->admin], InvoicePrintingInstruction::class);
  }
}
