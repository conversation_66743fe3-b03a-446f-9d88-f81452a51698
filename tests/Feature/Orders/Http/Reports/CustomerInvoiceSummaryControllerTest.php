<?php


namespace Tests\Feature\Orders\Http\Reports;


use App\Http\Livewire\CustomerInvoiceSummaryReport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class CustomerInvoiceSummaryControllerTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_view_customer_invoice_summary_report()
  {
    $customer = $this->createCustomer();
    $this->createInvoicedOrders(5, ['customer_id' => $customer->user_id, ]);

    $this->actingAs($this->admin)
      ->get(route('orderModule.reports.customerInvoiceSummary.index'))
      ->assertStatus(Response::HTTP_OK)
      ->assertViewIs('orders::reports.invoice-summary.index')
      ->assertSee('Customer Invoice Summary Report')
      ->assertSeeLivewire(CustomerInvoiceSummaryReport::class);
  }
}
