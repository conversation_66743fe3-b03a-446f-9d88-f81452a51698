<?php


namespace Tests\Feature\Orders\Http\Quotations;


use App\Exceptions\Orders\QuotationAlreadyConverted;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Mo<PERSON>y\MockInterface;
use Modules\Orders\Actions\Quotations\ConvertToOrderAction;
use Modules\Orders\Entities\Order;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class ConvertToOrderTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  /**
   * @var mixed
   */
  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_can_convert_quotation_to_order()
  {
    $quotation = $this->createQuotation();

    $response = $this->actingAs($this->admin)
      ->post(route('orderModule.quotations.makeOrder', $quotation));
    $quotation->refresh();

    $response->assertSessionHasNoErrors()
      ->assertRedirect(route('orderModule.orders.show', $quotation->order_id));
  }

  public function test_quotation_already_converted()
  {
    $quotation = $this->createQuotation(['converted' => true]);

    $response = $this->actingAs($this->admin)
      ->from(route('orderModule.quotations.show', $quotation))
      ->post(route('orderModule.quotations.makeOrder', $quotation));
    $quotation->refresh();

    $response->assertRedirect(route('orderModule.quotations.show', $quotation));
    $this->assertNull($quotation->order_id);
  }
}
