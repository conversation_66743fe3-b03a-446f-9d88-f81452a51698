<?php


namespace Tests\Feature\Orders\Http\Orders;


use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class ListArchivedOrdersTest extends TestCase
{
  use RefreshDatabase, CreatesTest<PERSON>ser, WithFaker, HasOrder;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_view_archived_orders_list()
  {
    $this->actingAs($this->admin)
      ->get(route('orderModule.orders.archived.index'))
      ->assertViewHasAll(['searchConditions', 'searchFields', 'searchParams'])
      ->assertViewIs('orders::orders.archived.index')
      ->assertSeeText('Archived Orders');
  }
}
