<?php


namespace Tests\Feature\Orders\Http\Orders;


use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\Orders\Actions\Orders\CancelOrderAction;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class PrintAcknowledgementTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  public function test_prints_acknowledgement()
  {
    \PDF::fake();
    $order = $this->createOrder();
    $admin = $this->createAdmin();

    $this->actingAs($admin)
      ->get(route('orderModule.orders.print.acknowledgement', $order))
      ->assertStatus(200);
    $this->assertDatabaseHas('activity_logs', [
      'loggable_id' => $order->id,
      'description' => "$admin->name printed acknowledgement"
    ]);
  }
}
