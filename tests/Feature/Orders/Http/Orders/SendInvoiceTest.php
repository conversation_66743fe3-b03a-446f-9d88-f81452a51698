<?php


namespace Tests\Feature\Orders\Http\Orders;


use App\Exceptions\PricingNotCompleted;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Modules\Orders\Emails\Orders\InvoiceGenerated;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class SendInvoiceTest extends \Tests\TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_sends_invoice()
  {
    Mail::fake();

    $invoice = $this->createInvoicedOrder();

    $this->actingAs($this->admin)
      ->from(route('orderModule.orders.showInvoice', $invoice))
      ->post(route('orderModule.orders.sendInvoice', $invoice), [
        'recipients' => '<EMAIL>',
        'body' => $this->faker->sentence
      ])
      ->assertSessionHasNoErrors()
      ->assertRedirect(route('orderModule.orders.showInvoice', $invoice));

    Mail::assertQueued(InvoiceGenerated::class);
  }

  public function test_email_not_sent_when_item_pricing_not_completed()
  {
    Mail::fake();

    $invoice = $this->createInvoicedOrder();
    $invoice->items()->save(factory(OrderLine::class)->make(['price' => null]));
    $invoice->refresh();

    $this->actingAs($this->admin)
      ->from(route('orderModule.orders.showInvoice', $invoice))
      ->post(route('orderModule.orders.sendInvoice', $invoice), [
        'recipients' => '<EMAIL>',
        'body' => $this->faker->sentence
      ])
      ->assertRedirect(route('orderModule.orders.showInvoice', $invoice));

    Mail::assertNotQueued(InvoiceGenerated::class);
  }

  public function test_email_not_sent_when_when_shipping_cost_is_required()
  {
    Mail::fake();

    $invoice = $this->createInvoicedOrder(['shipping_via' => Order::STATUS_SHIPPED, 'shipping_cost' => null]);

    $this->actingAs($this->admin)
      ->from(route('orderModule.orders.showInvoice', $invoice))
      ->post(route('orderModule.orders.sendInvoice', $invoice), [
        'recipients' => '<EMAIL>',
        'body' => $this->faker->sentence
      ])
      ->assertRedirect(route('orderModule.orders.showInvoice', $invoice));

    Mail::assertNotQueued(InvoiceGenerated::class);
  }
}
