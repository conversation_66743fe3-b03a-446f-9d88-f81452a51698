<?php

namespace Tests\Feature\Orders\Http\Orders;

use Modules\Orders\Entities\Order;
use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class DeliveryControllerTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  protected $admin;

  public function setUp(): void
  {
    parent::setUp();

    $this->admin = $this->createAdmin();
  }

  public function test_can_view_delivery_receipt()
  {
    $order = $this->createOrder();

    $response = $this->actingAs($this->admin)
      ->get(route('orderModule.orders.showDelivery', $order));

    $response->assertOk()
      ->assertViewIs('orders::orders.delivery')
      ->assertViewHasAll(['order', 'pricingCompleted'])
      ->assertSeeText('Delivery Receipt');

    foreach($order->items as $item) {
      $response->assertSeeText($item->code);
    }
  }

  public function test_does_not_print_when_shipping_via_is_required()
  {
    $order = $this->createOrder([
      'shipping_via' => Order::SHIP_VIA_CALL,
      'customer_pickup_by' => null
    ]);

    $response = $this->actingAs($this->admin)
      ->from(route('orderModule.orders.showDelivery', $order))
      ->get(route('orderModule.orders.downloadDeliveryReceipt', ['order' => $order, 'price' => true]));

    $response->assertRedirect(route('orderModule.orders.showDelivery', $order));
  }

  public function test_downloads_delivery_receipt()
  {
    $order = $this->createInvoicedOrder();

    $response = $this->actingAs($this->admin)
      ->get(route('orderModule.orders.downloadDeliveryReceipt', ['order' => $order, 'price' => true]));
    $order->refresh();

    $response->assertStatus(200);
    $this->assertTrue($order->delivered());
    $this->assertNotNull($order->delivered_at);
    $this->assertEquals(Order::STATUS_DELIVERED, $order->status);
  }
}
