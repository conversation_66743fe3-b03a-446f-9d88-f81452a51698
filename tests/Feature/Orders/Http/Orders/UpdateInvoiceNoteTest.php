<?php


namespace Tests\Feature\Orders\Http\Orders;


use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Tests\Traits\CreatesTestUser;
use Tests\Traits\HasOrder;

class UpdateInvoiceNoteTest extends TestCase
{
  use RefreshDatabase, CreatesTestUser, WithFaker, HasOrder;

  public function test_can_update_invoice_note()
  {
    $order = $this->createInvoicedOrder();

    $this->actingAs($this->createAdmin())
      ->from(route('orderModule.orders.showInvoice', $order))
      ->put(route('orderModule.orders.updateInvoice', $order), [
        'notes' => 'Send to me'
      ])
      ->assertRedirect(route('orderModule.orders.showInvoice', $order))
      ->assertSessionHas('success', 'Invoice note updated successfully.');
    $order->refresh();

    $this->assertEquals('Send to me', $order->invoice_notes);
  }

  public function test_can_update_invoice_note_via_json()
  {
    $order = $this->createInvoicedOrder();

    $this->actingAs($this->createAdmin())
      ->from(route('orderModule.orders.showInvoice', $order))
      ->putJson(route('orderModule.orders.updateInvoice', $order), [
        'notes' => 'Send to me'
      ])
      ->assertJsonFragment(['message' => 'Invoice note updated successfully.'])
      ->assertStatus(201);

    $order->refresh();

    $this->assertEquals('Send to me', $order->invoice_notes);
  }
}
