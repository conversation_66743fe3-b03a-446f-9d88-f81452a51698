<?php


namespace Tests\Traits;


use Modules\General\Entities\PurchaseOrder;

trait HasPurchaseOrder
{
  public function createPurchaseOrder(array $attributes = [])
  {
    return factory(PurchaseOrder::class)->create($attributes);
  }

  public function createPurchaseOrders(int $count = 2, array $attributes = [])
  {
    return factory(PurchaseOrder::class, $count)->create($attributes);
  }

  public function createReceivedPurchaseOrder(array $attributes = [])
  {
    return $this->createPurchaseOrderWithState('received', $attributes);
  }

  public function createPurchaseOrderWithState(string $state, $attributes = [])
  {
    return factory(PurchaseOrder::class)->state($state)->create($attributes);
  }
}
