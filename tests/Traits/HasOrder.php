<?php


namespace Tests\Traits;


use Modules\Inventory\Entities\Item;
use Modules\Orders\Entities\Order;
use Modules\Orders\Entities\OrderLine;
use Modules\Orders\Entities\Quotation;
use Modules\Users\Entities\Address;
use Modules\Users\Entities\Customer;

trait HasOrder
{
  public function createOrder(array $attributes = [])
  {
    return factory(Order::class)->create($attributes);
  }

  public function createOrders(int $count = 2, array $attributes = [])
  {
    return factory(Order::class, $count)->create($attributes);
  }

  /**
   * @param array $attributes
   * @return \Illuminate\Database\Eloquent\Model|Quotation
   */
  public function createQuotation(array $attributes = [])
  {
    return factory(Quotation::class)->create($attributes);
  }

  /**
   * @param int $count
   * @param array $attributes
   * @return \Illuminate\Database\Eloquent\Collection
   */
  public function createQuotations(int $count = 2, array $attributes = []): \Illuminate\Database\Eloquent\Collection
  {
    return factory(Quotation::class, $count)->create($attributes);
  }

  public function createInvoicedOrder(array $attributes = [])
  {
    return $this->createOrderWithState('invoiced', $attributes);
  }

  public function createInvoicedOrders(int $count = 2, array $attributes = [])
  {
    return $this->createOrdersWithState('invoiced', $count, $attributes);
  }

  public function createOrderWithState(string $state, $attributes = [])
  {
    return factory(Order::class)->state($state)->create($attributes);
  }

  public function createOrdersWithState(string $state, int $count = 2, $attributes = [])
  {
    return factory(Order::class, $count)->state($state)->create($attributes);
  }

  public function createOrderLine(array $attributes = [])
  {
    return factory(OrderLine::class)->create($attributes);
  }

  public function createOrderLineWithState(string $state, array $attributes = [])
  {
    return factory(OrderLine::class)->state($state)->create($attributes);
  }

  public function createOrderRequest(array $attributes = []): array
  {
    return [];
  }

  /**
   * @param Customer $customer
   * @return array
   */
  protected function makeOrderRequest(Customer $customer): array
  {
    $itemOne = factory(Item::class)->create();
    $billingAddress = factory(Address::class)->state('billing')->create([
      'addressable_id' => $customer->user_id,
      'addressable_type' => Customer::class,
    ]);
    $shippingAddress = factory(Address::class)->state('shipping')->create([
      'addressable_id' => $customer->user_id,
      'addressable_type' => Customer::class,
    ]);

    return [
      'order_date' => now()->toDateString(),
      'purchase_order_number' => $this->faker->word,
      'salesperson' => $this->faker->name,
      'shipping_date' => $this->faker->dateTimeThisMonth,
      'telephone' => $this->faker->phoneNumber,
      'billing_company' => $billingAddress->name,
      'billing_address' => $billingAddress->full_address,
      'shipping_phone' => $this->faker->phoneNumber,
      'shipping_company' => $shippingAddress->name,
      'shipping_address' => $shippingAddress->full_address,
      'ship_via' => $this->faker->randomElement(['Customer Pickup', 'Our Truck', 'Shipping Company', 'Dats Trucking', 'Link Trucking', 'UPS', 'Fed Ex', 'USPS', 'Rental']),
      'shipping_note' => $this->faker->words(3, true),
      'shipping_tracking_number' => $this->faker->randomNumber(8),
      'shop_notes' => $this->faker->sentence,
      'private_notes' => $this->faker->sentence,
      'item_id' => [
        $itemOne->id
      ],
      'code' => [
        $itemOne->code
      ],
      'description' => [
        $itemOne->description
      ],
      'quantity' => [
        10
      ],
      'cost' => [
        $itemOne->cost
      ],
      'price' => [
        $itemOne->price
      ],
      'weight' => [
        $itemOne->weight
      ],
      'build_instructions' => [
        $itemOne->build_instructions
      ],
      'commission_type' => [
        null
      ]
    ];
  }

  /**
   * @param Customer $customer
   * @return array
   */
  protected function makeQuotationRequest(Customer $customer): array
  {
    $itemOne = factory(Item::class)->create();
    $billingAddress = factory(Address::class)
      ->state('billing')
      ->create([
        'addressable_id' => $customer->user_id,
        'addressable_type' => Customer::class,
      ]);
    $shippingAddress = factory(Address::class)
      ->state('shipping')
      ->create([
        'addressable_id' => $customer->user_id,
        'addressable_type' => Customer::class,
      ]);

    return [
      'quotation_date' => now()->toDateString(),
      'expiry_date' => now()->addMonth()->toDateString(),
      'purchase_order_number' => $this->faker->word,
      'salesperson' => $this->faker->name,
      'lead_time' => $this->faker->sentence,
      'telephone' => $this->faker->phoneNumber,
      'billing_company' => $billingAddress->name,
      'billing_address' => $billingAddress->full_address,
      'shipping_phone' => $this->faker->phoneNumber,
      'shipping_company' => $shippingAddress->name,
      'shipping_address' => $shippingAddress->full_address,
      'shop_notes' => $this->faker->sentence,
      'private_notes' => $this->faker->sentence,
      'show_total' => mt_rand(0, 1),
      'freight_included' => mt_rand(0, 1),
      'item_id' => [
        $itemOne->id
      ],
      'number' => [
        $itemOne->number,
      ],
      'code' => [
        $itemOne->code
      ],
      'location' => [
        null
      ],
      'description' => [
        $itemOne->description
      ],
      'quantity' => [
        10
      ],
      'cost' => [
        $itemOne->cost
      ],
      'price' => [
        $itemOne->price
      ],
      'weight' => [
        $itemOne->weight
      ],
      'build_instructions' => [
        $itemOne->build_instructions
      ],
      'commission_type' => [
        null
      ]
    ];
  }
}
